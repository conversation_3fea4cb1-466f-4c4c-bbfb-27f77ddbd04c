<!-- src/lib/components/features/settings/Settings.svelte -->
<script lang="ts">
  import { Bell, Clock,Globe, Settings, Shield } from 'lucide-svelte';
  import { onMount } from 'svelte';

  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Separator } from '$lib/components/ui/separator';
  import { Switch } from '$lib/components/ui/switch';
  import { currentTimezone,timezoneStore } from '$lib/stores/features/timezone';

  import TimezoneSelector from './TimezoneSelector.svelte';
  import CacheManagement from './CacheManagement.svelte';

  // 设置状态
  let notificationsEnabled = $state(true);
  let shareAnonymousData = $state(false);

  // 时区状态
  const timezone = $derived($currentTimezone);

  /**
   * 处理通知设置变更
   */
  function handleNotificationChange(enabled: boolean) {
    notificationsEnabled = enabled;
    // 这里可以添加保存到 localStorage 或发送到服务器的逻辑
    console.log('通知设置已更新:', enabled);
  }

  /**
   * 处理隐私设置变更
   */
  function handlePrivacyChange(enabled: boolean) {
    shareAnonymousData = enabled;
    // 这里可以添加保存到 localStorage 或发送到服务器的逻辑
    console.log('隐私设置已更新:', enabled);
  }

  /**
   * 组件挂载时初始化设置
   */
  onMount(() => {
    // 从 localStorage 加载设置
    try {
      const savedNotifications = localStorage.getItem('notifications-enabled');
      const savedPrivacy = localStorage.getItem('share-anonymous-data');

      if (savedNotifications !== null) {
        notificationsEnabled = JSON.parse(savedNotifications);
      }

      if (savedPrivacy !== null) {
        shareAnonymousData = JSON.parse(savedPrivacy);
      }
    } catch (error) {
      console.warn('Failed to load settings from localStorage:', error);
    }

    // 初始化时区设置
    timezoneStore.initialize();
  });

  // 监听设置变化并保存到 localStorage
  $effect(() => {
    try {
      localStorage.setItem('notifications-enabled', JSON.stringify(notificationsEnabled));
    } catch (error) {
      console.warn('Failed to save notification settings:', error);
    }
  });

  $effect(() => {
    try {
      localStorage.setItem('share-anonymous-data', JSON.stringify(shareAnonymousData));
    } catch (error) {
      console.warn('Failed to save privacy settings:', error);
    }
  });
</script>

<div class="space-y-6">
  <!-- 页面标题 -->
  <div class="flex items-center gap-3">
    <Settings class="text-muted-foreground h-8 w-8" />
    <div>
      <h1 class="text-3xl font-bold tracking-tight">设置</h1>
      <p class="text-muted-foreground">管理您的应用程序偏好设置和配置</p>
    </div>
  </div>

  <!-- 通用设置 -->
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Bell class="h-5 w-5" />
        通用设置
      </CardTitle>
      <p class="text-muted-foreground text-sm">配置应用程序的基本行为和通知偏好</p>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 通知设置 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <label
            for="notifications-switch"
            class="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            启用通知
          </label>
          <p class="text-muted-foreground text-xs">接收重要更新和系统通知</p>
        </div>
        <Switch
          id="notifications-switch"
          bind:checked={notificationsEnabled}
          onCheckedChange={handleNotificationChange}
        />
      </div>
    </CardContent>
  </Card>

  <!-- 时区设置 -->
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Globe class="h-5 w-5" />
        时区设置
      </CardTitle>
      <p class="text-muted-foreground text-sm">设置您的首选时区，影响所有时间显示</p>
    </CardHeader>
    <CardContent>
      <TimezoneSelector showSearch={true} commonOnly={false} />

      <!-- 时区设置说明 -->
      <div class="bg-muted/50 mt-4 rounded-lg p-3">
        <div class="flex items-start gap-2">
          <Clock class="text-muted-foreground mt-0.5 h-4 w-4" />
          <div class="space-y-1">
            <p class="text-sm font-medium">时区设置说明</p>
            <ul class="text-muted-foreground space-y-1 text-xs">
              <li>• 时区设置将影响所有图表和数据的时间显示</li>
              <li>• 默认使用浏览器检测到的本地时区</li>
              <li>• 设置会自动保存并在下次访问时恢复</li>
              <li>• 当前时区: {timezone}</li>
            </ul>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- 隐私设置 -->
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Shield class="h-5 w-5" />
        隐私设置
      </CardTitle>
      <p class="text-muted-foreground text-sm">管理您的数据隐私和分享偏好</p>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 匿名数据分享 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <label
            for="privacy-switch"
            class="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            分享匿名数据
          </label>
          <p class="text-muted-foreground text-xs">帮助我们改进产品，不包含个人身份信息</p>
        </div>
        <Switch
          id="privacy-switch"
          bind:checked={shareAnonymousData}
          onCheckedChange={handlePrivacyChange}
        />
      </div>
    </CardContent>
  </Card>

  <!-- 缓存管理 -->
  <CacheManagement />
</div>
