<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  import CurrencyIcon from '$lib/components/features/CurrencyIcon.svelte';

  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    formatFundingRate,
    formatTimeDiff,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取极端资金费率相关参数
  const base = getParameterValue(event, 'base') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const fundingRate = getParameterValue(event, 'fundingRate') as number;
  const dailyFundingRate = getParameterValue(event, 'dailyFundingRate') as number;

  // 判断资金费率方向
  const isPositive = fundingRate > 0;
  const rateDirection = isPositive ? '⬆️' : '⬇️';

  // 格式化交易对显示
  const pairDisplay = pair ? pair.split('/')[1] || 'USDT' : 'USDT';

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3">
    <!-- 水平布局：图标 + 内容，参考 FundingRateSwitchCard 样式 -->
    <div class="flex items-center space-x-3">
      <!-- 左侧：货币图标 -->
      <div class="flex-shrink-0">
        <CurrencyIcon
          currencyId={event.currency}
          symbol={base}
          size="size-8"
          class="border border-border"
        />
      </div>

      <!-- 中间：主要内容 -->
      <div class="flex-1 min-w-0">
        <!-- 第一行：状态标题 -->
        <div class="flex items-center space-x-2 mb-1">
          <span class="text-foreground font-medium text-sm">
            资金费率达到
          </span>
          <span class="text-muted-foreground text-sm">
            {base || '未知'}/{pairDisplay}
          </span>
          <span class="text-muted-foreground text-xs">
            • {formatTimeDiff(event.date)}
          </span>
        </div>

        <!-- 第二行：资金费率状态描述 -->
        <div class="text-sm text-foreground leading-relaxed flex items-center flex-wrap gap-1">
          <Badge
            variant={isPositive ? 'default' : 'destructive'}
            class={`text-xs px-1.5 py-0.5 ${isPositive ? 'bg-green-700 text-white border-green-700 dark:bg-green-600 dark:text-white dark:border-green-600' : ''}`}
          >
            {rateDirection} {formatFundingRate(fundingRate)}
          </Badge>
          <span>每小时</span>
          {#if dailyFundingRate}
            <span class="text-muted-foreground">
              ({formatFundingRate(dailyFundingRate)} 每日)
            </span>
          {/if}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
