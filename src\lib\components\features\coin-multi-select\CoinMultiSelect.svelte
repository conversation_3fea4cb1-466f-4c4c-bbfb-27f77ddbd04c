<script lang="ts">
  import { ChevronDown, ChevronUp,X } from 'lucide-svelte';
  import { onMount } from 'svelte';

  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { coinSearchDataService } from '$lib/services/data/coinSearch';
  import type { CoinOption } from '$lib/types';

  interface Props {
    selectedCoins?: string[];
    placeholder?: string;
    disabled?: boolean;
    class?: string;
    maxSelections?: number;
    onSelectionChange?: (selectedCoins: string[]) => void;
    onCoinAdd?: (coin: CoinOption) => void;
    onCoinRemove?: (coinSymbol: string) => void;
  }

  let {
    selectedCoins = $bindable([]),
    placeholder = '请选择币种',
    disabled = false,
    class: className = '',
    maxSelections = 10,
    onSelectionChange,
    onCoinAdd,
    onCoinRemove,
  }: Props = $props();

  let inputElement: HTMLInputElement;
  let containerRef = $state<HTMLDivElement | undefined>(undefined);
  let isOpen = $state(false);
  let isLoading = $state(false);
  let options = $state<CoinOption[]>([]);
  let searchQuery = $state('');
  let selectedIndex = $state(-1);
  let searchTimeout: ReturnType<typeof setTimeout>;

  // 获取选中币种的详细信息
  let selectedCoinOptions = $state<CoinOption[]>([]);

  // 防抖搜索
  async function handleSearch(query: string) {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    searchTimeout = setTimeout(async () => {
      isLoading = true;
      try {
        const results = await coinSearchDataService.searchCoins(query, 10);
        // 过滤掉已选中的币种
        options = results.filter((option) => !selectedCoins.includes(option.value));
        selectedIndex = -1;
        isOpen = options.length > 0;
      } catch (error) {
        console.error('搜索币种失败:', error);
        options = [];
        isOpen = false;
      } finally {
        isLoading = false;
      }
    }, 300);
  }

  // 处理输入变化
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = target.value;
    searchQuery = newValue;

    if (newValue.trim()) {
      handleSearch(newValue);
    } else {
      // 空输入时显示热门币种（排除已选中的）
      loadPopularCoins();
    }
  }

  // 加载热门币种
  async function loadPopularCoins() {
    isLoading = true;
    try {
      const results = await coinSearchDataService.getPopularCoins(10);
      // 过滤掉已选中的币种
      options = results.filter((option) => !selectedCoins.includes(option.value));
      selectedIndex = -1;
      isOpen = true;
    } catch (error) {
      console.error('加载热门币种失败:', error);
      options = [];
      isOpen = false;
    } finally {
      isLoading = false;
    }
  }

  // 处理选项选择
  function handleSelect(option: CoinOption) {
    if (selectedCoins.length >= maxSelections) {
      return; // 达到最大选择数量
    }

    const newSelectedCoins = [...selectedCoins, option.value];
    selectedCoins = newSelectedCoins;

    // 添加到选中币种详细信息
    selectedCoinOptions = [...selectedCoinOptions, option];

    // 清空搜索
    searchQuery = '';
    isOpen = false;
    selectedIndex = -1;

    // 重新加载选项（排除新选中的币种）
    if (searchQuery.trim()) {
      handleSearch(searchQuery);
    } else {
      loadPopularCoins();
    }

    if (onCoinAdd) {
      onCoinAdd(option);
    }
    if (onSelectionChange) {
      onSelectionChange(newSelectedCoins);
    }
  }

  // 移除选中的币种
  function removeCoin(coinSymbol: string) {
    const newSelectedCoins = selectedCoins.filter((coin) => coin !== coinSymbol);
    selectedCoins = newSelectedCoins;

    // 从选中币种详细信息中移除
    selectedCoinOptions = selectedCoinOptions.filter((option) => option.value !== coinSymbol);

    // 重新加载选项
    if (searchQuery.trim()) {
      handleSearch(searchQuery);
    } else {
      loadPopularCoins();
    }

    if (onCoinRemove) {
      onCoinRemove(coinSymbol);
    }
    if (onSelectionChange) {
      onSelectionChange(newSelectedCoins);
    }
  }

  // 处理键盘导航
  function handleKeydown(event: KeyboardEvent) {
    if (!isOpen || options.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        selectedIndex = selectedIndex < options.length - 1 ? selectedIndex + 1 : 0;
        break;
      case 'ArrowUp':
        event.preventDefault();
        selectedIndex = selectedIndex > 0 ? selectedIndex - 1 : options.length - 1;
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < options.length) {
          handleSelect(options[selectedIndex]);
        }
        break;
      case 'Escape':
        event.preventDefault();
        isOpen = false;
        selectedIndex = -1;
        break;
    }
  }

  // 处理输入框聚焦
  function handleFocus() {
    if (searchQuery.trim()) {
      handleSearch(searchQuery);
    } else {
      loadPopularCoins();
    }
  }

  // 切换下拉列表
  function toggleDropdown() {
    if (isOpen) {
      isOpen = false;
    } else {
      handleFocus();
    }
  }

  // 清空所有选择
  function clearAll() {
    selectedCoins = [];
    selectedCoinOptions = [];
    searchQuery = '';
    isOpen = false;
    selectedIndex = -1;

    if (onSelectionChange) {
      onSelectionChange([]);
    }

    inputElement?.focus();
  }

  // 处理外部点击
  function handleClickOutside(event: MouseEvent) {
    if (!containerRef || !isOpen) return;

    const target = event.target as Node;
    if (!containerRef.contains(target)) {
      isOpen = false;
      selectedIndex = -1;
    }
  }

  // 组件挂载时清理定时器和添加事件监听器
  onMount(() => {
    const handleClick = (event: MouseEvent) => handleClickOutside(event);

    document.addEventListener('click', handleClick);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
      document.removeEventListener('click', handleClick);
    };
  });

  // 同步外部 selectedCoins 变化
  $effect(() => {
    // 当外部 selectedCoins 变化时，重新加载选项
    if (searchQuery.trim()) {
      handleSearch(searchQuery);
    } else if (isOpen) {
      loadPopularCoins();
    }
  });
</script>

<div bind:this={containerRef} class="relative {className}">
  <!-- 搜索输入框容器 -->
  <div
    class="border-input bg-background ring-offset-background focus-within:ring-ring min-h-[2.25rem] w-full rounded-md border px-3 py-2 text-sm focus-within:ring-2 focus-within:ring-offset-2"
  >
    <!-- 选中的币种标签和输入框 -->
    <div class="flex flex-wrap items-center gap-1">
      {#each selectedCoinOptions as coin}
        <Badge variant="secondary" class="flex h-6 items-center gap-1 px-2 py-0 text-xs">
          <img src={coin.image} alt={coin.symbol} class="h-3 w-3 rounded-full" loading="lazy" />
          <span>{coin.symbol}</span>
          <Button
            variant="ghost"
            size="sm"
            class="ml-1 h-3 w-3 p-0 hover:bg-transparent"
            onclick={() => removeCoin(coin.value)}
          >
            <X class="h-2.5 w-2.5" />
          </Button>
        </Badge>
      {/each}

      <!-- 输入框 -->
      <div class="relative min-w-[120px] flex-1">
        <input
          bind:this={inputElement}
          bind:value={searchQuery}
          placeholder={selectedCoins.length > 0 ? '' : placeholder}
          {disabled}
          class="placeholder:text-muted-foreground w-full border-0 bg-transparent text-sm outline-none"
          oninput={handleInput}
          onfocus={handleFocus}
          onkeydown={handleKeydown}
        />
      </div>

      <!-- 右侧按钮区域 -->
      <div class="flex items-center gap-1">
        {#if selectedCoins.length > 0}
          <Button
            variant="ghost"
            size="sm"
            class="text-muted-foreground hover:text-foreground h-5 px-1 text-xs"
            onclick={clearAll}
          >
            清空
          </Button>
        {/if}
        <Button variant="ghost" size="sm" class="h-5 w-5 p-0" onclick={toggleDropdown}>
          {#if isOpen}
            <ChevronUp class="h-3 w-3" />
          {:else}
            <ChevronDown class="h-3 w-3" />
          {/if}
        </Button>
      </div>
    </div>
  </div>

  <!-- 下拉选项列表 -->
  {#if isOpen}
    <div
      class="bg-popover text-popover-foreground absolute z-50 mt-1 w-full rounded-md border p-0 shadow-md outline-none"
    >
      <div class="max-h-60 overflow-y-auto">
        {#if selectedCoins.length >= maxSelections}
          <div class="text-muted-foreground p-4 text-center text-sm">
            最多只能选择 {maxSelections} 个币种
          </div>
        {:else if isLoading}
          <div class="space-y-2 p-2">
            {#each Array(5) as _}
              <div class="flex items-center space-x-3 p-2">
                <Skeleton class="h-6 w-6 rounded-full" />
                <div class="flex-1 space-y-1">
                  <Skeleton class="h-4 w-3/4" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
              </div>
            {/each}
          </div>
        {:else if options.length > 0}
          <div class="p-1">
            {#each options as option, index}
              <button
                class="hover:bg-accent hover:text-accent-foreground flex w-full items-center space-x-3 rounded-sm px-3 py-2 text-left text-sm {selectedIndex ===
                index
                  ? 'bg-accent text-accent-foreground'
                  : ''}"
                onclick={() => handleSelect(option)}
              >
                <img
                  src={option.image}
                  alt={option.symbol}
                  class="h-5 w-5 rounded-full"
                  loading="lazy"
                />
                <div class="min-w-0 flex-1">
                  <div class="truncate font-medium">{option.label}</div>
                  <div class="text-muted-foreground text-xs">{option.symbol}</div>
                </div>
              </button>
            {/each}
          </div>
        {:else}
          <div class="text-muted-foreground p-4 text-center text-sm">未找到相关币种</div>
        {/if}
      </div>
    </div>
  {/if}
</div>
