<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  import CurrencyIcon from '$lib/components/features/CurrencyIcon.svelte';
  import { ArrowUp, ArrowDown } from 'lucide-svelte';

  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    formatUsdAmount,
    formatAmount,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取交易所转账相关参数
  const base = getParameterValue(event, 'base') as string;
  const exchangeSide = getParameterValue(event, 'exchangeSide') as number;
  const amount = getParameterValue(event, 'amount') as number;
  const amountUsd = getParameterValue(event, 'amountUsd') as number;
  const chain = getParameterValue(event, 'chain') as string;
  const fromLabel = getParameterValue(event, 'fromLabel') as string;
  const toLabel = getParameterValue(event, 'toLabel') as string;
  const fromName = getParameterValue(event, 'fromName') as string;
  const toName = getParameterValue(event, 'toName') as string;
  const toAddress = getParameterValue(event, 'toAddress') as string;

  // 获取转账方向和显示信息
  const isWithdrawal = exchangeSide === 2;
  const transferDirection = isWithdrawal ? 'withdrawn' : 'deposited';

  // 格式化金额显示
  const formattedAmount = formatAmount(amount);
  const formattedUsdAmount = formatUsdAmount(amountUsd);

  // 获取来源和目标信息
  const fromInfo = fromName || fromLabel || '未知来源';
  const toInfo = toName || toLabel || '未知目标';

  // 截断地址显示
  function truncateAddress(address: string, length: number = 8): string {
    if (!address) return '';
    if (address.length <= length * 2) return address;
    return `${address.slice(0, length)}...${address.slice(-length)}`;
  }

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3">
    <!-- 主要信息行：参考附图的水平布局 -->
    <div class="flex items-center gap-3">
      <!-- 左侧：货币图标 -->
      <div class="flex-shrink-0">
        <CurrencyIcon
          currencyId={event.currency}
          symbol={base}
          size="size-8"
          class="border border-border"
        />
      </div>

      <!-- 中间：金额和转账信息 -->
      <div class="flex-1 min-w-0">
        <!-- 金额行 -->
        <div class="flex items-center gap-2 mb-1">
          <!-- 转账方向图标 -->
          <div class="flex-shrink-0">
            {#if isWithdrawal}
              <ArrowUp class="size-4 text-green-500" />
            {:else}
              <ArrowDown class="size-4 text-blue-500" />
            {/if}
          </div>

          <!-- 金额信息 -->
          <div class="flex items-center gap-1 text-sm font-medium">
            <span class="text-foreground">
              {formattedAmount} {base}
            </span>
            <span class="text-muted-foreground">
              ({formattedUsdAmount})
            </span>
            <span class="text-muted-foreground">
              {transferDirection}
            </span>
          </div>
        </div>

        <!-- 转账路径信息 -->
        <div class="flex items-center gap-2 text-xs text-muted-foreground">
          <span>From</span>
          <span class="font-medium text-foreground">
            {fromInfo}
          </span>

          {#if toAddress && toAddress !== toInfo}
            <span>To</span>
            <span class="font-mono text-foreground" title={toAddress}>
              {truncateAddress(toAddress, 6)}
            </span>
          {:else if toInfo !== fromInfo}
            <span>To</span>
            <span class="font-medium text-foreground">
              {toInfo}
            </span>
          {/if}
        </div>


      </div>
    </div>
  </CardContent>
</Card>
