<script lang="ts">
  import { Card, CardContent } from '$lib/components/ui/card';
  import CurrencyIcon from '$lib/components/features/CurrencyIcon.svelte';
  import { ArrowUp, ArrowDown } from 'lucide-svelte';

  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    formatUsdAmount,
    formatAmount,
    type BaseEventCardProps
  } from '../utils';
  import { cn } from '$lib/utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取交易所转账相关参数
  const base = getParameterValue(event, 'base') as string;
  const exchangeSide = getParameterValue(event, 'exchangeSide') as number;
  const amount = getParameterValue(event, 'amount') as number;
  const amountUsd = getParameterValue(event, 'amountUsd') as number;
  const fromLabel = getParameterValue(event, 'fromLabel') as string;
  const toLabel = getParameterValue(event, 'toLabel') as string;
  const fromName = getParameterValue(event, 'fromName') as string;
  const toName = getParameterValue(event, 'toName') as string;
  const toAddress = getParameterValue(event, 'toAddress') as string;

  // 获取转账方向和显示信息
  const isWithdrawal = exchangeSide === 2;
  const transferDirection = isWithdrawal ? 'withdrawn' : 'deposited';

  // 格式化金额显示
  const formattedAmount = formatAmount(amount);
  const formattedUsdAmount = formatUsdAmount(amountUsd);

  // 获取来源和目标信息
  const fromInfo = fromName || fromLabel || '未知来源';
  const toInfo = toName || toLabel || '未知目标';

  // 截断地址显示
  function truncateAddress(address: string, length: number = 8): string {
    if (!address) return '';
    if (address.length <= length * 2) return address;
    return `${address.slice(0, length)}...${address.slice(-length)}`;
  }

  // 卡片样式 - 使用 cn 函数优化
  const cardClasses = cn(
    // 基础样式
    "w-full transition-all duration-200",
    // 高亮状态
    isHighlighted && "ring-2 ring-primary border-primary/50 shadow-lg",
    // 默认悬停效果
    !isHighlighted && "hover:shadow-md",
    // 可点击状态
    onCardClick && "cursor-pointer"
  );

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class={cn("p-2 md:p-3")}>
    <!-- 响应式布局：使用更小的断点控制布局 -->
    <div class={cn(
      "flex gap-2 md:gap-3",
      "flex-col md:items-start"
    )}>
      <!-- 货币图标：窄屏幕在上方，宽屏幕在左侧 -->
      <div class="self-start flex-shrink-0">
        <CurrencyIcon
          currencyId={event.currency}
          symbol={base}
          size="size-6 md:size-8"
          class="border border-border"
        />
      </div>

      <!-- 金额和转账信息 -->
      <div class="min-w-0 flex-1">
        <!-- 金额行 -->
        <div class={cn(
          "flex items-start mb-1",
          "gap-1 md:gap-2"
        )}>
          <!-- 转账方向图标 -->
          <div class="mt-0.5 flex-shrink-0">
            {#if isWithdrawal}
              <ArrowUp class={cn("size-3 md:size-4", "text-emerald-500")} />
            {:else}
              <ArrowDown class={cn("size-3 md:size-4", "text-blue-500")} />
            {/if}
          </div>

          <!-- 金额信息 - 响应式换行 -->
          <div class={cn(
            "flex flex-wrap items-center gap-1 min-w-0",
            "text-xs md:text-sm font-medium"
          )}>
            <span class="whitespace-nowrap text-foreground">
              {formattedAmount} {base}
            </span>
            <span class="whitespace-nowrap text-muted-foreground">
              ({formattedUsdAmount})
            </span>
            <span class="whitespace-nowrap text-muted-foreground">
              {transferDirection}
            </span>
          </div>
        </div>

        <!-- 转账路径信息 - 响应式布局 -->
        <div class={cn(
          "space-y-0.5 flex flex-wrap items-center md:gap-1 md:space-y-0",
          "text-xs text-muted-foreground"
        )}>
          <!-- From 行 -->
          <div class="flex items-center gap-1 md:contents">
            <span class="whitespace-nowrap">From</span>
            <span class={cn(
              "truncate font-medium text-foreground",
              "max-w-[140px] md:max-w-none"
            )} title={fromInfo}>
              {fromInfo}
            </span>
          </div>

          <!-- To 行 -->
          {#if toAddress && toAddress !== toInfo}
            <div class="flex items-center gap-1 md:contents">
              <span class="whitespace-nowrap">To</span>
              <span class={cn(
                "truncate font-mono text-foreground",
                "max-w-[140px] md:max-w-none"
              )} title={toAddress}>
                {truncateAddress(toAddress, 4)}
              </span>
            </div>
          {:else if toInfo !== fromInfo}
            <div class="flex items-center gap-1 md:contents">
              <span class="whitespace-nowrap">To</span>
              <span class={cn(
                "truncate font-medium text-foreground",
                "max-w-[140px] md:max-w-none"
              )} title={toInfo}>
                {toInfo}
              </span>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
