<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }
  import {
    formatUsdAmount,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取交易所转账相关参数 - 只保留必要的基础信息
  const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;
  const base = getParameterValue(event, 'base') as string;
  const exchangeSide = getParameterValue(event, 'exchangeSide') as number;
  const amountUsd = getParameterValue(event, 'amountUsd') as number;
  const chain = getParameterValue(event, 'chain') as string;

  // 获取转账方向
  const transferDirection = exchangeSide === 1 ? '转入' : exchangeSide === 2 ? '转出' : '未知';

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <!-- 简化的卡片内容 -->
  <CardContent class="p-3">
    <!-- 主要信息行 -->
    <div class="flex items-center justify-between mb-2">
      <!-- 左侧：币种和区块链 -->
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <!-- 币种 -->
        <span class="text-foreground font-semibold text-sm">
          {base || '未知'}
        </span>

        <!-- 区块链 -->
        {#if chain}
          <span class="text-muted-foreground text-xs">
            {chain}
          </span>
        {/if}
      </div>

      <!-- 右侧：转账方向 -->
      <Badge variant={transferDirection === '转入' ? 'default' : 'secondary'} class="text-xs flex-shrink-0">
        {transferDirection}
      </Badge>
    </div>

    <!-- 交易所和关键数据 -->
    <div class="grid grid-cols-2 gap-2 text-xs">
      <!-- 交易所 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">交易所</div>
        <div class="text-foreground font-medium truncate" title={exchangeLabel}>
          {exchangeLabel || '未知'}
        </div>
      </div>

      <!-- 转账金额（关键数据） -->
      <div class="space-y-1">
        <div class="text-muted-foreground">转账金额</div>
        <div class="text-foreground font-semibold">
          {formatUsdAmount(amountUsd)}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
