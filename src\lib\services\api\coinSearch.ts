// src/lib/services/api/coinSearch.ts
import type { CoinInfo, CoinSearchResponse } from '$lib/types';
import { currencyPersistentCache } from '$lib/services/cache/currencyPersistentCache';
import { logApiError } from '$lib/utils/logger';

import { BaseApiService } from './base';

/**
 * 币种搜索 API 服务
 */
export class CoinSearchApiService extends BaseApiService {
  constructor() {
    // 使用外部 API 基础 URL
    super('https://api.coinact.gg');
  }

  /**
   * 根据货币ID获取货币信息（带智能缓存）
   * @param currencyId 货币ID
   * @param forceRefresh 是否强制刷新缓存
   * @returns 货币信息
   */
  async getCurrencyInfo(currencyId: string, forceRefresh: boolean = false): Promise<CoinInfo | null> {
    if (!currencyId) {
      throw new Error('Currency ID is required');
    }

    try {
      // 1. 检查是否有正在进行的请求（请求去重）
      const pendingRequest = currencyPersistentCache.getPendingRequest(currencyId);
      if (pendingRequest) {
        return await pendingRequest;
      }

      // 2. 检查缓存（除非强制刷新）
      if (!forceRefresh) {
        const cachedItem = currencyPersistentCache.getCachedItem(currencyId);
        if (cachedItem) {
          return cachedItem.data;
        }
      }

      // 3. 创建新的 API 请求
      const requestPromise = this.fetchCurrencyFromApi(currencyId);
      currencyPersistentCache.setPendingRequest(currencyId, requestPromise);

      const result = await requestPromise;

      // 4. 缓存结果
      if (result) {
        currencyPersistentCache.setCachedItem(currencyId, result);
      }

      return result;
    } catch (error) {
      logApiError(error as Error, 'getCurrencyInfo', 'GET', undefined, {
        operation: 'currency_fetch',
        context: {
          currencyId,
          forceRefresh,
        },
      });
      return null;
    }
  }

  /**
   * 从 API 获取货币信息的内部方法
   * @param currencyId 货币ID
   * @returns 货币信息
   */
  private async fetchCurrencyFromApi(currencyId: string): Promise<CoinInfo | null> {
    const endpoint = `/catalog/currencies/${currencyId}`;

    try {
      const response = await this.get<CoinInfo>(endpoint, {
        headers: {
          accept: '*/*',
          'content-type': 'application/json',
        },
        // 外部 API 可能较慢，增加超时时间
        timeout: 15000,
        // 减少重试次数，避免过多请求
        retry: {
          maxRetries: 1,
          retryDelay: 500,
        },
      });

      return response.data;
    } catch (error) {
      logApiError(error as Error, 'fetchCurrencyFromApi', 'GET', undefined, {
        operation: 'api_request',
        context: {
          currencyId,
          endpoint,
        },
      });
      return null;
    }
  }

  /**
   * 根据名称搜索币种
   * @param name 币种名称关键词
   * @param limit 返回结果数量限制，默认为 10
   * @returns 币种搜索结果
   */
  async searchCoins(name: string, limit: number = 10): Promise<CoinSearchResponse> {
    const endpoint = `/ranking/coins?orderBy=marketCap&desc=true&limit=${limit}&name=${encodeURIComponent(name)}`;

    const response = await this.get<CoinSearchResponse>(endpoint, {
      headers: {
        accept: '*/*',
        'content-type': 'application/json',
      },
      // 外部 API 可能较慢，增加超时时间
      timeout: 15000,
      // 减少重试次数，避免过多请求
      retry: {
        maxRetries: 1,
        retryDelay: 500,
      },
    });

    return response.data;
  }

  /**
   * 获取热门币种列表（无搜索关键词时使用）
   * @param limit 返回结果数量限制，默认为 10
   * @returns 热门币种列表
   */
  async getPopularCoins(limit: number = 10): Promise<CoinSearchResponse> {
    const endpoint = `/ranking/coins?orderBy=marketCap&desc=true&limit=${limit}`;

    const response = await this.get<CoinSearchResponse>(endpoint, {
      headers: {
        accept: '*/*',
        'content-type': 'application/json',
      },
      timeout: 15000,
      retry: {
        maxRetries: 1,
        retryDelay: 500,
      },
    });

    return response.data;
  }
}

// 创建并导出服务实例
export const coinSearchApiService = new CoinSearchApiService();
