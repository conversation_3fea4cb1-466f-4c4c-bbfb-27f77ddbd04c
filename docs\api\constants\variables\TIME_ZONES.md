[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / TIME_ZONES

# Variable: TIME_ZONES

> `const` **TIME_ZONES**: readonly \[\{ `label`: `"UTC"`; `value`: `"UTC"`; \}, \{ `label`: `"北京时间 (UTC+8)"`; `value`: `"Asia/Shanghai"`; \}, \{ `label`: `"纽约时间 (UTC-5/-4)"`; `value`: `"America/New_York"`; \}, \{ `label`: `"伦敦时间 (UTC+0/+1)"`; `value`: `"Europe/London"`; \}, \{ `label`: `"东京时间 (UTC+9)"`; `value`: `"Asia/Tokyo"`; \}\]

Defined in: src/lib/constants/time.ts:2
