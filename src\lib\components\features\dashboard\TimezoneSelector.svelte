<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Tabs, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
  import {
    findTimezoneById,
    getDefaultTimezone,
    searchTimezones,
    TIMEZONE_CATEGORY_LABELS,
    TIMEZONES_BY_CATEGORY,
    type TimezoneOption,
  } from '$lib/constants/timezones';

  interface Props {
    /** 当前选中的时区 ID */
    selectedTimezone: string;
    /** 时区变更回调 */
    onTimezoneChange: (timezoneId: string) => void;
    /** 是否显示时区设置面板 */
    showSettings?: boolean;
    /** 切换设置面板显示状态的回调 */
    onToggleSettings?: () => void;
  }

  const {
    selectedTimezone,
    onTimezoneChange,
    showSettings = false,
    onToggleSettings,
  }: Props = $props();

  let timeZoneSearchQuery = $state('');

  // 当前选中的时区信息
  const currentTimezone = $derived(
    findTimezoneById(selectedTimezone) || getDefaultTimezone()
  );

  // 过滤后的时区列表
  const filteredTimezones = $derived(searchTimezones(timeZoneSearchQuery));

  // 按分类过滤的时区
  const filteredByCategory = $derived.by(() => {
    const result: Record<string, TimezoneOption[]> = {};

    // 特殊选项（总是显示）
    result.special = filteredTimezones.filter(tz =>
      TIMEZONES_BY_CATEGORY.special.some(special => special.id === tz.id)
    );

    // 其他分类
    ['africa', 'america', 'asia', 'europe'].forEach(category => {
      const categoryTimezones = filteredTimezones.filter(tz =>
        TIMEZONES_BY_CATEGORY[category as keyof typeof TIMEZONES_BY_CATEGORY]
          .some(catTz => catTz.id === tz.id)
      );
      if (categoryTimezones.length > 0) {
        result[category] = categoryTimezones;
      }
    });

    return result;
  });

  function handleTimezoneChange(timezoneId: string) {
    onTimezoneChange(timezoneId);
  }

  function handleToggleSettings() {
    onToggleSettings?.();
  }
</script>

<!-- 时区信息显示和设置触发器 -->
<div class="flex items-center justify-between">
  <span class="text-muted-foreground text-xs">
    浏览器时间: <span class="font-medium">中国标准时间 (CST)</span>
  </span>
  <div class="flex items-center space-x-2">
    <Badge variant="outline" class="text-xs">
      {currentTimezone.offset}
    </Badge>
    {#if onToggleSettings}
      <Button
        variant="link"
        size="sm"
        onclick={handleToggleSettings}
        class="h-auto p-0 text-xs"
      >
        {showSettings ? '收起设置' : '更改时间设置'}
      </Button>
    {/if}
  </div>
</div>

{#if showSettings}
  <!-- 时区设置面板 -->
  <div class="mt-3 space-y-3">
    <Tabs value="timezone" class="w-full">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="timezone">Time zone</TabsTrigger>
        <TabsTrigger value="fiscal">Fiscal year</TabsTrigger>
      </TabsList>

      <TabsContent value="timezone" class="mt-4 space-y-3">
        <!-- 搜索框 -->
        <Input
          type="text"
          placeholder="Type to search (country, city, abbreviation)"
          bind:value={timeZoneSearchQuery}
          class="text-xs"
        />

        <!-- 时区列表 -->
        <div class="max-h-48 space-y-1 overflow-y-auto">
          <!-- 特殊选项 -->
          {#if filteredByCategory.special?.length > 0}
            {#each filteredByCategory.special as timezone (timezone.id)}
              <Button
                onclick={() => handleTimezoneChange(timezone.id)}
                variant={selectedTimezone === timezone.id ? 'default' : 'ghost'}
                size="sm"
                class="h-auto w-full justify-between py-2 text-xs"
              >
                <span class="flex flex-col items-start">
                  <span>{timezone.displayName}</span>
                  <span class="text-muted-foreground text-xs">{timezone.description}</span>
                </span>
                <Badge variant="outline" class="text-xs">
                  {timezone.offset}
                </Badge>
              </Button>
            {/each}
          {/if}

          <!-- 其他分类 -->
          {#each Object.entries(filteredByCategory) as [category, timezones] (category)}
            {#if category !== 'special' && timezones.length > 0}
              <div class="text-foreground py-2 text-xs font-semibold">
                {TIMEZONE_CATEGORY_LABELS[category as keyof typeof TIMEZONE_CATEGORY_LABELS]}
              </div>
              {#each timezones as timezone (timezone.id)}
                <Button
                  onclick={() => handleTimezoneChange(timezone.id)}
                  variant={selectedTimezone === timezone.id ? 'default' : 'ghost'}
                  size="sm"
                  class="h-auto w-full justify-between py-2 text-xs"
                >
                  <span class="flex flex-col items-start">
                    <span>{timezone.displayName}</span>
                    <span class="text-muted-foreground text-xs">{timezone.description}</span>
                  </span>
                  <Badge variant="outline" class="text-xs">
                    {timezone.offset}
                  </Badge>
                </Button>
              {/each}
            {/if}
          {/each}
        </div>
      </TabsContent>

      <TabsContent value="fiscal" class="mt-4 space-y-3">
        <p class="text-muted-foreground text-sm">财政年度设置功能即将推出...</p>
      </TabsContent>
    </Tabs>
  </div>
{/if}
