<!-- src/lib/components/liquidation/LiquidationDashboard.svelte -->
<script lang="ts">
  import { onDestroy, onMount } from 'svelte';

  import { Alert, AlertDescription } from '$lib/components/ui/alert';
  // shadcn-svelte 组件导入
  import { Button } from '$lib/components/ui/button';
  import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
  } from '$lib/components/ui/tooltip';
  import { liquidationStore } from '$lib/stores/features/liquidation';

  // 获取派生的 store
  const trendCharts = liquidationStore.trendCharts;
  import { ChartPanel } from '$lib/components/charts';
  import type {
    LiquidationChartConfig,
    SnapshotTimeRange,
    TrendDuration,
    TrendTimeFrame,
    TrendTimeRange,
    TrendViewMode,
  } from '$lib/types';
  import {
    convertToLongShortPieData,
    convertToThresholdPieData,
  } from '$lib/utils/chartDataAdapter';

  // --- Chart Data Processing ---

  // 获取时间范围标签
  function getSnapshotTimeRangeLabel(range: string): string {
    const labels: Record<string, string> = {
      '1h': '近1小时',
      '4h': '近4小时',
      '12h': '近12小时',
      '24h': '近24小时',
    };
    return labels[range] || '近24小时';
  }

  function getTrendTimeRangeLabel(range: string): string {
    const labels: Record<string, string> = { '7d': '7天', '30d': '30天', '90d': '90天' };
    return labels[range] || '7天';
  }

  // 获取图表数据
  function getChartData(chart: LiquidationChartConfig, series: 'snapshot' | 'trend') {
    const rawData =
      series === 'snapshot' ? $liquidationStore.snapshotData : $liquidationStore.trendData;

    switch (chart.id) {
      case 'pie-long-short':
        return convertToLongShortPieData(rawData);
      case 'pie-order-size':
        return convertToThresholdPieData(rawData);
      default:
        return rawData;
    }
  }

  // 获取时间范围标签
  function getTimeRangeLabel(series: 'snapshot' | 'trend'): string {
    return series === 'snapshot'
      ? getSnapshotTimeRangeLabel($liquidationStore.timeFilters.snapshotTimeRange)
      : getTrendTimeRangeLabel($liquidationStore.timeFilters.trendTimeRange);
  }

  $: kpiChart = $liquidationStore.chartConfigs.find(
    (c) => c.id === 'kpi' && c.series === 'snapshot'
  );

  $: pieCharts = $liquidationStore.chartConfigs
    .filter(
      (c): c is LiquidationChartConfig & { id: 'pie-long-short' | 'pie-order-size' } =>
        (c.id === 'pie-long-short' || c.id === 'pie-order-size') && c.series === 'snapshot'
    )
    .sort((a, b) => a.order - b.order);

  // 使用 store 中的 trendCharts 派生 store，它会根据视图模式自动过滤

  // --- Time Filter Options ---

  const snapshotTimeRanges: { value: SnapshotTimeRange; label: string }[] = [
    { value: '1h', label: '近1小时' },
    { value: '4h', label: '近4小时' },
    { value: '12h', label: '近12小时' },
    { value: '24h', label: '近24小时' },
  ];

  const _trendTimeRanges: { value: TrendTimeRange; label: string }[] = [
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
    { value: '90d', label: '90天' },
  ];

  // 新的时间粒度选项（X轴每格对应的时间间隔）
  const trendTimeFrames: { value: TrendTimeFrame; label: string }[] = [
    { value: '1h', label: '1小时' },
    { value: '4h', label: '4小时' },
    { value: '1d', label: '1天' },
    { value: '1w', label: '1周' },
  ];

  // 新的总时间范围选项（整个X轴覆盖的时间范围）
  const trendDurations: { value: TrendDuration; label: string }[] = [
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
    { value: '90d', label: '90天' },
  ];

  // 趋势视图模式选项
  const trendViewModes: { value: TrendViewMode; label: string }[] = [
    { value: 'overall', label: '全市场' },
    { value: 'comparison', label: '主流/山寨对比' },
  ];

  const refreshIntervals = [
    { label: '不刷新', value: null },
    { label: '每1分钟', value: 60000 },
    { label: '每5分钟', value: 300000 },
    { label: '每15分钟', value: 900000 },
  ];

  // --- Event Handlers ---

  function handleChartClick(event: CustomEvent) {
    const { chartId, chartType, name, value } = event.detail;
    console.log(`Chart clicked: ${chartId}, Type: ${chartType}, Name: ${name}, Value: ${value}`);
  }

  async function handleSnapshotTimeRangeChange(event: Event) {
    const newRange = (event.target as HTMLSelectElement).value as SnapshotTimeRange;
    liquidationStore.setSnapshotTimeRange(newRange);
    await liquidationStore.loadSnapshotData(newRange);
  }

  async function _handleTrendTimeRangeChange(event: Event) {
    const newRange = (event.target as HTMLSelectElement).value as TrendTimeRange;
    liquidationStore.setTrendTimeRange(newRange);
    await liquidationStore.loadTrendData(newRange);
  }

  async function handleTrendTimeFrameChange(event: Event) {
    const newTimeFrame = (event.target as HTMLSelectElement).value as TrendTimeFrame;
    liquidationStore.setTrendTimeFrame(newTimeFrame);
    // API 以15分钟粒度返回数据，图表组件会自动重新聚合数据，不需要重新获取
  }

  async function handleTrendDurationChange(event: Event) {
    const newDuration = (event.target as HTMLSelectElement).value as TrendDuration;
    liquidationStore.setTrendDuration(newDuration);
    // 重新获取数据以应用新的时间范围
    await liquidationStore.loadDetailedTrendData(undefined, newDuration);
  }

  function handleTrendViewModeChange(viewMode: TrendViewMode) {
    liquidationStore.setTrendViewMode(viewMode);
    // 视图模式改变时，图表组件会自动重新渲染，不需要重新获取数据
  }

  let selectedRefreshInterval: number | null = null;
  function handleRefreshIntervalChange(event: Event) {
    const value = (event.target as HTMLSelectElement).value;
    selectedRefreshInterval = value === 'null' ? null : Number(value);

    if (selectedRefreshInterval) {
      liquidationStore.startAutoRefresh(selectedRefreshInterval);
    } else {
      liquidationStore.stopAutoRefresh();
    }
  }

  // --- Lifecycle ---

  onMount(() => {
    // 防止重复初始化
    let isInitialized = false;

    const initializeData = async () => {
      if (isInitialized) return;
      isInitialized = true;

      try {
        // 初始化数据
        await liquidationStore.refreshAllData();

        if (selectedRefreshInterval) {
          liquidationStore.startAutoRefresh(selectedRefreshInterval);
        }
      } catch (error) {
        console.error('Failed to initialize liquidation data:', error);
        isInitialized = false; // 允许重试
      }
    };

    // 监听快捷键刷新事件，添加防抖
    let refreshTimer: number | null = null;
    const handleRefreshData = () => {
      if (refreshTimer) {
        clearTimeout(refreshTimer);
      }

      refreshTimer = window.setTimeout(() => {
        liquidationStore.refreshAllData();
        refreshTimer = null;
      }, 1000); // 1秒防抖
    };

    window.addEventListener('refresh-data', handleRefreshData);

    // 延迟初始化，避免与其他组件冲突
    setTimeout(initializeData, 100);

    return () => {
      window.removeEventListener('refresh-data', handleRefreshData);
      if (refreshTimer) {
        clearTimeout(refreshTimer);
      }
    };
  });

  onDestroy(() => {
    liquidationStore.stopAutoRefresh();
  });
</script>

<div>
  <div class="mb-4 flex flex-wrap items-center justify-between gap-y-2">
    <h1 class="text-foreground text-2xl font-bold">市场清算监控 📊</h1>
    <div class="flex items-center gap-x-2">
      <!-- 手动刷新按钮 -->
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <Button variant="outline" size="sm" onclick={() => liquidationStore.refreshAllData()}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 15M20 20l-1.5-1.5A9 9 0 713.5 9"
                />
              </svg>
              刷新
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>手动刷新所有数据</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <!-- 定时刷新下拉框 -->
      <select
        id="refresh-interval"
        class="border-input bg-background focus:border-ring focus:ring-ring/50 h-9 rounded-md border px-3 py-2 text-sm shadow-xs focus:ring-[3px] focus:outline-none"
        on:change={handleRefreshIntervalChange}
      >
        {#each refreshIntervals as interval (interval.label)}
          <option value={interval.value} selected={interval.value === selectedRefreshInterval}
            >{interval.label}</option
          >
        {/each}
      </select>
    </div>
  </div>

  <!-- 系列一：市场清算快照 -->
  <div class="mb-6">
    <div class="mb-2 flex flex-wrap items-center justify-between gap-y-2">
      <div>
        <h2 class="text-foreground text-xl font-semibold">市场清算快照</h2>
      </div>
      <div class="flex items-center">
        <label for="snapshot-time-range" class="text-muted-foreground mr-2 text-sm font-medium"
          >快照窗口:</label
        >
        <select
          id="snapshot-time-range"
          class="border-input bg-background focus:border-ring focus:ring-ring/50 h-9 rounded-md border px-3 py-2 text-sm shadow-xs focus:ring-[3px] focus:outline-none"
          value={$liquidationStore.timeFilters.snapshotTimeRange}
          on:change={handleSnapshotTimeRangeChange}
        >
          {#each snapshotTimeRanges as option (option.value)}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
      </div>
    </div>

    {#if kpiChart}
      <div class="mb-3">
        <ChartPanel
          chart={kpiChart}
          data={getChartData(kpiChart, 'snapshot')}
          isLoading={$liquidationStore.isLoading}
          series="snapshot"
          timeRangeLabel={getTimeRangeLabel('snapshot')}
          enableDrag={false}
          showHeader={false}
          onChartClick={handleChartClick}
        />
      </div>
    {/if}

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {#each pieCharts as chart (chart.id)}
        <ChartPanel
          {chart}
          data={getChartData(chart, 'snapshot')}
          isLoading={$liquidationStore.isLoading}
          series="snapshot"
          timeRangeLabel={getTimeRangeLabel('snapshot')}
          enableDrag={false}
          showHeader={true}
          customHeight="450px"
          onChartClick={handleChartClick}
        />
      {/each}
    </div>
  </div>

  <!-- 系列二：市场清算趋势 -->
  <div class="mb-4">
    <div class="mb-2 flex flex-wrap items-center justify-between gap-y-2">
      <div>
        <h2 class="text-foreground text-xl font-semibold">市场清算趋势</h2>
      </div>
      <div class="flex items-center gap-x-4">
        <!-- 视图切换器 -->
        <div class="flex items-center">
          <span class="text-muted-foreground mr-2 text-sm font-medium">视图模式:</span>
          <div class="bg-muted flex items-center rounded-lg p-1">
            {#each trendViewModes as viewMode (viewMode.value)}
              <Button
                variant={$liquidationStore.timeFilters.trendViewMode === viewMode.value
                  ? 'default'
                  : 'ghost'}
                size="sm"
                class="h-8 px-3 {$liquidationStore.timeFilters.trendViewMode === viewMode.value
                  ? 'bg-background text-foreground shadow-sm'
                  : ''}"
                onclick={() => handleTrendViewModeChange(viewMode.value)}
              >
                {viewMode.label}
              </Button>
            {/each}
          </div>
        </div>

        <!-- Time Frame 筛选器 -->
        <div class="flex items-center">
          <label for="trend-time-frame" class="text-muted-foreground mr-2 text-sm font-medium"
            >Time Frame:</label
          >
          <select
            id="trend-time-frame"
            class="border-input bg-background focus:border-ring focus:ring-ring/50 h-9 rounded-md border px-3 py-2 text-sm shadow-xs focus:ring-[3px] focus:outline-none"
            value={$liquidationStore.timeFilters.trendTimeFrame}
            on:change={handleTrendTimeFrameChange}
          >
            {#each trendTimeFrames as option (option.value)}
              <option value={option.value}>{option.label}</option>
            {/each}
          </select>
        </div>

        <!-- 时间范围筛选器 -->
        <div class="flex items-center">
          <label for="trend-duration" class="text-muted-foreground mr-2 text-sm font-medium"
            >时间范围:</label
          >
          <select
            id="trend-duration"
            class="border-input bg-background focus:border-ring focus:ring-ring/50 h-9 rounded-md border px-3 py-2 text-sm shadow-xs focus:ring-[3px] focus:outline-none"
            value={$liquidationStore.timeFilters.trendDuration}
            on:change={handleTrendDurationChange}
          >
            {#each trendDurations as option (option.value)}
              <option value={option.value}>{option.label}</option>
            {/each}
          </select>
        </div>
      </div>
    </div>

    <div>
      {#each $trendCharts as chart (chart.id)}
        <ChartPanel
          {chart}
          data={getChartData(chart, 'trend')}
          isLoading={$liquidationStore.isLoading}
          series="trend"
          timeRangeLabel={getTimeRangeLabel('trend')}
          enableDrag={false}
          showHeader={true}
          customHeight="450px"
          onChartClick={handleChartClick}
        />
      {/each}
    </div>
  </div>

  {#if $liquidationStore.isLoading}
    <div class="fixed right-4 bottom-4 z-50 w-80">
      <Alert>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 animate-spin"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 15M20 20l-1.5-1.5A9 9 0 013.5 9"
          />
        </svg>
        <AlertDescription>数据加载中，请稍候...</AlertDescription>
      </Alert>
    </div>
  {/if}
</div>
