[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / CHART_CONFIG

# Variable: CHART_CONFIG

> `const` **CHART_CONFIG**: `object`

Defined in: src/lib/constants/charts.ts:30

## Type declaration

### ANIMATION_DURATION

> `readonly` **ANIMATION_DURATION**: `1000` = `1000`

### DEFAULT_HEIGHT

> `readonly` **DEFAULT_HEIGHT**: `400` = `400`

### MAX_HEIGHT

> `readonly` **MAX_HEIGHT**: `800` = `800`

### MIN_HEIGHT

> `readonly` **MIN_HEIGHT**: `200` = `200`

### TOOLTIP_TRIGGER_DELAY

> `readonly` **TOOLTIP_TRIGGER_DELAY**: `100` = `100`
