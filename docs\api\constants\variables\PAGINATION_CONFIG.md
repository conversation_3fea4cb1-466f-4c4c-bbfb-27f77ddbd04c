[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / PAGINATION_CONFIG

# Variable: PAGINATION_CONFIG

> `const` **PAGINATION_CONFIG**: `object`

Defined in: src/lib/constants/ui.ts:33

## Type declaration

### DEFAULT_PAGE_SIZE

> `readonly` **DEFAULT_PAGE_SIZE**: `10` = `10`

### MAX_VISIBLE_PAGES

> `readonly` **MAX_VISIBLE_PAGES**: `7` = `7`

### PAGE_SIZE_OPTIONS

> `readonly` **PAGE_SIZE_OPTIONS**: readonly \[`10`, `20`, `50`, `100`\]
