[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / ChartData

# Interface: ChartData

Defined in: src/lib/types/dashboard.ts:102

图表数据集合

包含仪表板中所有类型图表的数据

## Example

```typescript
const chartData: ChartData = {
  barChartData: [{ name: '一月', value: 100 }],
  lineChartData: [{ name: '周一', value: 85 }],
  pieChartData: [{ name: '直接访问', value: 320 }],
  scatterChartData: [{ name: 'Point 1', x: 10, y: 20, category: 'A' }],
};
```

## Properties

### barChartData

> **barChartData**: [`BarChartItem`](BarChartItem.md)[]

Defined in: src/lib/types/dashboard.ts:104

柱状图数据数组

---

### lineChartData

> **lineChartData**: [`LineChartItem`](LineChartItem.md)[]

Defined in: src/lib/types/dashboard.ts:106

折线图数据数组

---

### pieChartData

> **pieChartData**: [`PieChartItem`](PieChartItem.md)[]

Defined in: src/lib/types/dashboard.ts:108

饼图数据数组

---

### scatterChartData

> **scatterChartData**: [`ScatterChartItem`](ScatterChartItem.md)[]

Defined in: src/lib/types/dashboard.ts:110

散点图数据数组
