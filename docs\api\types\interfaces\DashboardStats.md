[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / DashboardStats

# Interface: DashboardStats

Defined in: src/lib/types/dashboard.ts:128

仪表板统计数据

包含仪表板顶部显示的关键指标

## Example

```typescript
const stats: DashboardStats = {
  totalUsers: 15420,
  monthlyRevenue: 45600,
  conversionRate: 12.5,
  activeUsers: 8930,
};
```

## Properties

### activeUsers

> **activeUsers**: `number`

Defined in: src/lib/types/dashboard.ts:136

活跃用户数

---

### conversionRate

> **conversionRate**: `number`

Defined in: src/lib/types/dashboard.ts:134

转化率（百分比）

---

### monthlyRevenue

> **monthlyRevenue**: `number`

Defined in: src/lib/types/dashboard.ts:132

月收入（单位：元或美元）

---

### totalUsers

> **totalUsers**: `number`

Defined in: src/lib/types/dashboard.ts:130

总用户数
