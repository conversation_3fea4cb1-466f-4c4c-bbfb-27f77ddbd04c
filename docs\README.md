# 项目文档

这里包含了项目的详细文档，包括组件使用指南、API 文档和开发指南。

> 🎉 **文档已更新**：同步了最新的代码重构，包括统一服务层、爆仓分析功能、MSW 集成等。

## 📚 文档目录

### 核心文档

- [组件文档](./components/README.md) - 所有组件的使用指南和 API 文档
- [服务文档](./services/README.md) - 统一服务层架构和 API 文档 🆕
- [状态管理文档](./stores/README.md) - 状态管理模式和使用指南
- [开发指南](./development/README.md) - 开发流程和最佳实践

### 功能文档

- [爆仓分析功能](./features/liquidation.md) - 专业金融数据可视化功能 🆕
- [主题切换功能](./features/theme-switching.md) - shadcn-svelte 标准主题管理 🆕
- [仪表板功能](./features/dashboard.md) - 数据可视化仪表板功能
- [数据查询功能](./features/data-query.md) - 灵活的数据查询和分析

### 技术文档

- [MSW 集成指南](./development/msw-integration.md) - API 模拟服务使用指南 🆕
- [MSW 生命周期管理](./development/msw-lifecycle.md) - 解决 MSW CPU 占用问题 🆕
- [常量管理](./development/constants.md) - 集中化常量管理方案 🆕
- [测试策略](./development/testing.md) - 完整的测试覆盖策略
- [API 文档指南](./api-guide.md) - 自动生成的 API 文档使用指南
- [自动生成的 API 文档](./api/README.md) - 完整的 TypeScript API 文档

## 🚀 快速导航

### 新手入门

1. [项目概览](../README.md) - 项目介绍和技术栈
2. [环境搭建](./development/setup.md) - 开发环境配置
3. [第一个组件](./development/first-component.md) - 组件开发入门
4. [MSW 使用入门](./development/msw-integration.md) - API 模拟服务入门

### 核心概念

1. [架构设计](./architecture/README.md) - 分层架构和设计原则
2. [组件设计原则](./components/design-principles.md) - 组件开发规范
3. [状态管理模式](./stores/patterns.md) - 响应式状态管理
4. [服务层架构](./services/README.md) - 统一数据服务层

### 功能模块

1. [仪表板系统](./features/dashboard.md) - 数据可视化仪表板
2. [爆仓分析系统](./features/liquidation.md) - 专业金融数据分析
3. [主题切换系统](./features/theme-switching.md) - shadcn-svelte 标准主题管理
4. [数据查询系统](./features/data-query.md) - 灵活数据查询工具

### 进阶主题

1. [性能优化](./development/performance.md) - 应用性能优化策略
2. [测试策略](./development/testing.md) - 单元测试和集成测试
3. [部署优化](./deployment/optimization.md) - 生产环境部署优化
4. [错误处理](./development/error-handling.md) - 统一错误处理机制
