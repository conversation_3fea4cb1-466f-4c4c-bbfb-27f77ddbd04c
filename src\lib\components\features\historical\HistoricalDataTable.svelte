<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { ArrowUpDown, ArrowUp, ArrowDown, TrendingUp, TrendingDown } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '$lib/components/ui/table';
  import { Badge } from '$lib/components/ui/badge';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { formatCurrency, formatNumber } from '$lib/utils/formatters';
  import { formatDateInTimezone } from '$lib/utils/timezone';
  import { createModuleLogger } from '$lib/utils/logger';
  import type { HistoricalTableRow, HistoricalQueryParams, MarketEventType } from '$lib/types';
  import { isDirectionalEventType } from '$lib/types/business/market';

  const historicalTableLogger = createModuleLogger('historical-data-table');
  const dispatch = createEventDispatcher<{
    sort: { field: string; order: 'asc' | 'desc' };
  }>();

  // 组件属性
  export let tableRows: HistoricalTableRow[] = [];
  export let selectedCoins: string[] = [];
  export let loading = false;
  export let total = 0;
  export let sortBy = 'date';
  export let sortOrder: 'asc' | 'desc' = 'desc';
  export let dataType: MarketEventType | undefined = undefined;

  // 内部状态
  let hoveredRow: string | null = null;

  // 响应式变量
  $: isDirectional = dataType && isDirectionalEventType(dataType);
  $: directionalLabel = getDirectionalLabel(dataType);

  // 处理排序
  function handleSort(field: string) {
    const newOrder = sortBy === field && sortOrder === 'desc' ? 'asc' : 'desc';
    historicalTableLogger.debug('Sorting table', { field, order: newOrder });
    dispatch('sort', { field, order: newOrder });
  }



  // 获取排序图标
  function getSortIcon(field: string) {
    if (sortBy !== field) return ArrowUpDown;
    return sortOrder === 'asc' ? ArrowUp : ArrowDown;
  }

  // 格式化变化百分比
  function formatChangePercent(value: number): string {
    const formatted = formatNumber(Math.abs(value), 2);
    return value >= 0 ? `+${formatted}%` : `-${formatted}%`;
  }

  // 获取变化百分比的样式类
  function getChangePercentClass(value: number): string {
    if (value > 0) return 'text-green-600';
    if (value < 0) return 'text-red-600';
    return 'text-muted-foreground';
  }

  // 处理行悬停
  function handleRowHover(date: string | null) {
    hoveredRow = date;
  }

  // 获取方向性数据标签
  function getDirectionalLabel(eventType: MarketEventType | undefined): { long: string; short: string } {
    switch (eventType) {
      case 'LIQUIDATION_ORDER':
        return { long: '多头清算', short: '空头清算' };
      case 'VOLUME_INCREASE':
        return { long: '买入量增长', short: '卖出量增长' };
      default:
        return { long: '多头', short: '空头' };
    }
  }
</script>

<Card>
  <CardHeader class="pb-4">
    <CardTitle class="text-xl font-semibold">历史数据查询结果</CardTitle>
    {#if total > 0}
      <p class="text-sm text-muted-foreground">
        共 {total} 条记录
      </p>
    {/if}
  </CardHeader>
  <CardContent class="pt-0">
    <div class="w-full overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <!-- 日期列 -->
            <TableHead class="min-w-[120px]">
              <Button
                variant="ghost"
                size="sm"
                onclick={() => handleSort('date')}
                class="h-auto p-0 font-semibold hover:bg-transparent"
              >
                日期
                <svelte:component this={getSortIcon('date')} class="ml-1 h-4 w-4" />
              </Button>
            </TableHead>
            
            <!-- 动态币种列 -->
            {#each selectedCoins as coin}
              {#if isDirectional}
                <!-- 方向性数据：分离的多头/空头列 -->
                <TableHead class="min-w-[120px] text-center">
                  <div class="flex flex-col items-center">
                    <span class="font-semibold">{coin}</span>
                    <span class="text-xs text-red-600">{directionalLabel.long}</span>
                  </div>
                </TableHead>
                <TableHead class="min-w-[120px] text-center">
                  <div class="flex flex-col items-center">
                    <span class="font-semibold">{coin}</span>
                    <span class="text-xs text-green-600">{directionalLabel.short}</span>
                  </div>
                </TableHead>
              {:else}
                <!-- 非方向性数据：常规列 -->
                <TableHead class="min-w-[150px] text-center">
                  <div class="flex flex-col items-center">
                    <span class="font-semibold">{coin}</span>
                    <span class="text-xs text-muted-foreground">金额 (USD)</span>
                  </div>
                </TableHead>
              {/if}
            {/each}
          </TableRow>
        </TableHeader>
        <TableBody>
          {#if loading}
            <!-- 加载状态 -->
            {#each Array(5) as _}
              <TableRow>
                <TableCell><Skeleton class="h-4 w-20" /></TableCell>
                {#each selectedCoins as _}
                  {#if isDirectional}
                    <!-- 方向性数据：两列骨架屏 -->
                    <TableCell class="text-center"><Skeleton class="h-4 w-20 mx-auto" /></TableCell>
                    <TableCell class="text-center"><Skeleton class="h-4 w-20 mx-auto" /></TableCell>
                  {:else}
                    <!-- 非方向性数据：单列骨架屏 -->
                    <TableCell class="text-center"><Skeleton class="h-4 w-24 mx-auto" /></TableCell>
                  {/if}
                {/each}
              </TableRow>
            {/each}
          {:else if tableRows.length === 0}
            <!-- 空状态 -->
            <TableRow>
              <TableCell colspan={isDirectional ? selectedCoins.length * 2 + 1 : selectedCoins.length + 1} class="text-center py-8">
                <div class="text-muted-foreground">
                  <p class="text-lg font-medium">暂无数据</p>
                  <p class="text-sm">请调整查询条件后重新查询</p>
                </div>
              </TableCell>
            </TableRow>
          {:else}
            <!-- 数据行 -->
            {#each tableRows as row}
              <TableRow 
                class="hover:bg-muted/50 transition-colors"
                onmouseenter={() => handleRowHover(row.date)}
                onmouseleave={() => handleRowHover(null)}
              >
                <!-- 日期列 -->
                <TableCell class="font-medium">
                  <div class="flex flex-col">
                    <span>{row.dateDisplay}</span>
                    <span class="text-xs text-muted-foreground">{row.date}</span>
                  </div>
                </TableCell>
                
                <!-- 币种数据列 -->
                {#each selectedCoins as coin}
                  {#if isDirectional && row.coinData[coin]?.directional}
                    <!-- 方向性数据：多头列 -->
                    <TableCell class="text-center">
                      <div class="flex flex-col items-center space-y-1">
                        <div class="flex items-center space-x-1">
                          <TrendingUp class="h-3 w-3 text-red-600" />
                          <span class="font-medium text-red-600">
                            {row.coinData[coin].directional.formattedLongAmount}
                          </span>
                        </div>
                        <span class="text-xs text-muted-foreground">
                          {row.coinData[coin].directional.longCount} 笔
                        </span>
                      </div>
                    </TableCell>
                    <!-- 方向性数据：空头列 -->
                    <TableCell class="text-center">
                      <div class="flex flex-col items-center space-y-1">
                        <div class="flex items-center space-x-1">
                          <TrendingDown class="h-3 w-3 text-green-600" />
                          <span class="font-medium text-green-600">
                            {row.coinData[coin].directional.formattedShortAmount}
                          </span>
                        </div>
                        <span class="text-xs text-muted-foreground">
                          {row.coinData[coin].directional.shortCount} 笔
                        </span>
                      </div>
                    </TableCell>
                  {:else if !isDirectional && row.coinData[coin]}
                    <!-- 非方向性数据：常规列 -->
                    <TableCell class="text-center">
                      <div class="flex flex-col items-center space-y-1">
                        <span class="font-medium">
                          {row.coinData[coin].formattedAmount}
                        </span>
                        <div class="flex items-center space-x-2">
                          <Badge
                            variant="outline"
                            class={getChangePercentClass(row.coinData[coin].changePercent)}
                          >
                            {formatChangePercent(row.coinData[coin].changePercent)}
                          </Badge>
                        </div>
                        <span class="text-xs text-muted-foreground">
                          交易量: {row.coinData[coin].formattedVolume}
                        </span>
                      </div>
                    </TableCell>
                  {:else}
                    <!-- 无数据状态 -->
                    {#if isDirectional}
                      <TableCell class="text-center">
                        <span class="text-muted-foreground">-</span>
                      </TableCell>
                      <TableCell class="text-center">
                        <span class="text-muted-foreground">-</span>
                      </TableCell>
                    {:else}
                      <TableCell class="text-center">
                        <span class="text-muted-foreground">-</span>
                      </TableCell>
                    {/if}
                  {/if}
                {/each}
              </TableRow>
            {/each}
          {/if}
        </TableBody>
      </Table>
    </div>
  </CardContent>
</Card>

<style>
  /* 确保表格在小屏幕上可以水平滚动 */
  :global(.table-container) {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* 优化表格行的悬停效果 */
  :global(.table-row-hover) {
    background-color: hsl(var(--muted) / 0.5);
  }
</style>
