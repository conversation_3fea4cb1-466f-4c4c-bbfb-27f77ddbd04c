/**
 * 市场数据相关类型定义
 * 
 * @category Business Types
 */

import type { ApiResponse, CoinOption, DataTypeOption } from '../core';

/**
 * 市场事件参数类型
 */
export interface MarketEventParameter {
  /** 参数ID */
  parameterId: string;
  /** 参数值 */
  value: string | number | boolean;
}

/**
 * 市场事件类型枚举
 */
export type MarketEventType =
  | 'TWAP_DETECTION'
  | 'LIQUIDATION_ORDER'
  | 'EXCHANGE_TRANSFER'
  | 'ORDERBOOK_IMBALANCE'
  | 'FUNDING_RATE_SWITCH'
  | 'EXTREME_FUNDING_RATE'
  | 'VOLUME_INCREASE'
  | 'OPEN_INTEREST_VARIATION';

/**
 * 支持方向性数据的事件类型
 */
export const DIRECTIONAL_EVENT_TYPES: MarketEventType[] = [
  'LIQUIDATION_ORDER',
  'VOLUME_INCREASE'
];

/**
 * 检查事件类型是否支持方向性数据
 */
export function isDirectionalEventType(eventType: MarketEventType): boolean {
  return DIRECTIONAL_EVENT_TYPES.includes(eventType);
}

/**
 * 市场事件数据项
 */
export interface MarketEvent {
  /** 唯一标识符（ULID格式） */
  id: string;
  /** 市场事件类型 */
  marketEventType: MarketEventType;
  /** 时间戳（Unix秒） */
  date: number;
  /** 事件参数 */
  parameters: MarketEventParameter[];
  /** 货币ID */
  currency: string;
}

/**
 * 市场事件查询响应
 */
export type MarketEventResponse = ApiResponse<MarketEvent[]>;

/**
 * 币种信息
 */
export interface CoinInfo {
  /** 币种ID */
  id: string;
  /** 图标URL */
  image: string;
  /** 币种符号 */
  symbol: string;
  /** 币种名称 */
  name: string;
  /** 市场排名 */
  rank: string;
  /** 市值 */
  marketCap: number;
  /** 当前价格 */
  price: number;
  /** 价格变化 */
  priceVariation?: {
    /** 1小时变化 */
    h1: number;
    /** 24小时变化 */
    h24: number;
  };
  /** 买卖盘数据 */
  bidsAndAsks?: {
    /** 10%买盘总和 */
    bidsSum10Percent: number;
    /** 10%卖盘总和 */
    asksSum10Percent: number;
    /** 30%买盘总和 */
    bidsSum30Percent: number;
    /** 30%卖盘总和 */
    asksSum30Percent: number;
    /** 10%买卖比例 */
    bidsAndAsksRatio10: number;
    /** 30%买卖比例 */
    bidsAndAsksRatio30: number;
  };
  /** 资金费率 */
  fundingRate?: number;
  /** 持仓量 */
  openInterest?: number;
  /** 交易量 */
  volume?: {
    /** 24小时交易量 */
    h24: number;
  };
  /** 持仓量/交易量比例 */
  openInterestPerVolume?: number;
}

/**
 * 币种搜索响应
 */
export interface CoinSearchResponse {
  /** 当前页 */
  page: number;
  /** 总数 */
  total: number;
  /** 当前页数量 */
  count: number;
  /** 币种列表 */
  items: CoinInfo[];
}

/**
 * 市场事件类型显示名称映射
 */
export const MARKET_EVENT_TYPE_LABELS: Record<MarketEventType, string> = {
  TWAP_DETECTION: 'TWAP检测',
  LIQUIDATION_ORDER: '清算订单',
  EXCHANGE_TRANSFER: '交易所转账',
  ORDERBOOK_IMBALANCE: '订单簿失衡',
  FUNDING_RATE_SWITCH: '资金费率切换',
  EXTREME_FUNDING_RATE: '极端资金费率',
  VOLUME_INCREASE: '交易量增长',
  OPEN_INTEREST_VARIATION: '持仓量变化',
};

/**
 * 历史数据项接口
 */
export interface HistoricalDataItem {
  /** 唯一标识符 */
  id: string;
  /** 日期，格式 YYYY-MM-DD */
  date: string;
  /** 币种符号 */
  symbol: string;
  /** 币种名称 */
  name: string;
  /** 数据类型 */
  type: MarketEventType;
  /** 金额（USD） */
  amount: number;
  /** 数量 */
  quantity: number;
  /** 价格 */
  price: number;
  /** 变化百分比 */
  changePercent: number;
  /** 交易量 */
  volume: number;
  /** 市值 */
  marketCap: number;
  /** 时间戳（秒） */
  timestamp: number;
  /** 额外元数据 */
  metadata?: Record<string, any>;
}

/**
 * 历史数据查询参数
 */
export interface HistoricalQueryParams {
  /** 选中的币种符号数组 */
  selectedCoins: string[];
  /** 开始日期，格式 YYYY-MM-DD */
  startDate: string;
  /** 结束日期，格式 YYYY-MM-DD */
  endDate: string;
  /** 数据类型，单选模式 */
  dataTypes: MarketEventType;
  /** 排序字段 */
  sortBy?: 'date' | 'amount' | 'volume' | 'marketCap';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 方向性数据接口
 */
export interface DirectionalData {
  /** 多头/买入金额 */
  longAmount: number;
  /** 格式化的多头/买入金额 */
  formattedLongAmount: string;
  /** 空头/卖出金额 */
  shortAmount: number;
  /** 格式化的空头/卖出金额 */
  formattedShortAmount: string;
  /** 多头/买入数量 */
  longCount: number;
  /** 空头/卖出数量 */
  shortCount: number;
  /** 多空比例 */
  longShortRatio: number;
}

/**
 * 历史数据表格行数据
 */
export interface HistoricalTableRow {
  /** 日期，格式 YYYY-MM-DD */
  date: string;
  /** 格式化的日期显示 */
  dateDisplay: string;
  /** 各币种的数据，键为币种符号 */
  coinData: Record<string, {
    amount: number;
    formattedAmount: string;
    changePercent: number;
    volume: number;
    formattedVolume: string;
    /** 方向性数据（仅当数据类型支持时存在） */
    directional?: DirectionalData;
  }>;
  /** 当日总金额 */
  totalAmount: number;
  /** 格式化的总金额 */
  formattedTotalAmount: string;
  /** 当日总方向性数据（仅当数据类型支持时存在） */
  totalDirectional?: DirectionalData;
}

/**
 * 历史数据查询响应
 */
export interface HistoricalQueryResponse {
  /** 原始数据项数组 */
  items: HistoricalDataItem[];
  /** 表格行数据 */
  tableRows: HistoricalTableRow[];
  /** 总记录数 */
  total: number;
  /** 查询参数 */
  queryParams: HistoricalQueryParams;
}

/**
 * 历史数据 API 响应类型
 */
export type HistoricalDataApiResponse = ApiResponse<HistoricalQueryResponse>;

/**
 * 历史数据统计信息
 */
export interface HistoricalDataStats {
  /** 总记录数 */
  totalRecords: number;
  /** 日期范围 */
  dateRange: {
    start: string;
    end: string;
  };
  /** 币种数量 */
  coinCount: number;
  /** 数据类型数量 */
  dataTypeCount: number;
  /** 总金额 */
  totalAmount: number;
  /** 平均金额 */
  averageAmount: number;
  /** 最大金额 */
  maxAmount: number;
  /** 最小金额 */
  minAmount: number;
}

/**
 * 历史数据查询表单状态
 */
export interface HistoricalQueryFormState {
  /** 选中的币种符号数组 */
  selectedCoins: string[];
  /** 开始日期，格式 YYYY-MM-DD */
  startDate: string;
  /** 结束日期，格式 YYYY-MM-DD */
  endDate: string;
  /** 选中的数据类型 */
  selectedDataTypes: MarketEventType | null;
  /** 是否正在查询 */
  isQuerying: boolean;
  /** 查询错误 */
  error: string | null;
  /** 表单验证错误 */
  validationErrors: Record<string, string>;
  /** 是否已修改 */
  isDirty: boolean;
}

/**
 * 历史数据查询状态
 */
export type HistoricalQueryStatus = 'idle' | 'loading' | 'success' | 'error';

/**
 * 历史数据查询错误
 */
export interface HistoricalQueryError {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 详细信息 */
  details?: string;
  /** 时间戳 */
  timestamp: number;
}

// 辅助函数类型定义
export type GetParameterValueFn = (event: MarketEvent, parameterId: string) => string | number | boolean | undefined;
export type FormatEventDateFn = (timestamp: number) => string;
export type GetEventTypeLabelFn = (eventType: MarketEventType) => string;
