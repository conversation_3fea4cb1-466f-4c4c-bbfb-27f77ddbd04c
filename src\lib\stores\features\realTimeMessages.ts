// src/lib/stores/features/realTimeMessages.ts

import { get,writable } from 'svelte/store';

import type { RealTimeMessagesQuery } from '$lib/services/api/realTimeMessages';
import { realTimeMessagesApiService } from '$lib/services/api/realTimeMessages';

import { activeTabId,tabs, updateTabBadge } from '../ui/tabs';

/**
 * 消息类型定义
 */
export type MessageType = 'info' | 'success' | 'warning' | 'error' | 'notification';

/**
 * 实时消息接口
 */
export interface RealTimeMessage {
  /** 消息唯一标识 */
  id: string;
  /** 消息类型 */
  type: MessageType;
  /** 消息标题 */
  title: string;
  /** 消息内容 */
  content: string;
  /** 消息时间戳 */
  timestamp: string;
  /** 是否已读 */
  isRead: boolean;
  /** 消息来源 */
  source?: string;
  /** 消息优先级 */
  priority?: 'low' | 'medium' | 'high';
}

/**
 * 实时消息状态接口
 */
export interface RealTimeMessagesState {
  /** 消息列表 */
  messages: RealTimeMessage[];
  /** 连接状态 */
  isConnected: boolean;
  /** 最后更新时间 */
  lastUpdateTime: string | null;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 轮询状态 */
  isPolling: boolean;
  /** 轮询间隔（毫秒） */
  pollingInterval: number;
  /** 是否页面可见（用于暂停/恢复轮询） */
  isPageVisible: boolean;
  /** 请求进行中标志（防止重复请求） */
  isRequestInProgress: boolean;
  /** 重试次数 */
  retryCount: number;
  /** 最大重试次数 */
  maxRetries: number;
}

/**
 * 初始状态
 */
const initialState: RealTimeMessagesState = {
  messages: [],
  isConnected: false,
  lastUpdateTime: null,
  isLoading: false,
  error: null,
  isPolling: false,
  pollingInterval: 4000, // 4秒轮询间隔
  isPageVisible: true,
  isRequestInProgress: false,
  retryCount: 0,
  maxRetries: 3,
};

/**
 * 创建实时消息状态管理
 */
function createRealTimeMessagesStore() {
  const { subscribe, set, update } = writable<RealTimeMessagesState>(initialState);

  // HTTP 轮询相关变量
  let pollingTimer: NodeJS.Timeout | null = null;
  let isInitialized = false;
  let visibilityChangeHandler: (() => void) | null = null;

  /**
   * 检查当前是否在实时消息页面
   */
  function isOnRealTimeMessagesPage(): boolean {
    const currentActiveTabId = get(activeTabId);
    return currentActiveTabId === '/query/real-time-messages';
  }

  /**
   * 更新标签页徽章（只在非当前页面时显示）
   */
  function updateTabBadgeCount(state: RealTimeMessagesState) {
    // 如果当前正在查看实时消息页面，不显示徽章（消息视为已读）
    if (isOnRealTimeMessagesPage()) {
      updateTabBadge('/query/real-time-messages', 0);
      return;
    }

    // 只有在其他页面时才显示未读消息数量
    const unreadCount = state.messages.filter((msg) => !msg.isRead).length;
    updateTabBadge('/query/real-time-messages', unreadCount);
  }

  /**
   * 从 API 获取最新消息
   */
  async function fetchLatestMessages(): Promise<void> {
    // 防止重复请求
    update((state) => {
      if (state.isRequestInProgress) {
        return state;
      }
      return {
        ...state,
        isRequestInProgress: true,
        error: null,
      };
    });

    try {
      const currentState = await new Promise<RealTimeMessagesState>((resolve) => {
        let unsubscribe: (() => void) | undefined;
        unsubscribe = subscribe((state) => {
          if (unsubscribe) unsubscribe();
          resolve(state);
        });
      });

      const response = await realTimeMessagesApiService.getLatestMessages(
        currentState.lastUpdateTime || undefined
      );

      if (response.status === 'success' && response.data) {
        const { messages: newMessages, serverTime } = response.data;

        update((state) => {
          // 合并新消息，避免重复
          const existingIds = new Set(state.messages.map((msg) => msg.id));
          const uniqueNewMessages = newMessages.filter((msg) => !existingIds.has(msg.id));

          const updatedMessages = [...uniqueNewMessages, ...state.messages].slice(0, 200); // 最多保留200条消息

          const newState = {
            ...state,
            messages: updatedMessages,
            lastUpdateTime: serverTime,
            isConnected: true,
            isRequestInProgress: false,
            retryCount: 0, // 重置重试次数
            error: null,
          };

          updateTabBadgeCount(newState);
          return newState;
        });
      }
    } catch (error) {
      console.error('Failed to fetch latest messages:', error);

      update((state) => {
        const newRetryCount = state.retryCount + 1;
        const shouldRetry = newRetryCount <= state.maxRetries;

        const newState = {
          ...state,
          isRequestInProgress: false,
          isConnected: !shouldRetry, // 超过重试次数则标记为断开连接
          retryCount: newRetryCount,
          error: shouldRetry
            ? `网络请求失败，正在重试 (${newRetryCount}/${state.maxRetries})...`
            : `网络连接失败，已停止重试。错误: ${error instanceof Error ? error.message : String(error)}`,
        };

        return newState;
      });

      // 如果还有重试机会，延迟后重试
      const currentState = await new Promise<RealTimeMessagesState>((resolve) => {
        let unsubscribe: (() => void) | undefined;
        unsubscribe = subscribe((state) => {
          if (unsubscribe) unsubscribe();
          resolve(state);
        });
      });

      if (currentState.retryCount <= currentState.maxRetries) {
        setTimeout(
          () => {
            fetchLatestMessages();
          },
          Math.min(1000 * currentState.retryCount, 5000)
        ); // 指数退避，最大5秒
      }
    }
  }

  /**
   * 启动轮询
   */
  function startPolling() {
    update((state) => ({
      ...state,
      isPolling: true,
      error: null,
    }));

    // 立即获取一次数据
    fetchLatestMessages();

    // 设置定时轮询
    const currentState = new Promise<RealTimeMessagesState>((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = subscribe((state) => {
        if (unsubscribe) unsubscribe();
        resolve(state);
      });
    });

    currentState.then((state) => {
      pollingTimer = setInterval(() => {
        // 检查页面可见性和轮询状态
        const currentState = new Promise<RealTimeMessagesState>((resolve) => {
          let unsubscribe: (() => void) | undefined;
          unsubscribe = subscribe((state) => {
            if (unsubscribe) unsubscribe();
            resolve(state);
          });
        });

        currentState.then((state) => {
          if (state.isPolling && state.isPageVisible && !state.isRequestInProgress) {
            fetchLatestMessages();
          }
        });
      }, state.pollingInterval);
    });
  }

  /**
   * 停止轮询
   */
  function stopPolling() {
    if (pollingTimer) {
      clearInterval(pollingTimer);
      pollingTimer = null;
    }

    update((state) => ({
      ...state,
      isPolling: false,
    }));
  }

  /**
   * 页面可见性变化处理
   */
  function handleVisibilityChange() {
    const isVisible = !document.hidden;

    update((state) => ({
      ...state,
      isPageVisible: isVisible,
    }));

    // 页面重新可见时，立即获取最新数据
    if (isVisible) {
      const currentState = new Promise<RealTimeMessagesState>((resolve) => {
        let unsubscribe: (() => void) | undefined;
        unsubscribe = subscribe((state) => {
          if (unsubscribe) unsubscribe();
          resolve(state);
        });
      });

      currentState.then((state) => {
        if (state.isPolling && !state.isRequestInProgress) {
          fetchLatestMessages();
        }
      });
    }
  }

  /**
   * 检查实时消息标签页是否存在
   */
  function isRealTimeMessagesTabOpen(): boolean {
    const currentTabs = get(tabs);
    return currentTabs.some((tab) => tab.href === '/query/real-time-messages');
  }

  /**
   * 初始化实时消息服务（仅初始化基础设置，不启动轮询）
   */
  function initialize() {
    if (isInitialized) {
      return;
    }

    isInitialized = true;

    // 设置页面可见性监听
    if (typeof document !== 'undefined') {
      visibilityChangeHandler = handleVisibilityChange;
      document.addEventListener('visibilitychange', visibilityChangeHandler);
    }

    // 注意：不再自动启动轮询，改为标签页感知启动
    console.log('RealTimeMessages: 基础服务已初始化，等待标签页打开启动轮询');
  }

  /**
   * 标签页感知轮询启动（当实时消息标签页打开时调用）
   */
  function startTabAwarePolling() {
    if (!isInitialized) {
      initialize();
    }

    // 检查标签页是否存在
    if (isRealTimeMessagesTabOpen()) {
      console.log('RealTimeMessages: 实时消息标签页存在，启动轮询');
      startPolling();
    } else {
      console.log('RealTimeMessages: 实时消息标签页不存在，不启动轮询');
    }
  }

  /**
   * 标签页感知轮询停止（当实时消息标签页关闭时调用）
   */
  function stopTabAwarePolling() {
    // 检查标签页是否仍然存在
    if (!isRealTimeMessagesTabOpen()) {
      console.log('RealTimeMessages: 实时消息标签页已关闭，停止轮询');
      stopPolling();
    } else {
      console.log('RealTimeMessages: 实时消息标签页仍然存在，继续轮询');
    }
  }

  /**
   * 开始监听实时消息（兼容旧接口）
   */
  function startListening() {
    initialize();
  }

  /**
   * 停止监听实时消息
   */
  function stopListening() {
    stopPolling();
  }

  /**
   * 完全停止实时消息服务（仅在应用关闭时调用）
   */
  function destroy() {
    stopPolling();

    // 移除页面可见性监听
    if (typeof document !== 'undefined' && visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', visibilityChangeHandler);
      visibilityChangeHandler = null;
    }

    isInitialized = false;

    update((state) => ({
      ...state,
      isConnected: false,
      isPolling: false,
    }));
  }

  /**
   * 手动刷新消息
   */
  function refreshMessages() {
    update((state) => ({
      ...state,
      isLoading: true,
      retryCount: 0, // 重置重试次数
    }));

    // 立即获取最新消息
    fetchLatestMessages().finally(() => {
      update((state) => ({
        ...state,
        isLoading: false,
      }));
    });
  }

  /**
   * 标记消息为已读
   */
  async function markAsRead(messageId: string) {
    // 先更新本地状态
    update((state) => {
      const newState = {
        ...state,
        messages: state.messages.map((msg) =>
          msg.id === messageId ? { ...msg, isRead: true } : msg
        ),
      };
      updateTabBadgeCount(newState);
      return newState;
    });

    // 同步到服务器
    try {
      await realTimeMessagesApiService.markAsRead(messageId);
    } catch (error) {
      console.error('Failed to mark message as read on server:', error);
      // 如果服务器更新失败，回滚本地状态
      update((state) => {
        const newState = {
          ...state,
          messages: state.messages.map((msg) =>
            msg.id === messageId ? { ...msg, isRead: false } : msg
          ),
        };
        updateTabBadgeCount(newState);
        return newState;
      });
    }
  }

  /**
   * 标记所有消息为已读
   */
  async function markAllAsRead() {
    // 先更新本地状态
    const previousMessages = await new Promise<RealTimeMessage[]>((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = subscribe((state) => {
        if (unsubscribe) unsubscribe();
        resolve([...state.messages]);
      });
    });

    update((state) => {
      const newState = {
        ...state,
        messages: state.messages.map((msg) => ({ ...msg, isRead: true })),
      };
      updateTabBadgeCount(newState);
      return newState;
    });

    // 同步到服务器
    try {
      await realTimeMessagesApiService.markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all messages as read on server:', error);
      // 如果服务器更新失败，回滚本地状态
      update((state) => {
        const newState = {
          ...state,
          messages: previousMessages,
        };
        updateTabBadgeCount(newState);
        return newState;
      });
    }
  }

  /**
   * 清除所有消息
   */
  async function clearAllMessages() {
    // 备份当前消息
    const previousMessages = await new Promise<RealTimeMessage[]>((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = subscribe((state) => {
        if (unsubscribe) unsubscribe();
        resolve([...state.messages]);
      });
    });

    // 先更新本地状态
    update((state) => {
      const newState = {
        ...state,
        messages: [],
      };
      updateTabBadgeCount(newState);
      return newState;
    });

    // 同步到服务器
    try {
      await realTimeMessagesApiService.clearAllMessages();
    } catch (error) {
      console.error('Failed to clear all messages on server:', error);
      // 如果服务器更新失败，回滚本地状态
      update((state) => {
        const newState = {
          ...state,
          messages: previousMessages,
        };
        updateTabBadgeCount(newState);
        return newState;
      });
    }
  }

  /**
   * 删除指定消息
   */
  async function deleteMessage(messageId: string) {
    // 备份要删除的消息
    const messageToDelete = await new Promise<RealTimeMessage | undefined>((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = subscribe((state) => {
        if (unsubscribe) unsubscribe();
        resolve(state.messages.find((msg) => msg.id === messageId));
      });
    });

    // 先更新本地状态
    update((state) => {
      const newState = {
        ...state,
        messages: state.messages.filter((msg) => msg.id !== messageId),
      };
      updateTabBadgeCount(newState);
      return newState;
    });

    // 同步到服务器
    try {
      await realTimeMessagesApiService.deleteMessage(messageId);
    } catch (error) {
      console.error('Failed to delete message on server:', error);
      // 如果服务器更新失败，回滚本地状态
      if (messageToDelete) {
        update((state) => {
          const newState = {
            ...state,
            messages: [...state.messages, messageToDelete].sort(
              (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            ),
          };
          updateTabBadgeCount(newState);
          return newState;
        });
      }
    }
  }

  return {
    subscribe,
    initialize,
    startListening,
    stopListening,
    destroy,
    refreshMessages,
    markAsRead,
    markAllAsRead,
    clearAllMessages,
    deleteMessage,
    startPolling,
    stopPolling,
    fetchLatestMessages,
    // 新增标签页感知的轮询控制方法
    startTabAwarePolling,
    stopTabAwarePolling,
    isRealTimeMessagesTabOpen,
  };
}

/**
 * 实时消息状态管理实例
 */
export const realTimeMessagesStore = createRealTimeMessagesStore();
