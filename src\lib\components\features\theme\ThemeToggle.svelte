<!-- src/lib/components/features/theme/ThemeToggle.svelte -->
<script lang="ts">
  import { mode,toggleMode } from 'mode-watcher';

  import { Button } from '$lib/components/ui/button';
  import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
  } from '$lib/components/ui/tooltip';

  // 使用 mode-watcher 的响应式 store
  // mode.current 提供当前主题值
  $: isDark = mode.current === 'dark';
</script>

<TooltipProvider>
  <Tooltip>
    <TooltipTrigger>
      <Button variant="ghost" size="icon" onclick={toggleMode} class="h-9 w-9">
        {#if isDark}
          <!-- 太阳图标 (浅色模式) -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
        {:else}
          <!-- 月亮图标 (深色模式) -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
            />
          </svg>
        {/if}
      </Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>{isDark ? '切换到浅色模式' : '切换到深色模式'}</p>
    </TooltipContent>
  </Tooltip>
</TooltipProvider>
