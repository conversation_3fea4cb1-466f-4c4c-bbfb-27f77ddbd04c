/**
 * 分页相关类型定义
 * 
 * @category Core Types
 */

/**
 * 分页参数
 */
export interface PaginationParams {
  /** 当前页码（从1开始） */
  page: number;
  /** 每页条数 */
  pageSize: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 分页响应
 * 
 * @template T 数据项类型
 */
export interface PaginatedResponse<T> {
  /** 数据项数组 */
  items: T[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页条数 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
}

/**
 * 分页状态
 */
export interface PaginationState {
  /** 当前页码 */
  currentPage: number;
  /** 每页条数 */
  itemsPerPage: number;
  /** 总记录数 */
  totalItems: number;
  /** 总页数 */
  totalPages: number;
  /** 是否有上一页 */
  hasPrevious: boolean;
  /** 是否有下一页 */
  hasNext: boolean;
}

/**
 * 排序配置
 */
export interface SortConfig {
  /** 排序字段 */
  field: string;
  /** 排序方向 */
  direction: 'asc' | 'desc';
  /** 是否启用排序 */
  enabled: boolean;
}

/**
 * 分页配置选项
 */
export interface PaginationOptions {
  /** 默认页码 */
  defaultPage?: number;
  /** 默认每页条数 */
  defaultPageSize?: number;
  /** 每页条数选项 */
  pageSizeOptions?: number[];
  /** 是否显示总数 */
  showTotal?: boolean;
  /** 是否显示页码跳转 */
  showQuickJumper?: boolean;
  /** 是否显示每页条数选择器 */
  showSizeChanger?: boolean;
}
