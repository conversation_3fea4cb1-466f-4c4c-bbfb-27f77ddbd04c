[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / QueryParams

# Interface: QueryParams

Defined in: src/lib/types/dataQuery.ts:11

## Properties

### endDate

> **endDate**: `string`

Defined in: src/lib/types/dataQuery.ts:14

---

### keyword

> **keyword**: `string`

Defined in: src/lib/types/dataQuery.ts:12

---

### startDate

> **startDate**: `string`

Defined in: src/lib/types/dataQuery.ts:13

---

### status

> **status**: `string`

Defined in: src/lib/types/dataQuery.ts:15

---

### type

> **type**: `string`

Defined in: src/lib/types/dataQuery.ts:16
