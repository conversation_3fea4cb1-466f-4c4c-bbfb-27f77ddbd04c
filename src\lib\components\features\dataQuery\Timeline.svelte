<script lang="ts">
  import { Separator } from '$lib/components/ui/separator';
  import type { MarketEvent, MarketEventType } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';
  import { EventCardFactory } from './cards';
  import { cardInteractionStore } from '$lib/stores/features/cardInteraction';
  import EventCardDetailModal from './cards/EventCardDetailModal.svelte';
  import { onMount } from 'svelte';

  interface Props {
    items: MarketEvent[];
  }

  const { items = [] }: Props = $props();

  // 滚动容器引用
  let headerScrollContainer: HTMLDivElement = $state()!;
  let bodyScrollContainer: HTMLDivElement = $state()!;

  // 获取所有唯一的市场事件类型
  const uniqueTypes: MarketEventType[] = $derived([...new Set(items.map((item) => item.marketEventType))].sort() as MarketEventType[]);

  // 判断是否需要横向滚动（基于实际内容宽度）
  let needsHorizontalScroll = $state(false);

  // 检查是否需要横向滚动并计算网格列宽
  function checkHorizontalScroll() {
    if (!headerScrollContainer) return;

    // 检查内容宽度是否超过容器宽度
    const containerWidth = headerScrollContainer.offsetWidth;
    const contentWidth = headerScrollContainer.scrollWidth;
    needsHorizontalScroll = contentWidth > containerWidth;

    // 计算合适的网格列配置
    updateGridColumns();
  }

  // 计算网格列配置
  let gridTemplateColumns = $state('');

  function updateGridColumns() {
    if (uniqueTypes.length === 0) {
      gridTemplateColumns = '';
      return;
    }

    const columnCount = uniqueTypes.length;

    // 如果容器还没准备好，使用默认配置
    if (!headerScrollContainer) {
      gridTemplateColumns = `repeat(${columnCount}, minmax(120px, 1fr))`;
      return;
    }

    const containerWidth = headerScrollContainer.offsetWidth;
    const gap = 12; // gap-3 = 12px
    const totalGapWidth = (columnCount - 1) * gap;
    const availableWidth = containerWidth - totalGapWidth;

    // 计算每列的理想宽度
    const idealColumnWidth = availableWidth / columnCount;

    // 设置最小宽度为 100px
    const minWidth = Math.max(100, Math.min(idealColumnWidth, 120));

    if (idealColumnWidth < 120) {
      // 容器太小，使用固定宽度并启用滚动
      gridTemplateColumns = `repeat(${columnCount}, ${minWidth}px)`;
    } else {
      // 容器足够大，使用弹性布局
      gridTemplateColumns = `repeat(${columnCount}, minmax(${minWidth}px, 1fr))`;
    }
  }

  // 滚动同步机制
  let isScrolling = false;

  function syncHeaderScroll() {
    if (isScrolling || !headerScrollContainer || !bodyScrollContainer) return;
    isScrolling = true;
    requestAnimationFrame(() => {
      bodyScrollContainer.scrollLeft = headerScrollContainer.scrollLeft;
      isScrolling = false;
    });
  }

  function syncBodyScroll() {
    if (isScrolling || !headerScrollContainer || !bodyScrollContainer) return;
    isScrolling = true;
    requestAnimationFrame(() => {
      headerScrollContainer.scrollLeft = bodyScrollContainer.scrollLeft;
      isScrolling = false;
    });
  }

  // 组件挂载后设置滚动监听和检查
  onMount(() => {
    if (headerScrollContainer && bodyScrollContainer) {
      headerScrollContainer.addEventListener('scroll', syncHeaderScroll);
      bodyScrollContainer.addEventListener('scroll', syncBodyScroll);

      // 初始检查是否需要横向滚动
      setTimeout(checkHorizontalScroll, 0);

      // 监听窗口大小变化
      const handleResize = () => {
        setTimeout(checkHorizontalScroll, 0);
      };
      window.addEventListener('resize', handleResize);

      return () => {
        headerScrollContainer?.removeEventListener('scroll', syncHeaderScroll);
        bodyScrollContainer?.removeEventListener('scroll', syncBodyScroll);
        window.removeEventListener('resize', handleResize);
      };
    }
  });

  // 监听数据变化，重新检查滚动需求
  $effect(() => {
    // 当 uniqueTypes 变化时，重新计算网格布局和滚动需求
    uniqueTypes;
    if (headerScrollContainer) {
      setTimeout(checkHorizontalScroll, 0);
    } else {
      // 即使容器还没准备好，也要计算初始网格配置
      updateGridColumns();
    }
  });

  // 处理卡片点击事件
  function handleCardClick(event: MarketEvent) {
    cardInteractionStore.showDetailModal(event);
  }

  // 处理模态框关闭
  function handleModalClose() {
    cardInteractionStore.hideDetailModal();
  }

  // 将Unix时间戳归并到15分钟间隔
  function roundToFifteenMinutes(timestamp: number): string {
    const date = new Date(timestamp * 1000); // 转换为毫秒
    const minutes = date.getMinutes();
    const roundedMinutes = Math.floor(minutes / 15) * 15;

    const roundedDate = new Date(date);
    roundedDate.setMinutes(roundedMinutes, 0, 0); // 设置分钟、秒、毫秒

    return roundedDate.toISOString();
  }

  // 按15分钟间隔分组数据
  const itemsByTimeSlot: Record<string, MarketEvent[]> = $derived(
    items.reduce(
      (acc, item) => {
        const timeSlot = roundToFifteenMinutes(item.date);
        if (!acc[timeSlot]) {
          acc[timeSlot] = [];
        }
        acc[timeSlot].push(item);
        return acc;
      },
      {} as Record<string, MarketEvent[]>
    )
  );

  // 获取所有15分钟时间间隔并排序
  const allTimeSlots: string[] = $derived(
    Object.keys(itemsByTimeSlot).sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
  );

  // 格式化时间显示（显示15分钟时间范围）
  function formatTime(dateString: string): string {
    const date = new Date(dateString);
    const endDate = new Date(date.getTime() + 15 * 60 * 1000); // 加15分钟

    const startTime = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    const endTime = endDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    return `${startTime}-${endTime}`;
  }

  // 格式化日期显示
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  }


</script>

<div class="w-full z-10">
  {#if uniqueTypes.length === 0}
    <!-- 空状态 -->
    <div class="flex flex-col items-center justify-center py-12 text-center">
      <div class="bg-muted mb-4 flex h-12 w-12 items-center justify-center rounded-full">
        <svg
          class="text-muted-foreground h-6 w-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
      </div>
      <p class="text-muted-foreground text-sm">暂无时间轴数据</p>
    </div>
  {:else}
    <!-- 表头：类型列标题 -->
    <div class="mb-4 flex sm:mb-6">
      <!-- 固定区域：时间列和时间轴占位 -->
      <div class="flex flex-shrink-0">
        <div class="w-20 sm:w-24"></div>
        <!-- 时间列占位 -->
        <div class="w-1"></div>
        <!-- 时间轴占位 -->
      </div>

      <!-- 表头滚动容器 -->
      <div
        bind:this={headerScrollContainer}
        class={`flex-1 pl-3 sm:pl-4 pb-2 w-full overflow-x-auto scrollbar-thin scrollbar-track-accent/30 scrollbar-thumb-muted-foreground/40 hover:scrollbar-thumb-muted-foreground/60 dark:scrollbar-track-accent/20 dark:scrollbar-thumb-muted-foreground/30 dark:hover:scrollbar-thumb-muted-foreground/50 scrollbar-track-rounded-full scrollbar-thumb-rounded-full min-w-0`}
      >
        <div
          class="grid gap-3 pt-2"
          style="grid-template-columns: {gridTemplateColumns};"
        >
          {#each uniqueTypes as type}
            <div class="text-center">
              <h3
                class="text-foreground bg-accent/30 border-border/20 rounded-lg border px-3 py-2 text-sm font-semibold whitespace-nowrap"
              >
                {MARKET_EVENT_TYPE_LABELS[type as keyof typeof MARKET_EVENT_TYPE_LABELS] || type}
              </h3>
            </div>
          {/each}
        </div>
      </div>
    </div>

    <!-- 表体滚动容器 -->
    <div
      bind:this={bodyScrollContainer}
      class="overflow-x-auto scrollbar-none w-full min-w-0"
      style={'scrollbar-width: none; -ms-overflow-style: none;'}
    >
      <!-- 时间行 -->
      {#each allTimeSlots as timeSlot}
        {@const timeSlotItems = itemsByTimeSlot[timeSlot] || []}
        {@const totalItemsInSlot = timeSlotItems.length}
        <div class="relative mb-4 sm:mb-6 flex" class:mb-6={totalItemsInSlot > 3} class:sm:mb-8={totalItemsInSlot > 3}>
          <!-- 时间显示（左侧） -->
          <div class="flex w-20 sm:w-24 flex-col justify-start pr-2 sm:pr-4 text-right flex-shrink-0">
            <div
              class="text-foreground bg-accent/20 mb-1 rounded px-2 py-1 font-mono text-xs font-medium"
            >
              {formatTime(timeSlot)}
            </div>
            <div class="text-muted-foreground mb-1 font-mono text-xs">
              {formatDate(timeSlot)}
            </div>
            <!-- 显示该时间段的总记录数 -->
            {#if totalItemsInSlot > 0}
              <div class="text-muted-foreground text-xs">
                {totalItemsInSlot} 条记录
              </div>
            {/if}
          </div>

          <!-- 时间轴节点 -->
          <div class="flex w-1 flex-shrink-0 items-start justify-center pt-2">
            <div
              class="bg-primary border-background dark:border-card flex size-4 items-center justify-center rounded-full border-2 shadow-sm"
              class:bg-primary={totalItemsInSlot > 0}
              class:bg-muted={totalItemsInSlot === 0}
            ></div>
          </div>

          <!-- 类型列 -->
          <div class="flex-1 pl-3 sm:pl-4">
            <div
              class="grid items-start gap-3 pt-2"
              style="grid-template-columns: {gridTemplateColumns};"
            >
              {#each uniqueTypes as type}
                {@const typeItems = (itemsByTimeSlot[timeSlot] || []).filter(
                  (item) => item.marketEventType === type
                )}
                <div class="flex min-h-[60px] flex-col space-y-2">
                  {#if typeItems.length > 0}
                    {#each typeItems as item (item.id)}
                      <EventCardFactory
                        event={item}
                        size="sm"
                        variant="compact"
                        useSpecificCard={true}
                        isHighlighted={$cardInteractionStore.selectedEventId === item.id}
                        onCardClick={handleCardClick}
                      />
                    {/each}
                  {/if}
                </div>
              {/each}
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<!-- 详细卡片模态框 -->
<EventCardDetailModal
  open={$cardInteractionStore.showDetailModal}
  event={$cardInteractionStore.selectedEvent}
  onClose={handleModalClose}
/>
