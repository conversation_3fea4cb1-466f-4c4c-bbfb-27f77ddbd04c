<!-- src/lib/components/charts/ChartPanel/ChartPanel.svelte -->
<script lang="ts">
  // shadcn-svelte 组件导入
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import type { ChartConfig, LiquidationChartConfig } from '$lib/types';

  // 统一的图表配置类型
  type UnifiedChartConfig = ChartConfig | LiquidationChartConfig;

  // Props
  export let chart: UnifiedChartConfig;
  export let isLoading: boolean = false;
  export let data: any = null;
  export const series: 'snapshot' | 'trend' | null = null;
  export let timeRangeLabel: string = '';

  // 功能开关
  export let enableDrag: boolean = false;
  export let showHeader: boolean = true;
  export let customHeight: string = '';

  // 事件回调
  export let onChartDrop: (params: { draggedId: string; targetId: string }) => void = () => {};
  export let onChartClick: (params: any) => void = () => {};

  // 计算动态标题
  $: formattedTitle = timeRangeLabel ? `[${timeRangeLabel}] ${chart.title}` : chart.title;

  // 计算组件Props
  $: componentProps = (() => {
    const props: Record<string, any> = {
      data: data,
      title: formattedTitle,
      subtitle: chart.subTitle,
      onChartClick: onChartClick,
    };

    // 如果是LiquidationChartConfig，添加额外属性
    if ('options' in chart && chart.options) {
      Object.assign(props, chart.options);
    }

    return props;
  })();

  // 拖拽功能
  function handleDragStart(event: DragEvent) {
    if (!enableDrag) return;
    event.dataTransfer?.setData('text/plain', chart.id);
  }

  function handleDragOver(event: DragEvent) {
    if (!enableDrag) return;
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  function handleDrop(event: DragEvent) {
    if (!enableDrag) return;
    event.preventDefault();
    const draggedId = event.dataTransfer?.getData('text/plain');
    if (draggedId && draggedId !== chart.id) {
      onChartDrop({ draggedId, targetId: chart.id });
    }
  }

  // 处理图表点击事件
  function handleChartClick(event: any) {
    if (event?.detail) {
      onChartClick(event.detail);
    } else {
      onChartClick(event);
    }
  }

  // 计算容器样式
  $: containerStyle = customHeight ? `height: ${customHeight}` : '';

  // 判断是否为特殊布局的图表
  $: isKpiChart = chart.id === 'kpi';
  $: isPieChart = chart.id === 'pie-long-short' || chart.id === 'pie-order-size';
  $: useCardLayout = !isKpiChart;
  $: cardHeight = isPieChart || customHeight ? customHeight || '450px' : '320px';
</script>

<div
  draggable={enableDrag}
  on:dragstart={handleDragStart}
  on:dragover={handleDragOver}
  on:drop={handleDrop}
  role="region"
  aria-label={`${chart.title} chart`}
  class="chart-panel-container"
>
  {#if isKpiChart}
    <!-- KPI指标卡直接渲染，不带任何额外容器 -->
    {#if isLoading}
      <div class="grid w-full grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        {#each Array(5) as _}
          <Card>
            <CardContent class="p-4">
              <Skeleton class="mb-2 h-4 w-20" />
              <Skeleton class="mb-1 h-8 w-24" />
              <Skeleton class="h-3 w-12" />
            </CardContent>
          </Card>
        {/each}
      </div>
    {:else if chart.component}
      <svelte:component this={chart.component} {...componentProps} />
    {/if}
  {:else if useCardLayout}
    <!-- 使用Card容器的图表 -->
    <Card class="relative mb-4 overflow-hidden">
      {#if showHeader}
        <CardHeader class="pb-3">
          <CardTitle class="text-base">
            {formattedTitle}
          </CardTitle>
          <CardDescription class="text-xs">
            {chart.subTitle}
          </CardDescription>
        </CardHeader>
      {/if}

      <CardContent
        class="pt-0 {showHeader ? '' : 'p-6'}"
        style="height: {cardHeight}; {containerStyle}"
      >
        <div class="h-full w-full">
          {#if isLoading}
            <div class="h-full space-y-4">
              {#if isPieChart}
                <div class="flex h-full items-center justify-center">
                  <div class="w-full max-w-sm space-y-4">
                    <Skeleton class="mx-auto h-4 w-32" />
                    <Skeleton class="mx-auto h-64 w-64 rounded-full" />
                    <div class="space-y-2">
                      <Skeleton class="mx-auto h-3 w-24" />
                      <Skeleton class="mx-auto h-3 w-20" />
                    </div>
                  </div>
                </div>
              {:else}
                <Skeleton class="h-4 w-48" />
                <Skeleton class="h-full w-full" />
              {/if}
            </div>
          {:else if chart.component}
            <svelte:component this={chart.component} {...componentProps} />
          {/if}
        </div>
      </CardContent>
    </Card>
  {/if}
</div>

<style>
  .chart-panel-container {
    transition: transform 0.2s ease-in-out;
  }

  .chart-panel-container:hover {
    transform: translateY(-1px);
  }

  .chart-panel-container[draggable='true'] {
    cursor: grab;
  }

  .chart-panel-container[draggable='true']:active {
    cursor: grabbing;
  }
</style>
