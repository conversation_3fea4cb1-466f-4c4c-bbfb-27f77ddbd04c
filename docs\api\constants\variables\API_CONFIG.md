[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / API_CONFIG

# Variable: API_CONFIG

> `const` **API_CONFIG**: `object`

Defined in: src/lib/constants/api.ts:45

## Type declaration

### BASE_URL

> `readonly` **BASE_URL**: `any`

### RETRY_ATTEMPTS

> `readonly` **RETRY_ATTEMPTS**: `3` = `3`

### RETRY_DELAY

> `readonly` **RETRY_DELAY**: `1000` = `1000`

### TIMEOUT

> `readonly` **TIMEOUT**: `10000` = `10000`
