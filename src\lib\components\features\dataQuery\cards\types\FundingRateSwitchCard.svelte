<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  import CurrencyIcon from '$lib/components/features/CurrencyIcon.svelte';

  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    formatFundingRate,
    formatTimeDiff,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取资金费率切换相关参数
  const pair = getParameterValue(event, 'pair') as string;
  const base = getParameterValue(event, 'base') as string;
  const fundingRate = getParameterValue(event, 'fundingRate') as number;
  const previousFundingRate = getParameterValue(event, 'previousFundingRate') as number;
  const daysSince = getParameterValue(event, 'daysSince') as number;

  // 获取费率方向和变化
  const isPositive = fundingRate > 0;
  const wasPositive = previousFundingRate > 0;
  const previousDirection = wasPositive ? '正费率' : '负费率';

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3">
    <!-- 主要信息行 - 横向布局 -->
    <div class="flex items-start space-x-3">
      <!-- 左侧：加密货币图标 -->
      <div class="flex-shrink-0">
        <CurrencyIcon
          currencyId={event.currency}
          symbol={base}
          size="size-8"
          class="mt-0.5"
        />
      </div>

      <!-- 中间：主要内容 -->
      <div class="flex-1 min-w-0">
        <!-- 第一行：状态标题 -->
        <div class="flex items-center space-x-2 mb-1">
          <span class="text-foreground font-medium text-sm">
            状态
          </span>
          <span class="text-muted-foreground text-sm">
            {base || '未知'}/{pair?.split('/')[1] || 'USDT'}
          </span>
          <span class="text-muted-foreground text-xs">
            • {formatTimeDiff(event.date)}
          </span>
        </div>

        <!-- 第二行：资金费率状态描述 -->
        <div class="text-sm text-foreground leading-relaxed flex items-center flex-wrap gap-1">
          <span>资金费率现在为</span>
          <Badge
            variant={isPositive ? 'default' : 'destructive'}
            class="text-xs px-1.5 py-0.5"
          >
            {isPositive ? '⚡' : '📉'} {formatFundingRate(fundingRate)}
          </Badge>
          <span>每日，之前为{previousDirection} {daysSince}天</span>
        </div>
      </div>
    </div>
  </CardContent>
</Card>
