[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / PaginatedResponse

# Interface: PaginatedResponse\<T\>

Defined in: src/lib/types/api.ts:35

分页响应

## Type Parameters

### T

`T`

## Properties

### items

> **items**: `T`[]

Defined in: src/lib/types/api.ts:36

---

### page

> **page**: `number`

Defined in: src/lib/types/api.ts:38

---

### pageSize

> **pageSize**: `number`

Defined in: src/lib/types/api.ts:39

---

### total

> **total**: `number`

Defined in: src/lib/types/api.ts:37

---

### totalPages

> **totalPages**: `number`

Defined in: src/lib/types/api.ts:40
