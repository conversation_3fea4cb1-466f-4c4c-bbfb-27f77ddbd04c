# 主题切换功能文档

## 📋 概述

本项目采用 shadcn-svelte 标准方案实现浅色/深色模式切换功能，使用 mode-watcher 库进行主题管理，确保用户偏好设置的持久化存储和 ECharts 图表主题的兼容性。

## 🏗️ 技术架构

### 核心依赖

- **mode-watcher**: shadcn-svelte 推荐的主题管理库
- **ModeWatcher 组件**: 自动检测和应用主题
- **toggleMode API**: 主题切换函数
- **mode.current**: 当前主题状态

### 架构组件

```text
主题切换系统
├── ModeWatcher (根布局)          # 主题管理核心组件
├── ThemeToggle (UI组件)          # 主题切换按钮
├── chartColors.ts (工具)         # 图表主题适配
└── createThemeObserver (观察器)  # 图表主题监听
```

## 🎯 功能特性

### 1. 自动主题检测
- 检测系统主题偏好
- 自动应用对应主题
- 支持系统主题变化跟踪

### 2. 用户偏好持久化
- localStorage 自动存储
- 页面刷新后保持设置
- 跨标签页同步

### 3. ECharts 图表主题兼容
- 自动适配图表颜色
- 主题切换时实时更新
- 支持多种图表类型

### 4. 响应式主题切换
- 即时切换效果
- 平滑过渡动画
- 全局状态同步

## 🔧 实现细节

### 根布局集成

```svelte
<!-- src/routes/+layout.svelte -->
<script lang="ts">
  import { ModeWatcher } from 'mode-watcher';
</script>

<!-- ModeWatcher 组件 - 管理主题切换 -->
<ModeWatcher />
```

### 主题切换组件

```svelte
<!-- src/lib/components/features/theme/ThemeToggle.svelte -->
<script lang="ts">
  import { toggleMode, mode } from 'mode-watcher';
  import { Button } from '$lib/components/ui/button';

  // 使用 mode-watcher 的响应式状态
  $: isDark = mode.current === 'dark';
</script>

<Button onclick={toggleMode}>
  {#if isDark}
    <!-- 太阳图标 (切换到浅色模式) -->
  {:else}
    <!-- 月亮图标 (切换到深色模式) -->
  {/if}
</Button>
```

### 图表主题适配

```typescript
// src/lib/utils/chartColors.ts
import { mode } from 'mode-watcher';

/**
 * 检测当前是否为暗色主题
 */
function isDarkTheme(): boolean {
  if (typeof window === 'undefined') return false;
  return mode.current === 'dark';
}

/**
 * 监听主题变化并更新图表
 */
export function createThemeObserver(callback: () => void): (() => void) {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const target = mutation.target as HTMLElement;
        if (target === document.documentElement) {
          const currentMode = mode.current;
          console.log('Theme change detected:', currentMode);
          setTimeout(callback, 50);
        }
      }
    });
  });

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  });

  return () => observer.disconnect();
}
```

## 🎨 主题配置

### CSS 变量定义

```css
/* src/app.css */
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.13 0.028 261.692);
  /* ... 其他浅色主题变量 */
}

.dark {
  --background: oklch(0.13 0.028 261.692);
  --foreground: oklch(0.985 0.002 247.839);
  /* ... 其他深色主题变量 */
}
```

### 图表颜色配置

```typescript
function getThemeColors() {
  const isDark = isDarkTheme();

  if (isDark) {
    // 暗色主题颜色 - 更亮更鲜艳
    return {
      chart1: '#60a5fa', // 亮蓝色
      chart2: '#34d399', // 亮绿色
      chart3: '#fbbf24', // 亮黄色
      chart4: '#f87171', // 亮红色
      chart5: '#c084fc', // 亮紫色
    };
  } else {
    // 浅色主题颜色 - 更深更沉稳
    return {
      chart1: '#2563eb', // 深蓝色
      chart2: '#059669', // 深绿色
      chart3: '#d97706', // 深黄色
      chart4: '#dc2626', // 深红色
      chart5: '#7c3aed', // 深紫色
    };
  }
}
```

## 🔄 重构历程

### 重构前（自定义实现）
- 手动 localStorage 操作
- 直接 DOM 类操作
- 自定义主题检测逻辑
- 分散的主题管理代码

### 重构后（shadcn-svelte 标准）
- mode-watcher 统一管理
- 标准化 API 调用
- 自动主题检测和持久化
- 集中化主题配置

### 重构优势
1. **标准化**: 使用 shadcn-svelte 推荐方案
2. **可维护性**: 代码更简洁，逻辑更清晰
3. **功能完整性**: 保持所有原有功能
4. **兼容性**: 完美支持 ECharts 图表主题
5. **类型安全**: TypeScript 类型支持

## 🧪 测试验证

### 功能测试清单
- [ ] 主题切换按钮正常工作
- [ ] 系统主题偏好自动检测
- [ ] 用户设置持久化存储
- [ ] 页面刷新后主题保持
- [ ] ECharts 图表主题同步
- [ ] 跨页面主题状态一致

### 测试场景
1. **基础切换**: 点击按钮切换主题
2. **持久化**: 刷新页面验证主题保持
3. **图表适配**: 切换主题时图表颜色更新
4. **系统同步**: 系统主题变化时自动跟随

## 📝 使用指南

### 开发者使用

```typescript
// 在组件中使用主题状态
import { mode } from 'mode-watcher';

// 检查当前主题
const isDark = mode.current === 'dark';

// 切换主题
import { toggleMode } from 'mode-watcher';
toggleMode();

// 设置特定主题
import { setMode } from 'mode-watcher';
setMode('dark'); // 或 'light'
```

### 图表组件集成

```typescript
// 在图表组件中监听主题变化
import { createThemeObserver } from '$lib/utils/chartColors';

let themeUnsubscribe: (() => void) | null = null;

onMount(() => {
  themeUnsubscribe = createThemeObserver(() => {
    if (myChart && chartInitialized) {
      updateChart();
    }
  });
});

onDestroy(() => {
  if (themeUnsubscribe) {
    themeUnsubscribe();
  }
});
```

## 🔮 未来扩展

### 可能的增强功能
1. **多主题支持**: 除了深浅色，支持更多主题变体
2. **自定义主题**: 允许用户自定义颜色配置
3. **主题预览**: 实时预览主题效果
4. **动画增强**: 更丰富的主题切换动画

### 技术优化
1. **性能优化**: 减少主题切换时的重绘
2. **缓存机制**: 缓存主题相关的计算结果
3. **懒加载**: 按需加载主题资源
