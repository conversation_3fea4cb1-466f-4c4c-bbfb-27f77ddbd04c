[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / TIME_RANGES

# Variable: TIME_RANGES

> `const` **TIME_RANGES**: `object`

Defined in: src/lib/constants/time.ts:10

## Type declaration

### DASHBOARD

> `readonly` **DASHBOARD**: readonly \[\{ `label`: `"最近1小时"`; `value`: `"hour"`; \}, \{ `label`: `"最近24小时"`; `value`: `"day"`; \}, \{ `label`: `"最近7天"`; `value`: `"week"`; \}, \{ `label`: `"最近30天"`; `value`: `"month"`; \}, \{ `label`: `"自定义"`; `value`: `"custom"`; \}\]

### DATA_QUERY

> `readonly` **DATA_QUERY**: readonly \[\{ `label`: `"今天"`; `value`: `"today"`; \}, \{ `label`: `"昨天"`; `value`: `"yesterday"`; \}, \{ `label`: `"最近7天"`; `value`: `"week"`; \}, \{ `label`: `"最近30天"`; `value`: `"month"`; \}, \{ `label`: `"自定义范围"`; `value`: `"custom"`; \}\]
