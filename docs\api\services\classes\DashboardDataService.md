[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [services](../README.md) / DashboardDataService

# Class: DashboardDataService

Defined in: src/lib/services/data/dashboard.ts:42

仪表板数据服务类

提供仪表板相关的数据获取和处理功能，包括图表数据和统计信息。

## Example

```typescript
import { dashboardDataService } from '$lib/services/data/dashboard';

// 获取所有图表数据
const response = await dashboardDataService.fetchAllChartData('2024-01-01', '2024-01-31', 'UTC');

if (response.status === 'success') {
  console.log('图表数据:', response.data);
}
```

## Constructors

### Constructor

> **new DashboardDataService**(): `DashboardDataService`

Defined in: src/lib/services/data/dashboard.ts:45

#### Returns

`DashboardDataService`

## Methods

### fetchAllChartData()

> **fetchAllChartData**(`startTime`, `endTime`, `selectedTimeZone`): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<`AllChartData`\>\>

Defined in: src/lib/services/data/dashboard.ts:83

获取所有图表数据

获取仪表板中所有图表的数据，包括柱状图、折线图、饼图和散点图。
当 API 调用失败时，会自动返回模拟数据以确保应用可用性。

#### Parameters

##### startTime

`string`

开始时间，格式为 ISO 字符串

##### endTime

`string`

结束时间，格式为 ISO 字符串

##### selectedTimeZone

`string`

时区，如 'UTC', 'Asia/Shanghai'

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<`AllChartData`\>\>

Promise 包含所有图表数据的响应

#### Example

```typescript
const response = await dashboardDataService.fetchAllChartData(
  '2024-01-01T00:00:00Z',
  '2024-01-31T23:59:59Z',
  'UTC'
);

if (response.status === 'success') {
  const { barChartData, lineChartData, pieChartData, scatterChartData } = response.data;
  // 处理图表数据
}
```

---

### fetchDashboardStats()

> **fetchDashboardStats**(`startTime`, `endTime`, `selectedTimeZone`): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`DashboardStats`](../../types/interfaces/DashboardStats.md)\>\>

Defined in: src/lib/services/data/dashboard.ts:151

获取仪表板统计数据

获取仪表板的关键统计指标，如总用户数、月收入、转化率等。

#### Parameters

##### startTime

`string`

统计开始时间

##### endTime

`string`

统计结束时间

##### selectedTimeZone

`string`

时区设置

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`DashboardStats`](../../types/interfaces/DashboardStats.md)\>\>

Promise 包含统计数据的响应

#### Example

```typescript
const statsResponse = await dashboardDataService.fetchDashboardStats(
  '2024-01-01T00:00:00Z',
  '2024-01-31T23:59:59Z',
  'UTC'
);

if (statsResponse.status === 'success') {
  const { totalUsers, monthlyRevenue, conversionRate } = statsResponse.data;
  console.log(`总用户数: ${totalUsers}`);
}
```
