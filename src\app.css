@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.13 0.028 261.692);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.13 0.028 261.692);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.13 0.028 261.692);
  --primary: oklch(0.21 0.034 264.665);
  --primary-foreground: oklch(0.985 0.002 247.839);
  --secondary: oklch(0.967 0.003 264.542);
  --secondary-foreground: oklch(0.21 0.034 264.665);
  --muted: oklch(0.967 0.003 264.542);
  --muted-foreground: oklch(0.551 0.027 264.364);
  --accent: oklch(0.967 0.003 264.542);
  --accent-foreground: oklch(0.21 0.034 264.665);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.928 0.006 264.531);
  --input: oklch(0.928 0.006 264.531);
  --ring: oklch(0.707 0.022 261.325);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.002 247.839);
  --sidebar-foreground: oklch(0.13 0.028 261.692);
  --sidebar-primary: oklch(0.21 0.034 264.665);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.967 0.003 264.542);
  --sidebar-accent-foreground: oklch(0.21 0.034 264.665);
  --sidebar-border: oklch(0.928 0.006 264.531);
  --sidebar-ring: oklch(0.707 0.022 261.325);
  --success: oklch(0.647 0.156 154.8);
  --success-foreground: oklch(0.985 0.002 247.839);

  /* Message type colors */
  --message-info: oklch(0.6 0.118 184.704);
  --message-info-foreground: oklch(0.13 0.028 261.692);
  --message-info-background: oklch(0.967 0.003 264.542);
  --message-info-border: oklch(0.928 0.006 264.531);

  --message-success: oklch(0.647 0.156 154.8);
  --message-success-foreground: oklch(0.13 0.028 261.692);
  --message-success-background: oklch(0.967 0.003 264.542);
  --message-success-border: oklch(0.928 0.006 264.531);

  --message-warning: oklch(0.828 0.189 84.429);
  --message-warning-foreground: oklch(0.13 0.028 261.692);
  --message-warning-background: oklch(0.967 0.003 264.542);
  --message-warning-border: oklch(0.928 0.006 264.531);

  --message-error: oklch(0.577 0.245 27.325);
  --message-error-foreground: oklch(0.985 0.002 247.839);
  --message-error-background: oklch(0.967 0.003 264.542);
  --message-error-border: oklch(0.928 0.006 264.531);

  --message-notification: oklch(0.769 0.188 70.08);
  --message-notification-foreground: oklch(0.13 0.028 261.692);
  --message-notification-background: oklch(0.967 0.003 264.542);
  --message-notification-border: oklch(0.928 0.006 264.531);
}

.dark {
  --background: oklch(0.13 0.028 261.692);
  --foreground: oklch(0.985 0.002 247.839);
  --card: oklch(0.21 0.034 264.665);
  --card-foreground: oklch(0.985 0.002 247.839);
  --popover: oklch(0.21 0.034 264.665);
  --popover-foreground: oklch(0.985 0.002 247.839);
  --primary: oklch(0.928 0.006 264.531);
  --primary-foreground: oklch(0.21 0.034 264.665);
  --secondary: oklch(0.278 0.033 256.848);
  --secondary-foreground: oklch(0.985 0.002 247.839);
  --muted: oklch(0.278 0.033 256.848);
  --muted-foreground: oklch(0.707 0.022 261.325);
  --accent: oklch(0.278 0.033 256.848);
  --accent-foreground: oklch(0.985 0.002 247.839);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.034 264.665);
  --sidebar-foreground: oklch(0.985 0.002 247.839);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.278 0.033 256.848);
  --sidebar-accent-foreground: oklch(0.985 0.002 247.839);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
  --success: oklch(0.696 0.17 162.48);
  --success-foreground: oklch(0.13 0.028 261.692);

  /* Message type colors - dark theme */
  --message-info: oklch(0.696 0.17 162.48);
  --message-info-foreground: oklch(0.985 0.002 247.839);
  --message-info-background: oklch(0.278 0.033 256.848);
  --message-info-border: oklch(1 0 0 / 10%);

  --message-success: oklch(0.696 0.17 162.48);
  --message-success-foreground: oklch(0.985 0.002 247.839);
  --message-success-background: oklch(0.278 0.033 256.848);
  --message-success-border: oklch(1 0 0 / 10%);

  --message-warning: oklch(0.769 0.188 70.08);
  --message-warning-foreground: oklch(0.985 0.002 247.839);
  --message-warning-background: oklch(0.278 0.033 256.848);
  --message-warning-border: oklch(1 0 0 / 10%);

  --message-error: oklch(0.704 0.191 22.216);
  --message-error-foreground: oklch(0.985 0.002 247.839);
  --message-error-background: oklch(0.278 0.033 256.848);
  --message-error-border: oklch(1 0 0 / 10%);

  --message-notification: oklch(0.627 0.265 303.9);
  --message-notification-foreground: oklch(0.985 0.002 247.839);
  --message-notification-background: oklch(0.278 0.033 256.848);
  --message-notification-border: oklch(1 0 0 / 10%);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);

  /* Message type color mappings */
  --color-message-info: var(--message-info);
  --color-message-info-foreground: var(--message-info-foreground);
  --color-message-info-background: var(--message-info-background);
  --color-message-info-border: var(--message-info-border);

  --color-message-success: var(--message-success);
  --color-message-success-foreground: var(--message-success-foreground);
  --color-message-success-background: var(--message-success-background);
  --color-message-success-border: var(--message-success-border);

  --color-message-warning: var(--message-warning);
  --color-message-warning-foreground: var(--message-warning-foreground);
  --color-message-warning-background: var(--message-warning-background);
  --color-message-warning-border: var(--message-warning-border);

  --color-message-error: var(--message-error);
  --color-message-error-foreground: var(--message-error-foreground);
  --color-message-error-background: var(--message-error-background);
  --color-message-error-border: var(--message-error-border);

  --color-message-notification: var(--message-notification);
  --color-message-notification-foreground: var(--message-notification-foreground);
  --color-message-notification-background: var(--message-notification-background);
  --color-message-notification-border: var(--message-notification-border);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Message type card styles */
  .message-card-info {
    @apply border-message-info-border bg-message-info-background text-message-info-foreground;
  }

  .message-card-success {
    @apply border-message-success-border bg-message-success-background text-message-success-foreground;
  }

  .message-card-warning {
    @apply border-message-warning-border bg-message-warning-background text-message-warning-foreground;
  }

  .message-card-error {
    @apply border-message-error-border bg-message-error-background text-message-error-foreground;
  }

  .message-card-notification {
    @apply border-message-notification-border bg-message-notification-background text-message-notification-foreground;
  }

  /* Message icon styles */
  .message-icon-info {
    @apply text-message-info;
  }

  .message-icon-success {
    @apply text-message-success;
  }

  .message-icon-warning {
    @apply text-message-warning;
  }

  .message-icon-error {
    @apply text-message-error;
  }

  .message-icon-notification {
    @apply text-message-notification;
  }

  /* Common layout patterns */
  .flex-center {
    display: flex;
    align-items: center;
  }

  .flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  /* Badge styles */
  .badge-small {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
  }

  /* Text styles */
  .text-small {
    font-size: 0.75rem;
  }

  .text-small-muted {
    font-size: 0.75rem;
    opacity: 0.7;
  }

  /* Tooltip content styles */
  .tooltip-content-small {
    max-width: 20rem;
    font-size: 0.75rem;
  }

  .tooltip-content-medium {
    max-width: 24rem;
    font-size: 0.75rem;
    white-space: pre-wrap;
  }

  /* Message card styles */
  .message-title {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25;
    cursor: help;
    display: block;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .message-content {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    line-height: 1.625;
    cursor: help;
    display: block;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}

