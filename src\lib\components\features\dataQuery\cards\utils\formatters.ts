// 格式化金额为USD显示
export function formatUsdAmount(value: number | string | boolean | undefined): string {
  if (value === undefined || value === null || typeof value === 'boolean') return 'N/A';

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';
  
  // 根据金额大小选择合适的格式
  if (numValue >= 1000000) {
    return `$${(numValue / 1000000).toFixed(2)}M`;
  } else if (numValue >= 1000) {
    return `$${(numValue / 1000).toFixed(2)}K`;
  } else {
    return `$${numValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }
}

// 格式化数量
export function formatAmount(value: number | string | undefined): string {
  if (value === undefined || value === null) return 'N/A';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';
  
  if (numValue >= 1000000) {
    return `${(numValue / 1000000).toFixed(2)}M`;
  } else if (numValue >= 1000) {
    return `${(numValue / 1000).toFixed(2)}K`;
  } else {
    return numValue.toLocaleString('en-US', { maximumFractionDigits: 4 });
  }
}

// 格式化百分比
export function formatPercentage(value: number | string | undefined): string {
  if (value === undefined || value === null) return 'N/A';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';
  
  return `${numValue.toFixed(2)}%`;
}

// 格式化价格
export function formatPrice(value: number | string | undefined): string {
  if (value === undefined || value === null) return 'N/A';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';
  
  if (numValue < 0.01) {
    return numValue.toFixed(6);
  } else if (numValue < 1) {
    return numValue.toFixed(4);
  } else {
    return numValue.toFixed(2);
  }
}

// 格式化资金费率
export function formatFundingRate(value: number | string | undefined): string {
  if (value === undefined || value === null) return 'N/A';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';
  
  return `${(numValue * 100).toFixed(4)}%`;
}

// 格式化持续时间
export function formatDuration(value: number | string | undefined, unit: 'minutes' | 'hours' | 'days' = 'minutes'): string {
  if (value === undefined || value === null) return 'N/A';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';
  
  switch (unit) {
    case 'minutes':
      if (numValue >= 60) {
        const hours = Math.floor(numValue / 60);
        const minutes = numValue % 60;
        return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
      }
      return `${numValue}分钟`;
    case 'hours':
      if (numValue >= 24) {
        const days = Math.floor(numValue / 24);
        const hours = numValue % 24;
        return hours > 0 ? `${days}天${hours}小时` : `${days}天`;
      }
      return `${numValue}小时`;
    case 'days':
      return `${numValue}天`;
    default:
      return `${numValue}`;
  }
}

// 格式化地址（显示前6位和后4位）
export function formatAddress(address: string | undefined): string {
  if (!address || typeof address !== 'string') return 'N/A';
  
  if (address.length <= 10) return address;
  
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

// 格式化交易哈希
export function formatTxHash(hash: string | undefined): string {
  if (!hash || typeof hash !== 'string') return 'N/A';
  
  if (hash.length <= 16) return hash;
  
  return `${hash.slice(0, 8)}...${hash.slice(-8)}`;
}

// 格式化时间差
export function formatTimeDiff(timestamp: number): string {
  const now = Date.now();
  const eventTime = timestamp * 1000; // 转换为毫秒
  const diff = now - eventTime;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return '刚刚';
  }
}

// 格式化阈值
export function formatThreshold(value: number | string | undefined): string {
  if (value === undefined || value === null) return 'N/A';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';
  
  if (numValue === -1) return '极端';
  if (numValue === 0) return '无阈值';
  
  return `阈值 ${numValue}`;
}

// 格式化布尔值
export function formatBoolean(value: boolean | string | undefined, trueText: string = '是', falseText: string = '否'): string {
  if (value === undefined || value === null) return 'N/A';
  
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true' ? trueText : falseText;
  }
  
  return value ? trueText : falseText;
}
