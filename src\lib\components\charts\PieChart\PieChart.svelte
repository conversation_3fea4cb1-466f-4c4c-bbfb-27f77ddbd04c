<script lang="ts">
  import type { EChartsOption, PieSeriesOption } from 'echarts';
  import * as echarts from 'echarts';
  import { createEventDispatcher, onDestroy, onMount } from 'svelte';

  import { createThemeObserver,getChartColorMap, getDefaultColors } from '$lib/utils/chartColors';
  import type { PieChartDataItem } from '$lib/utils/chartDataAdapter';
  import { formatNumber } from '$lib/utils/formatters';

  const dispatch = createEventDispatcher();

  export let options: EChartsOption = {};
  export let data: PieChartDataItem[] = [];
  export let title: string = 'Product Category Distribution';
  export let subtitle: string = '';
  export let isDonut: boolean = false; // 是否显示为环形图
  export let labelPosition: 'inside' | 'outside' = 'outside'; // 标签位置

  let chartDom: HTMLDivElement;
  let myChart: echarts.ECharts;
  let themeUnsubscribe: (() => void) | null = null;

  const baseChartOptions: EChartsOption = {
    title: {
      text: title,
      subtext: subtitle,
      left: 'center',
      top: '5%',
      textStyle: {
        fontSize: 14,
      },
      subtextStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        const { name, value, percent } = params;
        return `${name}: ${formatNumber(value, 2)} (${percent}%)`;
      },
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      left: 'center',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12,
      },
    },
    series: [],
  };

  let chartInitialized = false;

  function initializeChart() {
    if (chartInitialized) return;
    myChart = echarts.init(chartDom);
    updateChart();
    chartInitialized = true;

    myChart.on('click', (params) => {
      dispatch('chartClick', {
        chartType: 'pie',
        name: params.name,
        value: params.value,
        percent: params.percent,
      });
    });
  }

  function updateChart() {
    if (!myChart) return;

    // 使用主题感知的颜色映射
    const colorMap = getChartColorMap();
    const defaultColors = getDefaultColors();

    // 根据数据名称设置颜色，如果没有匹配则使用默认颜色
    const colors: string[] = data.map(
      (item, index) => colorMap[item.name] || defaultColors[index % defaultColors.length]
    );

    const pieSeries: PieSeriesOption = {
      name: title,
      type: 'pie',
      radius: isDonut ? ['40%', '70%'] : '70%',
      center: ['50%', '50%'],
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 4,
        borderColor: '#fff',
        borderWidth: 1,
      },
      label: {
        show: true,
        position: labelPosition,
        formatter: '{b}: {d}%',
      },
      labelLine: {
        show: labelPosition === 'outside',
      },
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)',
          fontSize: 14,
          fontWeight: 'bold',
        },
      },
    };

    myChart.setOption(
      {
        ...baseChartOptions,
        ...options,
        color: colors, // 总是应用颜色数组，因为我们确保它总是有值
        title: {
          ...baseChartOptions.title,
          text: title,
          subtext: subtitle,
        },
        series: [pieSeries],
      },
      { notMerge: false }
    );
  }

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            initializeChart();
            observer.unobserve(chartDom);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartDom);

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    // 监听主题变化
    themeUnsubscribe = createThemeObserver(() => {
      if (myChart && chartInitialized) {
        updateChart();
      }
    });

    window.addEventListener('resize', resizeChart);

    onDestroy(() => {
      window.removeEventListener('resize', resizeChart);
      if (themeUnsubscribe) {
        themeUnsubscribe();
      }
      if (updateTimer) {
        clearTimeout(updateTimer);
      }
      if (myChart) {
        myChart.dispose();
      }
    });
  });

  // 优化响应式更新，添加防抖和条件检查
  let updateTimer: number | null = null;
  let lastDataHash = '';

  // 计算数据哈希值，避免不必要的更新
  function getDataHash(
    data: any,
    options: any,
    title: string,
    subtitle: string,
    isDonut: boolean,
    labelPosition: string
  ): string {
    return JSON.stringify({
      dataLength: Array.isArray(data) ? data.length : 0,
      dataFirst: Array.isArray(data) && data.length > 0 ? data[0] : null,
      options,
      title,
      subtitle,
      isDonut,
      labelPosition,
    });
  }

  $: {
    if (myChart && chartInitialized) {
      const currentHash = getDataHash(data, options, title, subtitle, isDonut, labelPosition);

      // 只有当数据真正发生变化时才更新
      if (currentHash !== lastDataHash) {
        if (updateTimer) {
          clearTimeout(updateTimer);
        }

        updateTimer = window.setTimeout(() => {
          try {
            updateChart();
            lastDataHash = currentHash;
          } catch (error) {
            console.error('PieChart update error:', error);
          }
          updateTimer = null;
        }, 50); // 50ms 防抖
      }
    }
  }
</script>

<div bind:this={chartDom} class="h-full w-full" aria-label="{title} - {subtitle}"></div>
