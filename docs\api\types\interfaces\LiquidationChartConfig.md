[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / LiquidationChartConfig

# Interface: LiquidationChartConfig

Defined in: src/lib/types/liquidation.ts:39

## Properties

### component

> **component**: `Component`\<`any`, `any`, `any`\>

Defined in: src/lib/types/liquidation.ts:41

---

### groupBy?

> `optional` **groupBy**: `null` \| `"side"` \| `"coinType"`

Defined in: src/lib/types/liquidation.ts:48

---

### id

> **id**: `string`

Defined in: src/lib/types/liquidation.ts:40

---

### options?

> `optional` **options**: `Record`\<`string`, `any`\>

Defined in: src/lib/types/liquidation.ts:47

---

### order

> **order**: `number`

Defined in: src/lib/types/liquidation.ts:43

---

### series?

> `optional` **series**: `string`

Defined in: src/lib/types/liquidation.ts:46

---

### subTitle

> **subTitle**: `string`

Defined in: src/lib/types/liquidation.ts:45

---

### title

> **title**: `string`

Defined in: src/lib/types/liquidation.ts:44

---

### viewMode?

> `optional` **viewMode**: [`TrendViewMode`](../type-aliases/TrendViewMode.md)

Defined in: src/lib/types/liquidation.ts:49

---

### visible

> **visible**: `boolean`

Defined in: src/lib/types/liquidation.ts:42
