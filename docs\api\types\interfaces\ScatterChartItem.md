[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / ScatterChartItem

# Interface: ScatterChartItem

Defined in: src/lib/types/dashboard.ts:76

散点图数据项

## Example

```typescript
const scatterData: ScatterChartItem = {
  name: 'Point 1',
  x: 10.5,
  y: 20.3,
  category: 'A',
};
```

## Properties

### category

> **category**: `string`

Defined in: src/lib/types/dashboard.ts:84

数据点分类，用于区分不同系列

---

### name

> **name**: `string`

Defined in: src/lib/types/dashboard.ts:78

数据点名称

---

### x

> **x**: `number`

Defined in: src/lib/types/dashboard.ts:80

X 轴坐标值

---

### y

> **y**: `number`

Defined in: src/lib/types/dashboard.ts:82

Y 轴坐标值
