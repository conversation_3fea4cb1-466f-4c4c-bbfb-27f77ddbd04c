<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }
  import {
    getDirectionText,
    getMarketTypeText,
    getStatusDisplay,
    formatUsdAmount,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取TWAP检测相关参数 - 只保留必要的基础信息
  const amountUsd = getParameterValue(event, 'amountUsd') as number;
  const side = getParameterValue(event, 'side') as number;
  const market = getParameterValue(event, 'market') as number;
  const status = getParameterValue(event, 'status') as string;
  const base = getParameterValue(event, 'base') as string;

  // 获取状态显示
  const statusDisplay = getStatusDisplay(status);

  // 获取方向和市场类型文本
  const sideText = getDirectionText(side, 'side');
  const marketText = getMarketTypeText(market);

  // 状态对应的Badge变体
  const statusBadgeVariant = status === 'ongoing' ? 'default' :
                           status === 'started' ? 'secondary' :
                           status === 'expired' ? 'destructive' : 'outline';

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <!-- 简化的卡片内容 -->
  <CardContent class="p-3">
    <!-- 主要信息行 -->
    <div class="flex items-center justify-between mb-2">
      <!-- 左侧：币种和市场类型 -->
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <!-- 币种 -->
        <span class="text-foreground font-semibold text-sm">
          {base || '未知'}
        </span>

        <!-- 市场类型 -->
        <span class="text-muted-foreground text-xs">
          {marketText}
        </span>
      </div>

      <!-- 右侧：状态 -->
      <Badge variant={statusBadgeVariant} class="text-xs flex-shrink-0">
        {statusDisplay}
      </Badge>
    </div>

    <!-- 方向和关键数据 -->
    <div class="grid grid-cols-2 gap-2 text-xs">
      <!-- 交易方向 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">交易方向</div>
        <div class="text-foreground font-medium">
          {sideText}
        </div>
      </div>

      <!-- 交易金额（关键数据） -->
      <div class="space-y-1">
        <div class="text-muted-foreground">交易金额</div>
        <div class="text-foreground font-semibold">
          {formatUsdAmount(amountUsd)}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
