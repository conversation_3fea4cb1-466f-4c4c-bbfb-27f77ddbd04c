# 时区设置功能文档

## 📋 概述

本项目实现了完整的时区设置功能，允许用户选择首选时区，影响所有时间显示。该功能采用现代化的时区管理方案，支持全球主要时区，并提供友好的用户界面。

## 🎯 功能特性

### 1. 全局时区管理
- 统一的时区状态管理
- 自动检测浏览器本地时区
- 持久化存储用户偏好
- 实时同步所有时间显示

### 2. 丰富的时区支持
- 支持 20+ 个主要时区
- 包含常用时区和其他重要时区
- 友好的时区显示名称
- UTC 偏移量显示

### 3. 智能时区选择器
- 基于 shadcn-svelte Select 组件
- 支持搜索和筛选功能
- 分组显示（常用/其他）
- 实时预览当前时区信息

### 4. 时区感知的时间显示
- 所有时间显示自动适配选定时区
- 图表时间轴时区转换
- 数据查询时间范围时区处理
- 相对时间显示

## 🏗️ 技术架构

### 核心组件

```text
时区设置系统
├── timezoneStore (状态管理)        # 时区状态和持久化
├── TimezoneSelector (UI组件)       # 时区选择器界面
├── timezone.ts (工具函数)          # 时区转换和格式化
└── Settings.svelte (集成页面)      # 设置页面集成
```

### 状态管理

**时区 Store (`src/lib/stores/features/timezone.ts`)**

```typescript
interface TimezoneState {
  selectedTimezone: string;      // 当前选中的时区
  useLocalTimezone: boolean;     // 是否使用本地时区
  isInitialized: boolean;        // 是否已初始化
}
```

**主要 API:**
- `timezoneStore.setTimezone(timezone)` - 设置时区
- `timezoneStore.useLocalTimezone()` - 使用本地时区
- `timezoneStore.reset()` - 重置设置
- `currentTimezone` - 当前时区（派生 store）
- `isUsingLocalTimezone` - 是否使用本地时区（派生 store）

### 时区配置

**扩展的时区列表 (`src/lib/constants/time.ts`)**

```typescript
interface TimezoneConfig {
  id: string;           // IANA 时区标识符
  displayName: string;  // 友好显示名称
  offset: string;       // UTC 偏移量
  description: string;  // 描述信息
  isCommon?: boolean;   // 是否为常用时区
}
```

**支持的时区:**
- **特殊选项**: 浏览器本地时区
- **常用时区**: UTC, 北京, 纽约, 伦敦, 东京, 洛杉矶, 巴黎, 香港, 新加坡
- **其他时区**: 芝加哥, 丹佛, 柏林, 莫斯科, 首尔, 新德里, 迪拜, 悉尼, 奥克兰, 圣保罗

## 🔧 使用指南

### 在设置页面中使用

时区设置已集成到应用的设置页面中：

```svelte
<!-- 访问设置页面 -->
<a href="/settings">设置</a>
```

### 在组件中使用时区状态

```svelte
<script>
  import { currentTimezone, isUsingLocalTimezone } from '$lib/stores/features/timezone';
  
  // 响应式获取当前时区
  $: timezone = $currentTimezone;
  $: isLocal = $isUsingLocalTimezone;
</script>

<p>当前时区: {timezone}</p>
<p>使用本地时区: {isLocal ? '是' : '否'}</p>
```

### 使用时区工具函数

```typescript
import {
  getCurrentTimezone,
  formatDateInTimezone,
  convertToTimezone,
  formatTimestampInTimezone
} from '$lib/utils/timezone';

// 获取当前时区
const timezone = getCurrentTimezone();

// 格式化日期到指定时区
const formatted = formatDateInTimezone(
  new Date(),
  { 
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  },
  'Asia/Shanghai'
);

// 转换日期到指定时区
const converted = convertToTimezone(new Date(), 'UTC');

// 格式化时间戳
const timestampFormatted = formatTimestampInTimezone(
  Date.now(),
  'datetime',
  'Europe/London'
);
```

### 在图表中使用时区

图表组件已自动集成时区支持：

```svelte
<script>
  import LineChart from '$lib/components/charts/LineChart/LineChart.svelte';
  
  // 图表会自动使用当前选定的时区进行时间轴显示
</script>

<LineChart {data} timeFrame="1h" />
```

## 📱 用户界面

### 时区选择器组件

**基本用法:**

```svelte
<script>
  import TimezoneSelector from '$lib/components/features/settings/TimezoneSelector.svelte';
</script>

<TimezoneSelector 
  showSearch={true}
  commonOnly={false}
  class="custom-class"
/>
```

**属性说明:**
- `showSearch`: 是否显示搜索框（默认: true）
- `commonOnly`: 是否只显示常用时区（默认: false）
- `class`: 自定义样式类名

### 界面特性

1. **当前时区显示**
   - 显示当前选中的时区名称
   - 显示 UTC 偏移量
   - 实时更新

2. **时区选择下拉框**
   - 分组显示常用和其他时区
   - 搜索功能支持名称、描述、偏移量
   - 每个选项显示完整信息

3. **快速操作按钮**
   - "使用本地时区" 按钮
   - "使用 UTC" 按钮

4. **设置说明**
   - 功能说明文档
   - 当前时区信息显示

## 🔄 数据流程

### 时区设置流程

```text
用户选择时区
    ↓
TimezoneSelector 组件
    ↓
timezoneStore.setTimezone()
    ↓
更新 store 状态
    ↓
保存到 localStorage
    ↓
触发响应式更新
    ↓
所有时间显示组件更新
```

### 时间显示流程

```text
原始时间数据
    ↓
getCurrentTimezone()
    ↓
convertToTimezone() / formatDateInTimezone()
    ↓
时区转换后的时间
    ↓
显示给用户
```

## 🧪 测试

### 单元测试

时区功能包含完整的单元测试：

```bash
# 运行时区相关测试
npm run test timezone.test.ts
```

**测试覆盖:**
- 时区 store 状态管理
- 时区工具函数
- 时区转换和格式化
- 持久化存储
- 错误处理

### 集成测试

```typescript
// 测试时区设置的端到端流程
it('should update all time displays when timezone changes', () => {
  // 设置时区
  timezoneStore.setTimezone('Asia/Tokyo');
  
  // 验证所有组件都使用新时区
  expect(getCurrentTimezone()).toBe('Asia/Tokyo');
  
  // 验证时间格式化使用新时区
  const formatted = formatDateInTimezone(new Date());
  expect(formatted).toBeDefined();
});
```

## 🚀 最佳实践

### 1. 时区处理原则
- 始终使用 IANA 时区标识符
- 避免硬编码时区偏移量
- 考虑夏令时的影响
- 提供降级处理机制

### 2. 用户体验
- 默认使用浏览器检测的本地时区
- 提供常用时区的快速选择
- 显示友好的时区名称和偏移量
- 实时预览时区变更效果

### 3. 性能优化
- 时区转换结果缓存
- 避免频繁的时区计算
- 使用防抖处理用户输入
- 延迟加载非关键时区数据

### 4. 错误处理
- 无效时区的降级处理
- localStorage 访问失败的处理
- Intl API 不支持的降级方案
- 网络环境下的兼容性处理

## 📝 维护指南

### 添加新时区

1. 在 `src/lib/constants/time.ts` 中添加时区配置
2. 确保使用正确的 IANA 时区标识符
3. 提供友好的显示名称和描述
4. 标记是否为常用时区

### 扩展时区功能

1. 在 `src/lib/utils/timezone.ts` 中添加新的工具函数
2. 更新相关的 TypeScript 类型定义
3. 编写对应的单元测试
4. 更新文档说明

### 调试时区问题

1. 检查浏览器的 Intl API 支持
2. 验证时区标识符的有效性
3. 确认 localStorage 的访问权限
4. 测试不同时区的转换结果

## 🔗 相关资源

- [IANA 时区数据库](https://www.iana.org/time-zones)
- [MDN Intl.DateTimeFormat](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat)
- [shadcn-svelte Select 组件](https://www.shadcn-svelte.com/docs/components/select)
- [Svelte Stores 文档](https://svelte.dev/docs/svelte-store)
