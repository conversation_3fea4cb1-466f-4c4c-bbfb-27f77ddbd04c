<script lang="ts">
  // 导入图标
  import BarChart3Icon from '@lucide/svelte/icons/bar-chart-3';
  import { onDestroy, onMount } from 'svelte';

  import { ChartPanel } from '$lib/components/charts';
  // shadcn-svelte 组件导入
  import { Separator } from '$lib/components/ui/separator';
  import { dashboardStore } from '$lib/stores/features/dashboard';
  import { openTab } from '$lib/stores/ui';

  import ControlPanel from './ControlPanel.svelte';
  import StatCard from './StatCard.svelte';

  // 获取图表数据
  function getChartData(chartId: string) {
    const chartData = $dashboardStore.chartData;
    switch (chartId) {
      case 'bar':
        return chartData.barChartData;
      case 'line':
        return chartData.lineChartData;
      case 'pie':
        return chartData.pieChartData;
      case 'scatter':
        return chartData.scatterChartData;
      default:
        return [];
    }
  }

  // 处理图表拖放事件
  function handleChartDrop(params: { draggedId: string; targetId: string }) {
    const { draggedId, targetId } = params;

    // 获取当前排序
    const currentOrder = $dashboardStore.chartConfigs.map((c) => c.id);

    // 找到拖拽和目标元素的索引
    const draggedIndex = currentOrder.indexOf(draggedId);
    const targetIndex = currentOrder.indexOf(targetId);

    if (draggedIndex > -1 && targetIndex > -1) {
      const newOrder = Array.from(currentOrder);
      const [removed] = newOrder.splice(draggedIndex, 1);
      newOrder.splice(targetIndex, 0, removed);

      // 更新排序
      dashboardStore.reorderCharts(newOrder);
    }
  }

  // 处理图表点击事件
  function handleChartClick(params: any) {
    const { chartType, name, value } = params;
    console.log(`Chart clicked: ${chartType}, Name: ${name}, Value: ${value}`);

    // 这里可以实现图表联动逻辑
  }

  let refreshTimer: ReturnType<typeof setInterval> | null = null;

  // 设置自动刷新
  $: {
    if ($dashboardStore.refreshInterval !== null) {
      startAutoRefresh($dashboardStore.refreshInterval);
    } else {
      stopAutoRefresh();
    }
  }

  // 开始自动刷新
  function startAutoRefresh(interval: number) {
    stopAutoRefresh(); // 先清除现有定时器
    refreshTimer = setInterval(() => {
      dashboardStore.loadDashboardData();
    }, interval * 1000);
  }

  // 停止自动刷新
  function stopAutoRefresh() {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  }

  // 组件挂载时加载数据和初始化标签页
  onMount(async () => {
    // 自动添加"总览"标签页到TabBar
    openTab({
      href: '/',
      label: '总览',
      icon: BarChart3Icon,
    });

    // 加载仪表板数据
    await dashboardStore.loadDashboardData();
  });

  // 组件销毁时清理定时器
  onDestroy(() => {
    stopAutoRefresh();
  });
</script>

<div class="space-y-6">
  <!-- 页面标题和控制面板 -->
  <div class="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
    <!-- 页面标题 -->
    <div class="space-y-2">
      <h1 class="text-foreground text-3xl font-bold tracking-tight">仪表板 📊</h1>
      <p class="text-muted-foreground">实时监控和数据分析</p>
    </div>

    <!-- 控制面板 -->
    <div class="lg:flex-shrink-0">
      <ControlPanel />
    </div>
  </div>

  <Separator />

  <!-- 统计卡片 -->
  <div class="grid grid-cols-2 gap-4 sm:grid-cols-2 lg:grid-cols-4">
    <StatCard title="总用户数" value={$dashboardStore.stats.totalUsers} icon="👤" color="blue" />
    <StatCard
      title="月收入"
      value={$dashboardStore.stats.monthlyRevenue}
      icon="💰"
      color="green"
      prefix="$"
    />
    <StatCard
      title="转化率"
      value={$dashboardStore.stats.conversionRate}
      icon="📈"
      color="purple"
      suffix="%"
    />
    <StatCard title="活跃用户" value={$dashboardStore.stats.activeUsers} icon="⚡" color="orange" />
  </div>

  <!-- 图表网格 -->
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
    {#each $dashboardStore.chartConfigs
      .filter((c) => c.visible)
      .sort((a, b) => a.order - b.order) as chart (chart.id)}
      <ChartPanel
        {chart}
        data={getChartData(chart.id)}
        isLoading={$dashboardStore.isLoading}
        enableDrag={true}
        showHeader={true}
        onChartDrop={handleChartDrop}
        onChartClick={handleChartClick}
      />
    {/each}
  </div>
</div>

<style>
  /* 禁用可能出现的溢出波浪线装饰 */
  :global(.wave-decoration),
  :global(.wave-container),
  :global(.wave-svg) {
    display: none !important;
  }
</style>
