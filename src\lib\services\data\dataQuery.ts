// 数据查询服务 - 整合原有的查询功能
import type { MarketEvent, BaseQueryParams } from '$lib/types';

// 向后兼容的查询参数类型
interface QueryParams {
  keyword: string;
  selectedCoins: string[];
  startDate: string;
  endDate: string;
  status: string;
  type: string[];
}

interface QueryResult {
  items: MarketEvent[];
  total: number;
}

// MSW 返回的 API 响应类型格式
interface DataQueryResponse {
  code: number;
  message: string;
  data: MarketEvent[];
}

/**
 * 数据查询服务类
 *
 * 提供灵活的数据查询、筛选、分页和导出功能。
 * 支持多种查询条件组合，包括关键词搜索、状态筛选、日期范围等。
 * 使用 MSW (Mock Service Worker) 进行开发环境的数据模拟。
 *
 * @example
 * ```typescript
 * import { dataQueryService } from '$lib/services/data/dataQuery';
 *
 * // 执行查询
 * const result = await dataQueryService.queryData({
 *   keyword: '搜索关键词',
 *   status: '有效',
 *   startDate: '2024-01-01',
 *   endDate: '2024-01-31',
 *   page: 1,
 *   pageSize: 10
 * });
 *
 * apiLogger.info(`查询完成，找到 ${result.total} 条记录`, { total: result.total });
 * apiLogger.debug('查询结果数据', { items: result.items });
 *
 * // 导出数据
 * const blob = await dataQueryService.exportData(queryParams);
 * // 创建下载链接...
 * ```
 *
 * @category Services
 */
export class DataQueryService {
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || '/api';
  }

  /**
   * 查询数据
   *
   * 根据提供的查询参数执行数据查询，支持分页。
   *
   * @param params - 查询参数对象
   * @param params.keyword - 搜索关键词，可选
   * @param params.status - 状态筛选，如 '全部'、'有效'、'无效'
   * @param params.type - 类型筛选，如 '类型A'、'类型B'、'类型C'
   * @param params.startDate - 开始日期，格式 YYYY-MM-DD
   * @param params.endDate - 结束日期，格式 YYYY-MM-DD
   * @param params.page - 页码，从 1 开始
   * @param params.pageSize - 每页条数
   * @returns Promise 包含查询结果和总数的对象
   *
   * @example
   * ```typescript
   * const result = await dataQueryService.queryData({
   *   keyword: 'test',
   *   status: '有效',
   *   type: '类型A',
   *   startDate: '2024-01-01',
   *   endDate: '2024-01-31',
   *   page: 1,
   *   pageSize: 20
   * });
   *
   * console.log(`总共 ${result.total} 条记录`);
   * result.items.forEach(item => {
   *   console.log(`${item.name} - ${item.status}`);
   * });
   * ```
   */
  async queryData(params: QueryParams & { page: number; pageSize: number }): Promise<QueryResult> {
    const response = await fetch(`${this.baseURL}/data/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const apiResponse: DataQueryResponse = await response.json();

    // 检查API响应状态 - 兼容MSW返回的格式 { code: 1000, message: 'success', data: [...] }
    if (apiResponse.code !== 1000 && apiResponse.message !== 'success') {
      throw new Error(`API Error: ${apiResponse.message || '未知错误'}`);
    }

    // 返回适配后的数据格式
    return {
      items: apiResponse.data,
      total: apiResponse.data.length, // 由于没有分页，总数就是返回数据的长度
    };
  }

  /**
   * 导出数据
   */
  async exportData(params: QueryParams): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/data/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.blob();
  }
}

// 创建默认实例
export const dataQueryService = new DataQueryService();
