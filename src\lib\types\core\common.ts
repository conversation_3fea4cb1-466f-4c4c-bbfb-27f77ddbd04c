/**
 * 通用共享类型定义
 * 
 * @category Core Types
 */

/**
 * 时间范围选项
 */
export type TimeRange =
  | 'today'      // 今天
  | 'yesterday'  // 昨天
  | 'week'       // 本周
  | 'month'      // 本月
  | 'last24h'    // 最近24小时
  | 'lastWeek'   // 上周
  | 'lastMonth'  // 上月
  | 'custom';    // 自定义时间范围

/**
 * 日期范围
 */
export interface DateRange {
  /** 开始日期 */
  start: string;
  /** 结束日期 */
  end: string;
}

/**
 * 选项接口（用于下拉选择、单选等）
 */
export interface Option<T = string> {
  /** 选项值 */
  value: T;
  /** 显示标签 */
  label: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 额外数据 */
  data?: Record<string, any>;
}

/**
 * 币种选项
 */
export interface CoinOption extends Option<string> {
  /** 币种符号 */
  symbol: string;
  /** 币种名称 */
  name: string;
  /** 图标URL */
  image?: string;
  /** 是否为主流币 */
  isMainstream?: boolean;
}

/**
 * 数据类型选项
 */
export interface DataTypeOption extends Option<string> {
  /** 描述信息 */
  description?: string;
  /** 是否启用 */
  enabled?: boolean;
  /** 分类 */
  category?: string;
}

/**
 * 金额范围
 */
export interface AmountRange {
  /** 最小金额 */
  min: number | null;
  /** 最大金额 */
  max: number | null;
}

/**
 * 数值范围
 */
export interface NumberRange {
  /** 最小值 */
  min: number;
  /** 最大值 */
  max: number;
}

/**
 * 坐标点
 */
export interface Point {
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
}

/**
 * 尺寸
 */
export interface Size {
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
}

/**
 * 状态类型
 */
export type Status = 'idle' | 'loading' | 'success' | 'error';

/**
 * 主题模式
 */
export type ThemeMode = 'light' | 'dark' | 'system';

/**
 * 语言类型
 */
export type Language = 'zh-CN' | 'en-US';

/**
 * 环境类型
 */
export type Environment = 'development' | 'production' | 'test';
