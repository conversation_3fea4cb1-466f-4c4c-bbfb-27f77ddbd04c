/**
 * 交集观察器 Hook
 *
 * 提供元素可见性检测功能
 *
 * @category Hooks
 */

import { onDestroy } from 'svelte';
import { type Writable,writable } from 'svelte/store';

import { browser } from '$app/environment';
import { createModuleLogger } from '$lib/utils/logger';

const intersectionLogger = createModuleLogger('use-intersection');

/**
 * 交集观察器配置
 */
export interface UseIntersectionOptions {
  /** 根元素 */
  root?: Element | null;
  /** 根边距 */
  rootMargin?: string;
  /** 阈值 */
  threshold?: number | number[];
  /** 是否只触发一次 */
  triggerOnce?: boolean;
  /** 初始可见状态 */
  initialIsIntersecting?: boolean;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 交集观察器状态
 */
export interface IntersectionState {
  /** 是否相交 */
  isIntersecting: boolean;
  /** 相交比例 */
  intersectionRatio: number;
  /** 相交矩形 */
  intersectionRect: DOMRectReadOnly | null;
  /** 边界矩形 */
  boundingClientRect: DOMRectReadOnly | null;
  /** 根边界矩形 */
  rootBounds: DOMRectReadOnly | null;
  /** 目标元素 */
  target: Element | null;
  /** 时间戳 */
  time: number;
}

/**
 * 交集观察器 Hook 返回值
 */
export interface UseIntersectionReturn {
  /** 交集状态 */
  intersectionState: Writable<IntersectionState>;
  /** 是否相交 */
  isIntersecting: Writable<boolean>;
  /** 相交比例 */
  intersectionRatio: Writable<number>;
  /** 观察元素 */
  observe: (element: Element) => void;
  /** 停止观察 */
  unobserve: (element: Element) => void;
  /** 停止观察所有元素 */
  disconnect: () => void;
}

/**
 * 交集观察器 Hook
 */
export function useIntersection(options: UseIntersectionOptions = {}): UseIntersectionReturn {
  const {
    root = null,
    rootMargin = '0px',
    threshold = 0,
    triggerOnce = false,
    initialIsIntersecting = false,
    enabled = true,
  } = options;

  let observer: IntersectionObserver | null = null;
  let hasTriggered = false;
  const observedElements = new Set<Element>();

  // 初始状态
  const initialState: IntersectionState = {
    isIntersecting: initialIsIntersecting,
    intersectionRatio: 0,
    intersectionRect: null,
    boundingClientRect: null,
    rootBounds: null,
    target: null,
    time: 0,
  };

  const intersectionState = writable<IntersectionState>(initialState);
  const isIntersecting = writable<boolean>(initialIsIntersecting);
  const intersectionRatio = writable<number>(0);

  /**
   * 创建观察器
   */
  function createObserver(): IntersectionObserver | null {
    if (!browser || !enabled || typeof IntersectionObserver === 'undefined') {
      return null;
    }

    try {
      return new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            const state: IntersectionState = {
              isIntersecting: entry.isIntersecting,
              intersectionRatio: entry.intersectionRatio,
              intersectionRect: entry.intersectionRect,
              boundingClientRect: entry.boundingClientRect,
              rootBounds: entry.rootBounds,
              target: entry.target,
              time: entry.time,
            };

            intersectionState.set(state);
            isIntersecting.set(entry.isIntersecting);
            intersectionRatio.set(entry.intersectionRatio);

            intersectionLogger.debug('Intersection state changed', {
              isIntersecting: entry.isIntersecting,
              intersectionRatio: entry.intersectionRatio,
              target: entry.target.tagName,
            });

            // 如果只触发一次且已经相交，则停止观察
            if (triggerOnce && entry.isIntersecting && !hasTriggered) {
              hasTriggered = true;
              unobserve(entry.target);
            }
          });
        },
        {
          root,
          rootMargin,
          threshold,
        }
      );
    } catch (error) {
      intersectionLogger.error('Failed to create IntersectionObserver', {
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * 观察元素
   */
  function observe(element: Element): void {
    if (!element || observedElements.has(element)) {
      return;
    }

    if (!observer) {
      observer = createObserver();
    }

    if (observer) {
      observer.observe(element);
      observedElements.add(element);

      intersectionLogger.debug('Element observation started', {
        element: element.tagName,
        totalObserved: observedElements.size,
      });
    }
  }

  /**
   * 停止观察元素
   */
  function unobserve(element: Element): void {
    if (!element || !observedElements.has(element)) {
      return;
    }

    if (observer) {
      observer.unobserve(element);
      observedElements.delete(element);

      intersectionLogger.debug('Element observation stopped', {
        element: element.tagName,
        totalObserved: observedElements.size,
      });
    }
  }

  /**
   * 停止观察所有元素
   */
  function disconnect(): void {
    if (observer) {
      observer.disconnect();
      observedElements.clear();
      hasTriggered = false;

      intersectionLogger.debug('All observations disconnected');
    }

    // 重置状态
    intersectionState.set(initialState);
    isIntersecting.set(initialIsIntersecting);
    intersectionRatio.set(0);
  }

  // 清理函数
  onDestroy(() => {
    disconnect();
  });

  return {
    intersectionState,
    isIntersecting,
    intersectionRatio,
    observe,
    unobserve,
    disconnect,
  };
}

/**
 * 简化的可见性检测 Hook
 */
export function useVisibility(
  threshold: number = 0,
  triggerOnce: boolean = false
): UseIntersectionReturn & {
  isVisible: Writable<boolean>;
} {
  const intersection = useIntersection({
    threshold,
    triggerOnce,
  });

  return {
    ...intersection,
    isVisible: intersection.isIntersecting,
  };
}

/**
 * 懒加载 Hook
 */
export function useLazyLoad(threshold: number = 0.1): UseIntersectionReturn & {
  shouldLoad: Writable<boolean>;
  load: (element: Element) => void;
} {
  const intersection = useIntersection({
    threshold,
    triggerOnce: true,
  });

  const shouldLoad = writable<boolean>(false);

  // 监听相交状态变化
  intersection.isIntersecting.subscribe((isIntersecting) => {
    if (isIntersecting) {
      shouldLoad.set(true);
    }
  });

  function load(element: Element): void {
    intersection.observe(element);
  }

  return {
    ...intersection,
    shouldLoad,
    load,
  };
}

/**
 * 无限滚动 Hook
 */
export function useInfiniteScroll(
  onLoadMore: () => void | Promise<void>,
  options: {
    threshold?: number;
    rootMargin?: string;
    enabled?: boolean;
  } = {}
): UseIntersectionReturn & {
  loadMore: (element: Element) => void;
} {
  const { threshold = 1, rootMargin = '100px', enabled = true } = options;

  const intersection = useIntersection({
    threshold,
    rootMargin,
    enabled,
  });

  // 监听相交状态变化
  intersection.isIntersecting.subscribe(async (isIntersecting) => {
    if (isIntersecting && enabled) {
      try {
        await onLoadMore();
        intersectionLogger.debug('Load more triggered');
      } catch (error) {
        intersectionLogger.error('Load more failed', {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  });

  function loadMore(element: Element): void {
    intersection.observe(element);
  }

  return {
    ...intersection,
    loadMore,
  };
}

/**
 * 视口进入/离开检测 Hook
 */
export function useViewportEntry(
  onEnter?: () => void,
  onLeave?: () => void,
  threshold: number = 0
): UseIntersectionReturn {
  const intersection = useIntersection({ threshold });

  let wasIntersecting = false;

  intersection.isIntersecting.subscribe((isIntersecting) => {
    if (isIntersecting && !wasIntersecting) {
      // 进入视口
      wasIntersecting = true;
      if (onEnter) {
        onEnter();
      }
      intersectionLogger.debug('Element entered viewport');
    } else if (!isIntersecting && wasIntersecting) {
      // 离开视口
      wasIntersecting = false;
      if (onLeave) {
        onLeave();
      }
      intersectionLogger.debug('Element left viewport');
    }
  });

  return intersection;
}
