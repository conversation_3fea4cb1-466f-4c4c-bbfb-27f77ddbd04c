[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / LIQUIDATION_CHART_CONFIG

# Variable: LIQUIDATION_CHART_CONFIG

> `const` **LIQUIDATION_CHART_CONFIG**: `object`

Defined in: src/lib/constants/charts.ts:52

## Type declaration

### SNAPSHOT_RANGES

> `readonly` **SNAPSHOT_RANGES**: readonly \[\{ `label`: `"1小时"`; `value`: `"1h"`; \}, \{ `label`: `"4小时"`; `value`: `"4h"`; \}, \{ `label`: `"12小时"`; `value`: `"12h"`; \}, \{ `label`: `"24小时"`; `value`: `"24h"`; \}\]

### TIME_FRAMES

> `readonly` **TIME_FRAMES**: readonly \[\{ `label`: `"1小时"`; `value`: `"1h"`; \}, \{ `label`: `"4小时"`; `value`: `"4h"`; \}, \{ `label`: `"1天"`; `value`: `"1d"`; \}, \{ `label`: `"1周"`; `value`: `"1w"`; \}\]

### TIME_RANGES

> `readonly` **TIME_RANGES**: readonly \[\{ `label`: `"7天"`; `value`: `"7d"`; \}, \{ `label`: `"30天"`; `value`: `"30d"`; \}, \{ `label`: `"90天"`; `value`: `"90d"`; \}\]

### VIEW_MODES

> `readonly` **VIEW_MODES**: readonly \[\{ `label`: `"全市场"`; `value`: `"overall"`; \}, \{ `label`: `"主流/山寨对比"`; `value`: `"comparison"`; \}\]
