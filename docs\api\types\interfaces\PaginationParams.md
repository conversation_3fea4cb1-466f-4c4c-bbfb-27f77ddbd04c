[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / PaginationParams

# Interface: PaginationParams

Defined in: src/lib/types/api.ts:25

分页参数

## Properties

### page

> **page**: `number`

Defined in: src/lib/types/api.ts:26

---

### pageSize

> **pageSize**: `number`

Defined in: src/lib/types/api.ts:27

---

### sortBy?

> `optional` **sortBy**: `string`

Defined in: src/lib/types/api.ts:28

---

### sortOrder?

> `optional` **sortOrder**: `"asc"` \| `"desc"`

Defined in: src/lib/types/api.ts:29
