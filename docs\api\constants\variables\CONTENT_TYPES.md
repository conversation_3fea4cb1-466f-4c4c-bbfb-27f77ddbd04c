[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / CONTENT_TYPES

# Variable: CONTENT_TYPES

> `const` **CONTENT_TYPES**: `object`

Defined in: src/lib/constants/api.ts:62

## Type declaration

### CSV

> `readonly` **CSV**: `"text/csv"` = `'text/csv'`

### EXCEL

> `readonly` **EXCEL**: `"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"` = `'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'`

### FORM_DATA

> `readonly` **FORM_DATA**: `"multipart/form-data"` = `'multipart/form-data'`

### JSON

> `readonly` **JSON**: `"application/json"` = `'application/json'`

### URL_ENCODED

> `readonly` **URL_ENCODED**: `"application/x-www-form-urlencoded"` = `'application/x-www-form-urlencoded'`
