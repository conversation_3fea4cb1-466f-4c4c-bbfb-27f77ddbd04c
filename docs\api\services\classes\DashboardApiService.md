[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [services](../README.md) / DashboardApiService

# Class: DashboardApiService

Defined in: src/lib/services/api/dashboard.ts:8

仪表板数据 API 服务

## Extends

- [`BaseApiService`](BaseApiService.md)

## Constructors

### Constructor

> **new DashboardApiService**(`baseURL?`): `DashboardApiService`

Defined in: src/lib/services/api/base.ts:12

#### Parameters

##### baseURL?

`string`

#### Returns

`DashboardApiService`

#### Inherited from

[`BaseApiService`](BaseApiService.md).[`constructor`](BaseApiService.md#constructor)

## Methods

### getOverview()

> **getOverview**(): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<`any`\>\>

Defined in: src/lib/services/api/dashboard.ts:26

获取仪表板概览数据

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<`any`\>\>

---

### getStats()

> **getStats**(): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`DashboardStats`](../../types/interfaces/DashboardStats.md)\>\>

Defined in: src/lib/services/api/dashboard.ts:12

获取仪表板统计数据

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`DashboardStats`](../../types/interfaces/DashboardStats.md)\>\>
