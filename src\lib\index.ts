// 统一导出所有 lib 模块
export * from './components';
export * from './services';
export * from './stores';
export * from './constants';

// 只导出类型，避免与组件和 stores 中的类型冲突
// 不使用 export type * 语法，而是明确导出需要的类型
export type {
  // 核心类型
  ApiResponse,
  ApiErrorInfo,
  PaginationParams,
  PaginatedResponse,
  TimeRange,
  DateRange,
  CoinOption,
  DataTypeOption,
  AmountRange,

  // 业务类型
  LiquidationData,
  LiquidationStats,
  LiquidationRankItem,
  LiquidationRankResponse,
  MarketEvent,
  MarketEventType,
  CoinInfo,
  HistoricalDataItem,
  BaseQueryParams,
  QueryResults,
  CompoundQueryParams,

  // UI 类型
  ChartData,
  ChartConfig,
  BarChartItem,
  LineChartItem,
  PieChartItem,
  DashboardStats,
  NavigationItem,
  NotificationItem,
} from './types';
