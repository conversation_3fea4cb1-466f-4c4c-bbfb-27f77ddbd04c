[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / REFRESH_INTERVALS

# Variable: REFRESH_INTERVALS

> `const` **REFRESH_INTERVALS**: `object`

Defined in: src/lib/constants/time.ts:37

## Type declaration

### FAST

> `readonly` **FAST**: `5000` = `5000`

### NORMAL

> `readonly` **NORMAL**: `30000` = `30000`

### REAL_TIME

> `readonly` **REAL_TIME**: `1000` = `1000`

### SLOW

> `readonly` **SLOW**: `60000` = `60000`

### VERY_SLOW

> `readonly` **VERY_SLOW**: `300000` = `300000`
