[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / HTTP_STATUS

# Variable: HTTP_STATUS

> `const` **HTTP_STATUS**: `object`

Defined in: src/lib/constants/api.ts:52

## Type declaration

### BAD_REQUEST

> `readonly` **BAD_REQUEST**: `400` = `400`

### CREATED

> `readonly` **CREATED**: `201` = `201`

### FORBIDDEN

> `readonly` **FORBIDDEN**: `403` = `403`

### INTERNAL_SERVER_ERROR

> `readonly` **INTERNAL_SERVER_ERROR**: `500` = `500`

### NOT_FOUND

> `readonly` **NOT_FOUND**: `404` = `404`

### OK

> `readonly` **OK**: `200` = `200`

### UNAUTHORIZED

> `readonly` **UNAUTHORIZED**: `401` = `401`
