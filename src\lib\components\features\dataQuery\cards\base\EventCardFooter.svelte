<script lang="ts">
  import { CardFooter } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import type { MarketEvent } from '$lib/types';
  import type { CardSize } from '../utils';

  interface Props {
    event: MarketEvent;
    size?: CardSize;
    isHighlighted?: boolean;
    showActions?: boolean;
    onCardClick?: (event: MarketEvent) => void;
    onViewDetails?: (event: MarketEvent) => void;
    onCopyId?: (event: MarketEvent) => void;
  }

  const { 
    event,
    size = 'md',
    isHighlighted = false,
    showActions = false,
    onCardClick,
    onViewDetails,
    onCopyId
  }: Props = $props();

  // 根据尺寸设置样式
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const buttonSizeClasses = {
    sm: 'h-6 px-2 text-xs',
    md: 'h-7 px-3 text-xs',
    lg: 'h-8 px-4 text-sm'
  };

  // 处理点击事件
  function handleCardClick() {
    onCardClick?.(event);
  }

  function handleViewDetails() {
    onViewDetails?.(event);
  }

  function handleCopyId() {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(event.id);
    }
    onCopyId?.(event);
  }
</script>

<CardFooter class="flex items-center justify-between border-t border-border pt-3">
  <!-- 状态指示 -->
  <div class="text-muted-foreground {sizeClasses[size]}">
    {#if isHighlighted}
      <span class="inline-flex items-center">
        <div class="bg-primary mr-1 h-2 w-2 rounded-full"></div>
        已选中
      </span>
    {:else}
      <span class="inline-flex items-center">
        <div class="bg-muted-foreground mr-1 h-2 w-2 rounded-full opacity-50"></div>
        点击查看详情
      </span>
    {/if}
  </div>

  <!-- 操作按钮区域 -->
  {#if showActions}
    <div class="flex items-center space-x-2">
      {#if onViewDetails}
        <Button 
          variant="outline" 
          size="sm"
          class={buttonSizeClasses[size]}
          onclick={handleViewDetails}
        >
          详情
        </Button>
      {/if}
      
      {#if onCopyId}
        <Button 
          variant="ghost" 
          size="sm"
          class={buttonSizeClasses[size]}
          onclick={handleCopyId}
          title="复制ID"
        >
          复制
        </Button>
      {/if}
    </div>
  {:else if onCardClick}
    <!-- 如果没有显示操作按钮但有点击处理，显示点击提示 -->
    <Button 
      variant="ghost" 
      size="sm"
      class={buttonSizeClasses[size]}
      onclick={handleCardClick}
    >
      查看
    </Button>
  {/if}
</CardFooter>
