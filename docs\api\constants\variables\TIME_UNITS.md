[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / TIME_UNITS

# Variable: TIME_UNITS

> `const` **TIME_UNITS**: `object`

Defined in: src/lib/constants/time.ts:45

## Type declaration

### DAY

> `readonly` **DAY**: `number`

### HOUR

> `readonly` **HOUR**: `number`

### MINUTE

> `readonly` **MINUTE**: `number`

### MONTH

> `readonly` **MONTH**: `number`

### SECOND

> `readonly` **SECOND**: `1000` = `1000`

### WEEK

> `readonly` **WEEK**: `number`

### YEAR

> `readonly` **YEAR**: `number`
