[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / DATE_FORMATS

# Variable: DATE_FORMATS

> `const` **DATE_FORMATS**: `object`

Defined in: src/lib/constants/time.ts:28

## Type declaration

### DATE

> `readonly` **DATE**: `"YYYY-MM-DD"` = `'YYYY-MM-DD'`

### DATETIME

> `readonly` **DATETIME**: `"YYYY-MM-DD HH:mm:ss"` = `'YYYY-MM-DD HH:mm:ss'`

### ISO

> `readonly` **ISO**: `"YYYY-MM-DDTHH:mm:ss.sssZ"` = `'YYYY-MM-DDTHH:mm:ss.sssZ'`

### MONTH

> `readonly` **MONTH**: `"YYYY-MM"` = `'YYYY-MM'`

### TIME

> `readonly` **TIME**: `"HH:mm:ss"` = `'HH:mm:ss'`

### YEAR

> `readonly` **YEAR**: `"YYYY"` = `'YYYY'`
