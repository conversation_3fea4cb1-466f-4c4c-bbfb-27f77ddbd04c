[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / DataItem

# Interface: DataItem

Defined in: src/lib/types/dataQuery.ts:2

## Properties

### createdAt

> **createdAt**: `string`

Defined in: src/lib/types/dataQuery.ts:7

---

### id

> **id**: `number`

Defined in: src/lib/types/dataQuery.ts:3

---

### name

> **name**: `string`

Defined in: src/lib/types/dataQuery.ts:4

---

### status

> **status**: `string`

Defined in: src/lib/types/dataQuery.ts:6

---

### type

> **type**: `string`

Defined in: src/lib/types/dataQuery.ts:5
