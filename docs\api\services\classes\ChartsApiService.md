[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [services](../README.md) / ChartsApiService

# Class: ChartsApiService

Defined in: src/lib/services/api/charts.ts:16

图表数据 API 服务

## Extends

- [`BaseApiService`](BaseApiService.md)

## Constructors

### Constructor

> **new ChartsApiService**(`baseURL?`): `ChartsApiService`

Defined in: src/lib/services/api/base.ts:12

#### Parameters

##### baseURL?

`string`

#### Returns

`ChartsApiService`

#### Inherited from

[`BaseApiService`](BaseApiService.md).[`constructor`](BaseApiService.md#constructor)

## Methods

### getAllChartData()

> **getAllChartData**(): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`ChartData`](../../types/interfaces/ChartData.md)\>\>

Defined in: src/lib/services/api/charts.ts:89

获取所有图表数据

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`ChartData`](../../types/interfaces/ChartData.md)\>\>

---

### getBarChartData()

> **getBarChartData**(): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`BarChartItem`](../../types/interfaces/BarChartItem.md)[]\>\>

Defined in: src/lib/services/api/charts.ts:20

获取柱状图数据

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`BarChartItem`](../../types/interfaces/BarChartItem.md)[]\>\>

---

### getLineChartData()

> **getLineChartData**(): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`LineChartItem`](../../types/interfaces/LineChartItem.md)[]\>\>

Defined in: src/lib/services/api/charts.ts:36

获取折线图数据

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`LineChartItem`](../../types/interfaces/LineChartItem.md)[]\>\>

---

### getPieChartData()

> **getPieChartData**(): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`PieChartItem`](../../types/interfaces/PieChartItem.md)[]\>\>

Defined in: src/lib/services/api/charts.ts:53

获取饼图数据

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`PieChartItem`](../../types/interfaces/PieChartItem.md)[]\>\>

---

### getScatterChartData()

> **getScatterChartData**(): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`ScatterChartItem`](../../types/interfaces/ScatterChartItem.md)[]\>\>

Defined in: src/lib/services/api/charts.ts:68

获取散点图数据

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`ScatterChartItem`](../../types/interfaces/ScatterChartItem.md)[]\>\>
