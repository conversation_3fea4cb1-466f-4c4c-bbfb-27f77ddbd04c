<!-- App.svelte -->
<script lang="ts">
  import WeekView from './WeekView.svelte';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';

  // 示例事件数据
  const sampleEvents = [
    {
      id: '1',
      title: '团队会议',
      startTime: '09:00',
      endTime: '10:30',
      date: '2024-07-29',
      color: '#3b82f6',
    },
    {
      id: '2',
      title: '项目评审',
      startTime: '14:00',
      endTime: '15:30',
      date: '2024-07-29',
      color: '#ef4444',
    },
    {
      id: '3',
      title: '午餐会',
      startTime: '12:00',
      endTime: '13:00',
      date: '2024-07-30',
      color: '#10b981',
    },
    {
      id: '4',
      title: '代码审查',
      startTime: '09:30',
      endTime: '10:00',
      date: '2024-07-29',
      color: '#f59e0b',
    },
    {
      id: '5',
      title: '客户演示',
      startTime: '15:00',
      endTime: '16:00',
      date: '2024-07-31',
      color: '#8b5cf6',
    },
    {
      id: '6',
      title: '培训课程',
      startTime: '10:00',
      endTime: '12:00',
      date: '2024-08-01',
      color: '#06b6d4',
    },
    {
      id: '7',
      title: '一对一会议',
      startTime: '16:30',
      endTime: '17:00',
      date: '2024-08-02',
      color: '#84cc16',
    },
    {
      id: '8',
      title: '重叠测试1',
      startTime: '14:15',
      endTime: '15:00',
      date: '2024-07-29',
      color: '#f97316',
    },
    {
      id: '9',
      title: '重叠测试2',
      startTime: '14:30',
      endTime: '15:15',
      date: '2024-07-29',
      color: '#ec4899',
    },
  ];

  let currentWeek = new Date();
</script>

<Card>
  <CardHeader class="pb-4">
    <CardTitle class="flex items-center justify-between">
      <span>查询结果</span>
    </CardTitle>
  </CardHeader>
  <CardContent class="pt-0">
    <WeekView events={sampleEvents} startHour={6} endHour={22} hourHeight={60} bind:currentWeek />
  </CardContent>
</Card>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
</style>
