<!-- src/lib/components/liquidation/charts/KpiCard.svelte -->
<script lang="ts">
  // shadcn-svelte 组件导入
  import { Card, CardContent } from '$lib/components/ui/card';
  import { liquidationStore } from '$lib/stores/features/liquidation';
  import { formatNumber } from '$lib/utils/formatters';
</script>

<div class="flex h-full w-full items-center justify-center">
  <div class="grid w-full grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
    <!-- 总清算金额 -->
    <Card>
      <CardContent class="p-4">
        <div class="text-muted-foreground text-xs font-medium">总清算金额</div>
        <div class="text-foreground mt-1 text-2xl font-bold">
          ${formatNumber($liquidationStore.stats.totalAmount, 2)}
        </div>
        <div class="text-muted-foreground text-xs">USD</div>
      </CardContent>
    </Card>

    <!-- 总清算笔数 -->
    <Card>
      <CardContent class="p-4">
        <div class="text-muted-foreground text-xs font-medium">总清算笔数</div>
        <div class="text-foreground mt-1 text-2xl font-bold">
          {formatNumber($liquidationStore.stats.totalCount, 0)}
        </div>
        <div class="text-muted-foreground text-xs">笔交易</div>
      </CardContent>
    </Card>

    <!-- 多头清算金额 -->
    <Card>
      <CardContent class="p-4">
        <div class="text-muted-foreground text-xs font-medium">多头清算金额</div>
        <div class="mt-1 text-2xl font-bold text-green-600">
          ${formatNumber($liquidationStore.stats.longAmount, 2)}
        </div>
        <div class="text-muted-foreground text-xs">USD</div>
      </CardContent>
    </Card>

    <!-- 空头清算金额 -->
    <Card>
      <CardContent class="p-4">
        <div class="text-muted-foreground text-xs font-medium">空头清算金额</div>
        <div class="mt-1 text-2xl font-bold text-red-600">
          ${formatNumber($liquidationStore.stats.shortAmount, 2)}
        </div>
        <div class="text-muted-foreground text-xs">USD</div>
      </CardContent>
    </Card>

    <!-- 多空金额比 -->
    <Card>
      <CardContent class="p-4">
        <div class="text-muted-foreground text-xs font-medium">多空金额比</div>
        <div class="mt-1 text-2xl font-bold text-blue-600">
          {formatNumber($liquidationStore.stats.longShortRatio, 2)}
        </div>
        <div class="text-muted-foreground text-xs">多/空</div>
      </CardContent>
    </Card>
  </div>
</div>
