<!-- src/lib/components/features/CurrencyIcon.svelte -->
<script lang="ts">
  import { onMount } from 'svelte';

  import { currencyCacheStore } from '$lib/stores/features/currencyCache';
  import type { CoinInfo } from '$lib/types';

  interface Props {
    /** 货币ID */
    currencyId: string;
    /** 备用显示的货币符号 */
    symbol?: string;
    /** 图标大小类名，默认为 'size-5' */
    size?: string;
    /** 是否显示加载状态 */
    showLoading?: boolean;
    /** 自定义类名 */
    class?: string;
  }

  const {
    currencyId,
    symbol = '',
    size = 'size-5',
    showLoading = true,
    class: className = '',
  }: Props = $props();

  // 状态管理
  let coinInfo: CoinInfo | null = $state(null);
  let loadingCoinInfo = $state(false);
  let imageLoadError = $state(false);
  let retryCount = $state(0);

  // 最大重试次数
  const MAX_RETRY_COUNT = 2;

  /**
   * 处理图片加载失败
   */
  function handleImageError(): void {
    imageLoadError = true;
    
    // 如果还有重试次数，尝试强制刷新缓存
    if (retryCount < MAX_RETRY_COUNT && currencyId && coinInfo) {
      retryCount++;
      
      currencyCacheStore.refreshCurrency(currencyId).then(refreshedInfo => {
        if (refreshedInfo && refreshedInfo.image !== coinInfo?.image) {
          // 如果获取到新的图片URL，重置错误状态并更新信息
          imageLoadError = false;
          coinInfo = refreshedInfo;
        }
      }).catch(error => {
        console.error('Failed to refresh currency info after image error:', error);
      });
    }
  }

  /**
   * 重置图片加载状态
   */
  function resetImageState(): void {
    imageLoadError = false;
    retryCount = 0;
  }

  /**
   * 获取显示的符号
   */
  function getDisplaySymbol(): string {
    return (coinInfo?.symbol || symbol || '?').charAt(0).toUpperCase();
  }

  /**
   * 获取图标的 alt 文本
   */
  function getAltText(): string {
    return coinInfo?.symbol || symbol || 'Currency';
  }

  /**
   * 获取完整的类名
   */
  function getIconClasses(): string {
    return [size, 'rounded-full', className].filter(Boolean).join(' ');
  }

  /**
   * 获取占位符类名
   */
  function getPlaceholderClasses(): string {
    return [
      size,
      'bg-muted rounded-full flex items-center justify-center text-xs font-medium',
      className
    ].filter(Boolean).join(' ');
  }

  // 监听 currencyId 变化
  $effect(() => {
    if (currencyId) {
      loadCurrencyInfo();
    }
  });

  /**
   * 加载货币信息
   */
  async function loadCurrencyInfo(): Promise<void> {
    if (!currencyId) return;

    // 重置状态
    resetImageState();
    
    // 先检查缓存
    const cachedInfo = currencyCacheStore.getCachedCurrency(currencyId);
    if (cachedInfo) {
      coinInfo = cachedInfo;
      return;
    }

    // 如果缓存中没有，显示加载状态并获取数据
    if (showLoading) {
      loadingCoinInfo = true;
    }

    try {
      const result = await currencyCacheStore.getCurrency(currencyId);
      if (result) {
        coinInfo = result;
      }
    } catch (error) {
      console.error('Failed to load currency info:', error);
    } finally {
      loadingCoinInfo = false;
    }
  }

  // 组件挂载时加载货币信息
  onMount(() => {
    if (currencyId) {
      loadCurrencyInfo();
    }
  });
</script>

<!-- 货币图标显示 -->
{#if loadingCoinInfo && showLoading}
  <!-- 加载中的占位符 -->
  <div class="{getPlaceholderClasses()} animate-pulse"></div>
{:else if coinInfo?.image && !imageLoadError}
  <!-- 货币图标 -->
  <img
    src={coinInfo.image}
    alt={getAltText()}
    class={getIconClasses()}
    loading="lazy"
    onerror={handleImageError}
  />
{:else}
  <!-- 默认图标占位符 -->
  <div class={getPlaceholderClasses()}>
    {getDisplaySymbol()}
  </div>
{/if}
