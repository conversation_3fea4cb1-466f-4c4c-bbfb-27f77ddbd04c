[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / CHART_TYPES

# Variable: CHART_TYPES

> `const` **CHART_TYPES**: `object`

Defined in: src/lib/constants/charts.ts:38

## Type declaration

### AREA

> `readonly` **AREA**: `"area"` = `'area'`

### BAR

> `readonly` **BAR**: `"bar"` = `'bar'`

### CANDLESTICK

> `readonly` **CANDLESTICK**: `"candlestick"` = `'candlestick'`

### LINE

> `readonly` **LINE**: `"line"` = `'line'`

### PIE

> `readonly` **PIE**: `"pie"` = `'pie'`

### SCATTER

> `readonly` **SCATTER**: `"scatter"` = `'scatter'`
