import { derived, writable } from 'svelte/store';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from '$lib/components/charts';
import { liquidationDataService } from '$lib/services/data/liquidation';
import { notifications } from '$lib/stores/ui/notifications';
import type {
  LiquidationChartConfig,
  LiquidationData,
  LiquidationStats,
  SnapshotTimeRange,
  TimeFilters,
  TrendDuration,
  TrendTimeFrame,
  TrendTimeRange,
  TrendViewMode,
} from '$lib/types';
import { storeLogger } from '$lib/utils/logger';

import DualAxisLineChart from '../../components/features/liquidation/charts/DualAxisLineChart.svelte';
// 创建图表组件导入路径
// 这些组件将在后面实现
import KpiCard from '../../components/features/liquidation/charts/KpiCard.svelte';

// 初始化图表配置
const initialChartConfigs: LiquidationChartConfig[] = [
  {
    id: 'kpi',
    component: KpiCard,
    visible: true,
    order: 0,
    title: '核心清算指标概览',
    subTitle: 'KPI指标',
    series: 'snapshot',
  },
  {
    id: 'pie-long-short',
    component: PieChart,
    visible: true,
    order: 1,
    title: '多/空清算金额(USD)占比',
    subTitle: '环形图',
    series: 'snapshot',
    options: { isDonut: true },
  },
  {
    id: 'pie-order-size',
    component: PieChart,
    visible: true,
    order: 2,
    title: '按订单threshold的清算笔数分布',
    subTitle: '饼图',
    series: 'snapshot',
  },
  // 全市场视图图表
  {
    id: 'stacked-area-long-short',
    component: LineChart,
    visible: true,
    order: 3,
    title: '多/空总清算金额(USD)趋势',
    subTitle: '折线图',
    series: 'trend',
    viewMode: 'overall',
  },
  {
    id: 'percent-stacked-area',
    component: BarChart,
    visible: true,
    order: 4,
    title: '多/空清算金额占比趋势',
    subTitle: '100%堆叠柱状图',
    series: 'trend',
    options: { stackPercent: true },
    groupBy: 'side',
    viewMode: 'overall',
  },
  // 主流/山寨对比视图图表
  {
    id: 'dual-axis-coin-type',
    component: DualAxisLineChart,
    visible: true,
    order: 5,
    title: '主流币 vs 山寨币 多/空清算金额(USD)',
    subTitle: '双Y轴折线图',
    series: 'trend',
    viewMode: 'comparison',
  },
  {
    id: 'stacked-bar-coin-type',
    component: BarChart,
    visible: true,
    order: 6,
    title: '主流币 vs 山寨币 多/空清算金额比例',
    subTitle: '100%堆叠柱状图',
    series: 'trend',
    options: { stackPercent: true },
    groupBy: 'coinType',
    viewMode: 'comparison',
  },
];

// 初始KPI数据
const initialStats: LiquidationStats = {
  totalAmount: 0,
  totalCount: 0,
  longAmount: 0,
  shortAmount: 0,
  longShortRatio: 0,
};

// 初始时间筛选器设置
const initialTimeFilters: TimeFilters = {
  snapshot: '24h', // 默认快照时间窗口
  snapshotTimeRange: '24h', // 默认快照时间窗口
  trend: '7d', // 默认趋势时间窗口
  trendTimeRange: '7d', // 默认趋势时间窗口（保持向后兼容）
  trendTimeFrame: '1d', // 默认时间粒度：每天
  trendDuration: '7d', // 默认总时间范围：7天
  trendViewMode: 'overall', // 默认视图模式：全市场
};

/**
 * 爆仓数据状态管理 - 统一版本
 *
 * 合并了新旧版本的优点：
 * - 保持旧版本的简洁性和直接的数据更新方法
 * - 加入新版本的完整错误处理和现代化特性
 * - 提供完整的异步数据加载和自动刷新功能
 *
 * @example
 * ```typescript
 * import { liquidationStore } from '$lib/stores/liquidation/store';
 *
 * // 订阅状态变化
 * liquidationStore.subscribe(state => {
 *   storeLogger.debug('清算数据状态更新', { state });
 * });
 *
 * // 加载数据
 * await liquidationStore.loadSnapshotData('24h');
 *
 * // 手动更新数据
 * liquidationStore.updateSnapshotData(newData);
 *
 * // 开始自动刷新
 * liquidationStore.startAutoRefresh(60000);
 * ```
 */
function createLiquidationStore() {
  // 基础store状态 - 加入错误处理
  const { subscribe, update, set } = writable({
    chartConfigs: initialChartConfigs,
    stats: initialStats,
    timeFilters: initialTimeFilters,
    snapshotData: [] as LiquidationData[],
    trendData: [] as LiquidationData[],
    isLoading: false,
    error: null as string | null, // 新增：错误状态
  });

  // 派生 stores - 提供细粒度的状态访问
  const charts = derived({ subscribe }, ($state) => $state.chartConfigs);
  const stats = derived({ subscribe }, ($state) => $state.stats);
  const loading = derived({ subscribe }, ($state) => $state.isLoading);
  const error = derived({ subscribe }, ($state) => $state.error); // 新增：错误状态
  const timeFilters = derived({ subscribe }, ($state) => $state.timeFilters);
  const snapshotData = derived({ subscribe }, ($state) => $state.snapshotData); // 新增：快照数据
  const trendData = derived({ subscribe }, ($state) => $state.trendData); // 新增：趋势数据

  // 派生store：按系列筛选图表
  const snapshotCharts = derived({ subscribe }, ($state) =>
    $state.chartConfigs.filter((chart) => chart.series === 'snapshot')
  );

  const trendCharts = derived({ subscribe }, ($state) =>
    $state.chartConfigs.filter(
      (chart) => chart.series === 'trend' && chart.viewMode === $state.timeFilters.trendViewMode
    )
  );

  return {
    subscribe,
    // 派生 stores
    charts,
    stats,
    loading,
    error,
    timeFilters,
    snapshotData,
    trendData,
    snapshotCharts,
    trendCharts,

    // === 数据更新方法 (保留旧版本的简洁性) ===

    // 更新快照数据
    updateSnapshotData: (data: LiquidationData[]) => {
      update((state) => ({ ...state, snapshotData: data, error: null }));
    },

    // 更新趋势数据
    updateTrendData: (data: LiquidationData[]) => {
      update((state) => ({ ...state, trendData: data, error: null }));
    },

    // 更新统计数据
    updateStats: (stats: LiquidationStats) => {
      update((state) => ({ ...state, stats, error: null }));
    },

    // 设置加载状态
    setLoading: (isLoading: boolean) => {
      update((state) => ({ ...state, isLoading }));
    },

    // 清除错误状态
    clearError: () => {
      update((state) => ({ ...state, error: null }));
    },

    // === 时间筛选器管理 ===

    // 设置时间筛选器 (批量更新)
    setTimeFilters: (filters: Partial<TimeFilters>) => {
      update((state) => ({
        ...state,
        timeFilters: { ...state.timeFilters, ...filters },
      }));
    },

    // 更新时间筛选器 (新版本兼容方法)
    updateTimeFilters: (filters: Partial<TimeFilters>) => {
      update((state) => ({
        ...state,
        timeFilters: { ...state.timeFilters, ...filters },
      }));
    },

    // 设置快照时间范围
    setSnapshotTimeRange: (range: SnapshotTimeRange) => {
      update((state) => ({
        ...state,
        timeFilters: { ...state.timeFilters, snapshotTimeRange: range },
      }));
    },

    // 设置趋势时间范围
    setTrendTimeRange: (range: TrendTimeRange) => {
      update((state) => ({
        ...state,
        timeFilters: { ...state.timeFilters, trendTimeRange: range },
      }));
    },

    // 设置趋势时间粒度
    setTrendTimeFrame: (timeFrame: TrendTimeFrame) => {
      update((state) => ({
        ...state,
        timeFilters: { ...state.timeFilters, trendTimeFrame: timeFrame },
      }));
    },

    // 设置趋势总时间范围
    setTrendDuration: (duration: TrendDuration) => {
      update((state) => ({
        ...state,
        timeFilters: { ...state.timeFilters, trendDuration: duration },
      }));
    },

    // 设置趋势视图模式
    setTrendViewMode: (viewMode: TrendViewMode) => {
      update((state) => ({
        ...state,
        timeFilters: { ...state.timeFilters, trendViewMode: viewMode },
      }));
    },

    // 切换图表可见性
    toggleChartVisibility: (chartId: string) => {
      update((state) => {
        const chartConfigs = state.chartConfigs.map((config) => {
          if (config.id === chartId) {
            return { ...config, visible: !config.visible };
          }
          return config;
        });
        return { ...state, chartConfigs };
      });
    },

    // === 异步数据加载方法 (新版本的完整错误处理) ===

    // 加载快照数据
    async loadSnapshotData(timeRange?: SnapshotTimeRange) {
      let currentState:
        | {
            chartConfigs: LiquidationChartConfig[];
            stats: LiquidationStats;
            timeFilters: TimeFilters;
            snapshotData: LiquidationData[];
            trendData: LiquidationData[];
            isLoading: boolean;
            error: string | null;
          }
        | undefined;

      update((state) => {
        currentState = state;
        return { ...state, isLoading: true, error: null };
      });

      try {
        const range = timeRange || currentState!.timeFilters.snapshotTimeRange;
        const response = await liquidationDataService.fetchSnapshotData(range);

        update((state) => ({
          ...state,
          snapshotData: response.data.data,
          stats: response.data.stats,
          isLoading: false,
        }));

        // 成功通知
        notifications.success('数据加载成功', '快照数据已更新');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载快照数据失败';
        update((state) => ({
          ...state,
          isLoading: false,
          error: errorMessage,
        }));

        // 错误通知
        notifications.error('数据加载失败', errorMessage);
      }
    },

    // 加载趋势数据
    async loadTrendData(timeRange?: TrendTimeRange) {
      let currentState:
        | {
            chartConfigs: LiquidationChartConfig[];
            stats: LiquidationStats;
            timeFilters: TimeFilters;
            snapshotData: LiquidationData[];
            trendData: LiquidationData[];
            isLoading: boolean;
            error: string | null;
          }
        | undefined;

      update((state) => {
        currentState = state;
        return { ...state, isLoading: true, error: null };
      });

      try {
        const range = timeRange || currentState!.timeFilters.trendTimeRange;
        const response = await liquidationDataService.fetchTrendData(range);

        update((state) => ({
          ...state,
          trendData: response.data,
          isLoading: false,
        }));

        // 成功通知
        notifications.success('趋势数据加载成功', '图表数据已更新');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载趋势数据失败';
        update((state) => ({
          ...state,
          isLoading: false,
          error: errorMessage,
        }));

        // 错误通知
        notifications.error('趋势数据加载失败', errorMessage);
      }
    },

    // 加载详细趋势数据
    async loadDetailedTrendData(timeFrame?: TrendTimeFrame, duration?: TrendDuration) {
      let currentState:
        | {
            chartConfigs: LiquidationChartConfig[];
            stats: LiquidationStats;
            timeFilters: TimeFilters;
            snapshotData: LiquidationData[];
            trendData: LiquidationData[];
            isLoading: boolean;
            error: string | null;
          }
        | undefined;

      update((state) => {
        currentState = state;
        return { ...state, isLoading: true, error: null };
      });

      try {
        const frame = timeFrame || currentState!.timeFilters.trendTimeFrame;
        const dur = duration || currentState!.timeFilters.trendDuration;
        const response = await liquidationDataService.fetchTrendDataWithFrameAndDuration(
          frame,
          dur
        );

        update((state) => ({
          ...state,
          trendData: response.data,
          isLoading: false,
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载详细趋势数据失败';
        update((state) => ({
          ...state,
          isLoading: false,
          error: errorMessage,
        }));
      }
    },

    // === 自动刷新和批量操作 ===

    // 开始自动刷新
    startAutoRefresh: (interval: number = 60000) => {
      liquidationDataService.startAutoRefresh(interval);
    },

    // 停止自动刷新
    stopAutoRefresh: () => {
      liquidationDataService.stopAutoRefresh();
    },

    // 刷新所有数据 (改进版本，添加防抖和重复调用保护)
    async refreshAllData() {
      let currentState:
        | {
            chartConfigs: LiquidationChartConfig[];
            stats: LiquidationStats;
            timeFilters: TimeFilters;
            snapshotData: LiquidationData[];
            trendData: LiquidationData[];
            isLoading: boolean;
            error: string | null;
          }
        | undefined;

      // 检查是否已经在加载中，避免重复调用
      update((state) => {
        currentState = state;
        if (state.isLoading) {
          storeLogger.debug('Data refresh already in progress, skipping refresh request');
          return state;
        }
        return state;
      });

      // 如果已经在加载中，直接返回
      if (currentState!.isLoading) {
        return;
      }

      try {
        const startTime = performance.now();
        storeLogger.info('Starting liquidation data refresh', {
          snapshotTimeRange: currentState!.timeFilters.snapshotTimeRange,
          trendTimeFrame: currentState!.timeFilters.trendTimeFrame,
          trendDuration: currentState!.timeFilters.trendDuration,
        });

        await Promise.all([
          this.loadSnapshotData(currentState!.timeFilters.snapshotTimeRange),
          this.loadDetailedTrendData(
            currentState!.timeFilters.trendTimeFrame,
            currentState!.timeFilters.trendDuration
          ),
        ]);

        const endTime = performance.now();
        const duration = endTime - startTime;
        storeLogger.info('Liquidation data refresh completed', {
          duration: `${duration.toFixed(2)}ms`,
          durationMs: duration,
        });
      } catch (error) {
        storeLogger.error('Error refreshing liquidation data', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
        update((state) => ({
          ...state,
          error: error instanceof Error ? error.message : 'Failed to refresh data',
          isLoading: false,
        }));
      }
    },

    // === 图表管理 ===

    // 重新排序图表 (新版本功能)
    reorderCharts: (newOrder: string[]) => {
      update((state) => {
        const orderMap = new Map<string, number>();
        newOrder.forEach((id, index) => {
          orderMap.set(id, index);
        });

        const chartConfigs = state.chartConfigs.map((config) => {
          if (orderMap.has(config.id)) {
            return { ...config, order: orderMap.get(config.id)! };
          }
          return config;
        });

        chartConfigs.sort((a, b) => a.order - b.order);
        return { ...state, chartConfigs };
      });
    },

    // === 重置功能 ===

    // 重置所有状态
    reset: () => {
      set({
        chartConfigs: initialChartConfigs,
        stats: initialStats,
        timeFilters: initialTimeFilters,
        snapshotData: [],
        trendData: [],
        isLoading: false,
        error: null,
      });
    },
  };
}

/**
 * 爆仓数据状态管理 - 统一实现
 *
 * 这是合并了新旧版本优点的统一实现：
 *
 * **特性：**
 * - ✅ 完整的错误处理和状态管理
 * - ✅ 异步数据加载和自动刷新
 * - ✅ 简洁的直接数据更新方法
 * - ✅ 细粒度的派生 stores
 * - ✅ 图表配置管理和重排序
 * - ✅ 时间筛选器管理
 *
 * **使用方式：**
 * ```typescript
 * // 异步加载数据
 * await liquidationStore.loadSnapshotData('24h');
 *
 * // 直接更新数据
 * liquidationStore.updateSnapshotData(newData);
 *
 * // 订阅状态变化
 * $: storeLogger.debug('加载状态变化', { isLoading: $liquidationStore.isLoading });
 * $: if ($liquidationStore.error) storeLogger.error('清算数据错误', { error: $liquidationStore.error });
 * ```
 *
 * @category Stores
 */
export const liquidationStore = createLiquidationStore();

// 导出派生 stores 以便直接使用
export const trendCharts = liquidationStore.trendCharts;
export const snapshotCharts = liquidationStore.snapshotCharts;
