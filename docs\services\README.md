# 服务层文档

本项目采用统一的服务层架构，提供数据获取、处理和状态管理的功能。

> 🎉 **架构重构完成**：统一了 API 服务和数据服务，使用 MSW 进行开发环境的数据模拟。

## 🏗️ 架构概览

```text
src/lib/services/
├── api/                 # API 基础服务
│   ├── base.ts         # 基础 API 服务类
│   ├── charts.ts       # 图表 API 服务
│   └── dashboard.ts    # 仪表板 API 服务
├── data/               # 数据服务层 🆕
│   ├── dashboard.ts    # 仪表板数据服务
│   ├── dataQuery.ts    # 数据查询服务
│   └── liquidation.ts  # 爆仓数据服务 🆕
└── index.ts           # 统一导出
```

## 🔄 服务层设计原则

### 1. 分层架构

- **API 层**：负责底层 HTTP 请求和响应处理
- **数据服务层**：负责业务逻辑和数据转换
- **状态管理层**：负责数据缓存和状态同步

### 2. 统一错误处理

- 所有服务都继承自 `BaseApiService`
- 统一的错误响应格式
- 使用 MSW 进行开发环境数据模拟

### 3. 类型安全

- 完整的 TypeScript 类型定义
- 编译时类型检查
- 智能代码提示

## 📊 数据服务

### DashboardDataService

仪表板数据服务，负责获取和处理仪表板相关数据。

```typescript
import { dashboardDataService } from '$lib/services/data/dashboard';

// 获取所有图表数据
const response = await dashboardDataService.fetchAllChartData(startTime, endTime, selectedTimeZone);

if (response.status === 'success') {
  console.log('图表数据:', response.data);
}

// 获取统计数据
const statsResponse = await dashboardDataService.fetchStats();
```

**主要方法：**

- `fetchAllChartData()` - 获取所有图表数据
- `fetchStats()` - 获取统计卡片数据
- `fetchOverview()` - 获取概览数据

### LiquidationDataService 🆕

专业的加密货币爆仓数据服务，提供快照数据、趋势分析和实时监控功能。

```typescript
import { liquidationDataService } from '$lib/services/data/liquidation';

// 获取24小时快照数据
const snapshotResponse = await liquidationDataService.fetchSnapshotData('24h');
if (snapshotResponse.status === 'success') {
  const { data, stats } = snapshotResponse.data;
  console.log(`总爆仓金额: $${stats.totalAmount.toLocaleString()}`);
  console.log(`多空比: ${stats.longShortRatio.toFixed(2)}`);
}

// 获取7天趋势数据
const trendResponse = await liquidationDataService.fetchTrendData('7d');

// 开始自动刷新（每分钟）
liquidationDataService.startAutoRefresh(60000);

// 停止自动刷新
liquidationDataService.stopAutoRefresh();
```

**主要方法：**

- `fetchSnapshotData(timeRange)` - 获取快照数据和统计信息
- `fetchTrendData(duration)` - 获取趋势数据
- `startAutoRefresh(interval)` - 开始自动刷新
- `stopAutoRefresh()` - 停止自动刷新

**支持的时间范围：**

- 快照数据：`'1h'`, `'4h'`, `'12h'`, `'24h'`
- 趋势数据：`'1h'`, `'4h'`, `'12h'`, `'1d'`, `'3d'`, `'7d'`, `'30d'`

**数据特性：**

- 支持主流币 vs 山寨币分类
- 多空方向分析
- 订单大小阈值分布
- 实时数据更新
- 自动错误处理和重试

### DataQueryService

数据查询服务，提供灵活的数据查询和导出功能。

```typescript
import { dataQueryService } from '$lib/services/data/dataQuery';

// 查询数据
const result = await dataQueryService.queryData({
  keyword: '搜索关键词',
  status: '有效',
  page: 1,
  pageSize: 10,
});

// 导出数据
const blob = await dataQueryService.exportData(queryParams);
```

**主要方法：**

- `queryData()` - 执行数据查询
- `exportData()` - 导出查询结果

## 🔧 API 基础服务

### BaseApiService

所有 API 服务的基类，提供统一的错误处理和请求封装。

```typescript
import { BaseApiService } from '$lib/services/api/base';

class CustomApiService extends BaseApiService {
  constructor() {
    super('/api/custom');
  }

  async getData() {
    return this.get('/data');
  }
}
```

**主要功能:**

- 统一的错误处理
- 请求和响应拦截
- 自动重试机制
- 超时控制

### ChartsApi

图表数据 API 服务。

```typescript
import { chartsApi } from '$lib/services/api';

// 获取所有图表数据
const data = await chartsApi.getAllChartData();

// 获取特定图表数据
const barData = await chartsApi.getBarChartData();
```

### DashboardApi

仪表板 API 服务。

```typescript
import { dashboardApi } from '$lib/services/api';

// 获取统计数据
const stats = await dashboardApi.getStats();

// 获取概览数据
const overview = await dashboardApi.getOverview();
```

## 🛡️ 错误处理和 MSW 集成

### MSW 数据模拟

开发环境使用 MSW (Mock Service Worker) 在网络层拦截 API 请求并返回模拟数据。

```typescript
// 业务代码保持纯净，只关注正常 API 调用
const response = await fetch('/api/data', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(params),
});

if (!response.ok) {
  throw new Error(`HTTP ${response.status}: ${response.statusText}`);
}

return await response.json();
```

### MSW 优势

- **开发独立性**: 前端开发不依赖后端 API 的完成度
- **测试可靠性**: 提供稳定、可预测的测试数据
- **真实环境模拟**: 在浏览器层面拦截请求，更接近真实环境
- **维护简单**: 集中管理所有 API 模拟逻辑

### 错误处理策略

- 统一的错误响应格式
- 清晰的错误信息提示
- 适当的错误边界处理

## 📝 使用指南

### 导入服务

```typescript
// 导入数据服务
import { dashboardDataService, dataQueryService, liquidationDataService } from '$lib/services';

// 导入 API 服务
import { chartsApi, dashboardApi } from '$lib/services';
```

### 错误处理

```typescript
try {
  const data = await dashboardDataService.fetchAllChartData();
  if (data.status === 'success') {
    // 处理成功数据
    console.log(data.data);
  } else {
    // 处理错误
    console.error(data.message);
  }
} catch (error) {
  // 处理异常
  console.error('服务调用失败:', error);
}
```

### 配置服务

```typescript
// 在环境变量中配置 API 基础地址
// .env.local
VITE_API_BASE_URL=https://api.example.com

// 服务会自动使用配置的地址
const service = new DashboardDataService();
```
