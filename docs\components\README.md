# 组件文档

本项目采用分层的组件架构，按功能和复用性进行组织。

## 📁 组件分类

### UI 组件 (`src/lib/components/ui/`)

基础的、可复用的 UI 组件，不包含业务逻辑。

#### Button 组件

通用按钮组件，支持多种样式和状态。

```svelte
<script>
  import { Button } from '$lib/components/ui';
</script>

<Button variant="primary" size="md" on:click={handleClick}>点击我</Button>
```

**Props:**

- `variant`: 'primary' | 'secondary' | 'danger' | 'ghost' - 按钮样式
- `size`: 'sm' | 'md' | 'lg' - 按钮大小
- `disabled`: boolean - 是否禁用
- `loading`: boolean - 是否显示加载状态

#### Input 组件

通用输入框组件，支持多种类型和验证。

```svelte
<script>
  import { Input } from '$lib/components/ui';
</script>

<Input type="text" placeholder="请输入内容" bind:value={inputValue} error={errorMessage} />
```

**Props:**

- `type`: 'text' | 'email' | 'password' | 'number' - 输入类型
- `placeholder`: string - 占位符文本
- `error`: string - 错误信息
- `disabled`: boolean - 是否禁用

#### Modal 组件

模态框组件，支持自定义内容和操作。

```svelte
<script>
  import { Modal } from '$lib/components/ui';
</script>

<Modal bind:open={isModalOpen} title="确认操作">
  <p>确定要执行此操作吗？</p>
  <svelte:fragment slot="footer">
    <Button variant="secondary" on:click={() => (isModalOpen = false)}>取消</Button>
    <Button variant="primary" on:click={handleConfirm}>确认</Button>
  </svelte:fragment>
</Modal>
```

**Props:**

- `open`: boolean - 是否显示模态框
- `title`: string - 模态框标题
- `size`: 'sm' | 'md' | 'lg' | 'xl' - 模态框大小

**Slots:**

- `default` - 模态框内容
- `footer` - 模态框底部操作区

### 图表组件 (`src/lib/components/charts/`)

基于 ECharts 的图表组件封装。

#### LineChart 组件

折线图组件，支持多系列数据和自定义配置。

```svelte
<script>
  import { LineChart } from '$lib/components/charts';

  const data = [
    { name: '一月', value: 100 },
    { name: '二月', value: 200 },
    // ...
  ];
</script>

<LineChart {data} height={400} />
```

#### BarChart 组件

柱状图组件，支持垂直和水平布局。

```svelte
<script>
  import { BarChart } from '$lib/components/charts';

  const data = [
    { name: '产品A', value: 120 },
    { name: '产品B', value: 200 },
    { name: '产品C', value: 150 },
  ];
</script>

<BarChart {data} height={400} />
```

**Props:**

- `data`: 图表数据数组
- `height`: 图表高度（默认 400px）
- `options`: ECharts 配置选项

#### PieChart 组件

饼图组件，支持环形图和标签显示。

```svelte
<script>
  import { PieChart } from '$lib/components/charts';

  const data = [
    { name: '直接访问', value: 335 },
    { name: '邮件营销', value: 310 },
    { name: '联盟广告', value: 234 },
  ];
</script>

<PieChart {data} options={{ isDonut: true }} />
```

**Props:**

- `data`: 饼图数据数组
- `options.isDonut`: 是否显示为环形图
- `options.showLabels`: 是否显示标签

#### ScatterChart 组件

散点图组件，支持多维数据展示。

```svelte
<script>
  import { ScatterChart } from '$lib/components/charts';

  const data = [
    { name: '数据点1', x: 10, y: 20, category: 'A' },
    { name: '数据点2', x: 15, y: 25, category: 'B' },
  ];
</script>

<ScatterChart {data} height={400} />
```

### 布局组件 (`src/lib/components/layout/`)

页面布局和导航相关组件。

#### Header 组件

页面头部组件，包含导航和用户信息。

#### Sidebar 组件

侧边栏导航组件，支持折叠和展开。

#### TabBar 组件

标签页组件，支持动态添加和关闭标签。

### 功能组件 (`src/lib/components/features/`)

包含业务逻辑的功能组件。

#### Dashboard 组件

仪表板主组件，整合了数据展示和控制面板。

```svelte
<script>
  import { Dashboard } from '$lib/components/features/dashboard';
</script>

<Dashboard />
```

**特性：**

- 实时数据统计卡片
- 多种图表类型展示
- 时间范围选择器
- 图表配置管理
- 拖拽排序功能

#### DataQuery 组件

数据查询组件，包含查询表单和结果展示。

```svelte
<script>
  import { DataQuery } from '$lib/components/features/dataQuery';
</script>

<DataQuery />
```

**特性：**

- 灵活的查询表单
- 分页数据表格
- 导出功能
- 响应式设计

#### LiquidationDashboard 组件 🆕

专业的加密货币爆仓数据分析仪表板。

```svelte
<script>
  import { LiquidationDashboard } from '$lib/components/features/liquidation';
</script>

<LiquidationDashboard />
```

**特性：**

- 实时爆仓数据监控
- 多维度数据分析（快照 + 趋势）
- 专业金融图表（双轴线图、堆叠面积图、饼图）
- 主流币 vs 山寨币对比分析
- 多空比例分析
- 自动刷新机制

#### SystemStatus 组件 🆕

系统状态监控组件，集成了强大的网络监控功能，提供实时的网络状态、质量和连接信息展示。

```svelte
<script>
  import { SystemStatus } from '$lib/components/features/system';
</script>

<SystemStatus />
```

**特性：**

- **网络状态监控**：实时检测在线/离线状态
- **网络质量评估**：优秀/良好/较差/离线四级质量指示
- **连接信息展示**：连接类型（4G/WiFi/以太网）和延迟信息
- **下载速度监控**：实时网络下载速度检测
- **响应式更新**：基于 Svelte 5 的 $state 和 $effect 语法
- **生命周期管理**：自动订阅和清理网络状态
- **视觉指示器**：彩色圆点、图标和动画效果
- **详细 Tooltip**：悬停显示完整的网络状态详情

**技术实现：**

- 使用 Svelte 5 语法（$state, $effect, $derived）
- 集成 networkMonitor.ts 工具模块
- 订阅 networkStatus, networkQuality, isOnline stores
- shadcn-svelte Badge 和 Tooltip 组件
- 自动网络质量检测和延迟测量

**使用场景：**

- 侧边栏底部系统状态显示
- 错误恢复界面网络状态指示
- 任何需要网络状态监控的场景

**主要子组件：**

##### ChartPanel 组件

图表面板组件，用于渲染各种类型的图表。

```svelte
<script>
  import ChartPanel from '$lib/components/features/liquidation/ChartPanel.svelte';

  const chartConfig = {
    id: 'kpi',
    component: KpiCard,
    title: '核心清算指标概览',
    // ...
  };
</script>

<ChartPanel chart={chartConfig} series="snapshot" onChartClick={handleChartClick} />
```

##### KpiCard 组件

KPI 指标卡片组件，展示核心清算指标。

```svelte
<script>
  import { KpiCard } from '$lib/components/features/liquidation/charts';
</script>

<KpiCard data={liquidationData} />
```

**显示指标：**

- 总清算金额
- 总清算笔数
- 多空比例
- 平均清算金额
- 主流币占比

##### DualAxisLineChart 组件

双轴线图组件，用于主流币 vs 山寨币对比分析。

```svelte
<script>
  import { DualAxisLineChart } from '$lib/components/features/liquidation/charts';
</script>

<DualAxisLineChart
  data={liquidationData}
  title="主流币 vs 山寨币 多/空清算金额(USD)"
  timeFrame="1h"
/>
```

**特性：**

- 双 Y 轴设计（主流币 + 山寨币）
- 不同颜色区分币种类型
- 不同线型区分多空方向
- 时间粒度聚合
- 交互式图例

##### StackedAreaChart 组件

堆叠面积图组件，展示多空清算金额趋势。

```svelte
<script>
  import { StackedAreaChart } from '$lib/components/features/liquidation/charts';
</script>

<StackedAreaChart
  data={liquidationData}
  title="多/空总清算金额(USD)趋势"
  stackPercent={false}
/>
```

##### PercentStackedAreaChart 组件

百分比堆叠面积图组件，展示主流币 vs 山寨币占比。

```svelte
<script>
  import { PercentStackedAreaChart } from '$lib/components/features/liquidation/charts';
</script>

<PercentStackedAreaChart
  data={liquidationData}
  title="主流币 vs 山寨币清算金额占比"
  stackPercent={true}
/>
```

## 🎨 设计原则

### 1. 单一职责

每个组件只负责一个明确的功能，避免组件过于复杂。

### 2. 可复用性

UI 组件设计为通用的，可在不同场景下复用。

### 3. 可组合性

组件支持通过 props 和 slots 进行灵活组合。

### 4. 类型安全

所有组件都有完整的 TypeScript 类型定义。

## 📝 使用指南

### 导入组件

```javascript
// 导入单个组件
import { Button } from '$lib/components/ui';

// 导入多个组件
import { Button, Input, Modal } from '$lib/components/ui';

// 导入所有组件
import * as UI from '$lib/components/ui';
```

### 组件命名

- 组件文件使用 PascalCase：`Button.svelte`
- 组件文件夹使用 camelCase：`button/`
- 导出名称使用 PascalCase：`export { Button }`

### 属性传递

```svelte
<!-- 基本属性 -->
<Button variant="primary">点击</Button>

<!-- 绑定属性 -->
<Input bind:value={inputValue} />

<!-- 事件处理 -->
<Button on:click={handleClick}>点击</Button>

<!-- 插槽内容 -->
<Modal>
  <p>模态框内容</p>
</Modal>
```
