<!-- src/lib/components/ui/error-boundary/ErrorBoundary.svelte -->
<script lang="ts">
  import { onDestroy,onMount } from 'svelte';

  import { notifications } from '$lib/stores/ui/notifications';
  import { createModuleLogger } from '$lib/utils/logger';

  import { ErrorDisplay, ErrorRecoveryHelper } from '../error-display';

  // Props
  export let fallbackComponent: any = null;
  export let showErrorDetails = false;
  export let enableRecovery = true;
  export let onError: ((error: Error, errorInfo?: any) => void) | null = null;

  // 错误状态
  let hasError = false;
  let error: Error | null = null;
  let errorInfo: any = null;
  let errorId = '';

  // Logger
  const errorLogger = createModuleLogger('error-boundary');

  // 错误处理函数
  function handleError(err: Error, info?: any) {
    hasError = true;
    error = err;
    errorInfo = info;
    errorId = `error-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // 记录错误日志
    errorLogger.error('Component error caught by ErrorBoundary', {
      errorId,
      message: err.message,
      stack: err.stack,
      componentStack: info?.componentStack,
      props: info?.props,
      timestamp: new Date().toISOString(),
    });

    // 显示用户通知
    notifications.error(
      '组件错误',
      '页面的某个部分遇到了问题，我们已经记录了这个错误。',
      0 // 不自动消失
    );

    // 调用外部错误处理器
    if (onError) {
      try {
        onError(err, info);
      } catch (handlerError) {
        errorLogger.error('Error in onError handler', {
          originalError: err.message,
          handlerError: handlerError instanceof Error ? handlerError.message : String(handlerError),
        });
      }
    }
  }

  // 重置错误状态
  function resetError() {
    hasError = false;
    error = null;
    errorInfo = null;
    errorId = '';

    errorLogger.info('ErrorBoundary reset', { timestamp: new Date().toISOString() });
    notifications.success('已恢复', '页面已重新加载，问题应该已经解决。');
  }

  // 全局错误监听器
  let unhandledErrorHandler: ((event: ErrorEvent) => void) | null = null;
  let unhandledRejectionHandler: ((event: PromiseRejectionEvent) => void) | null = null;

  onMount(() => {
    // 监听未捕获的 JavaScript 错误
    unhandledErrorHandler = (event: ErrorEvent) => {
      handleError(new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'unhandled-error',
      });
    };

    // 监听未捕获的 Promise 拒绝
    unhandledRejectionHandler = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));

      handleError(error, {
        type: 'unhandled-rejection',
        reason: event.reason,
      });
    };

    window.addEventListener('error', unhandledErrorHandler);
    window.addEventListener('unhandledrejection', unhandledRejectionHandler);

    errorLogger.debug('ErrorBoundary mounted and global error listeners attached');
  });

  onDestroy(() => {
    // 清理事件监听器
    if (unhandledErrorHandler) {
      window.removeEventListener('error', unhandledErrorHandler);
    }
    if (unhandledRejectionHandler) {
      window.removeEventListener('unhandledrejection', unhandledRejectionHandler);
    }

    errorLogger.debug('ErrorBoundary destroyed and global error listeners removed');
  });
</script>

{#if hasError}
  {#if fallbackComponent}
    <!-- 使用自定义的错误组件 -->
    <svelte:component this={fallbackComponent} {error} {errorInfo} {resetError} />
  {:else}
    <!-- 使用新的错误显示组件 -->
    <div class="flex min-h-[400px] items-center justify-center p-4">
      <div class="w-full max-w-2xl space-y-4">
        {#if error}
          <ErrorDisplay
            {error}
            title="页面遇到了问题"
            showRetry={enableRecovery}
            showDetails={showErrorDetails}
            showCopy={true}
            variant="card"
            size="md"
            onRetry={resetError}
            onCopy={() => notifications.success('已复制', '错误信息已复制到剪贴板')}
            onDismiss={resetError}
          />

          {#if enableRecovery}
            <ErrorRecoveryHelper
              {error}
              autoRecovery={true}
              maxRetries={3}
              retryDelay={2000}
              onRecovered={resetError}
              onFailed={() => notifications.error('自动恢复失败', '请尝试手动刷新页面')}
              onRetry={(attempt) => notifications.info('正在重试', `第 ${attempt} 次尝试恢复`)}
            />
          {/if}
        {/if}
      </div>
    </div>
  {/if}
{:else}
  <!-- 正常渲染子组件 -->
  <slot />
{/if}
