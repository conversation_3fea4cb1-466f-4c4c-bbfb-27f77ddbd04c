[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [services](../README.md) / LiquidationDataService

# Class: LiquidationDataService

Defined in: src/lib/services/data/liquidation.ts:42

爆仓数据服务类

专门处理加密货币市场的爆仓数据，提供快照数据、趋势分析和实时监控功能。
支持多种时间粒度和视图模式，包括全市场视图和主流币/山寨币对比视图。

## Example

```typescript
import { liquidationDataService } from '$lib/services/data/liquidation';

// 获取24小时快照数据
const snapshotResponse = await liquidationDataService.fetchSnapshotData('24h');
if (snapshotResponse.status === 'success') {
  const { data, stats } = snapshotResponse.data;
  console.log(`总爆仓金额: $${stats.totalAmount.toLocaleString()}`);
  console.log(`多空比: ${stats.longShortRatio.toFixed(2)}`);
}

// 获取7天趋势数据
const trendResponse = await liquidationDataService.fetchTrendData('7d');

// 开始自动刷新（每分钟）
liquidationDataService.startAutoRefresh(60000);

// 停止自动刷新
liquidationDataService.stopAutoRefresh();
```

## Constructors

### Constructor

> **new LiquidationDataService**(): `LiquidationDataService`

Defined in: src/lib/services/data/liquidation.ts:46

#### Returns

`LiquidationDataService`

## Methods

### fetchSnapshotData()

> **fetchSnapshotData**(`timeRange`): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<\{ `data`: [`LiquidationData`](../../types/interfaces/LiquidationData.md)[]; `stats`: [`LiquidationStats`](../../types/interfaces/LiquidationStats.md); \}\>\>

Defined in: src/lib/services/data/liquidation.ts:62

获取快照数据

#### Parameters

##### timeRange

[`SnapshotTimeRange`](../../types/type-aliases/SnapshotTimeRange.md)

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<\{ `data`: [`LiquidationData`](../../types/interfaces/LiquidationData.md)[]; `stats`: [`LiquidationStats`](../../types/interfaces/LiquidationStats.md); \}\>\>

---

### fetchTrendData()

> **fetchTrendData**(`timeRange`): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]\>\>

Defined in: src/lib/services/data/liquidation.ts:107

获取趋势数据

#### Parameters

##### timeRange

[`TrendTimeRange`](../../types/type-aliases/TrendTimeRange.md)

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]\>\>

---

### fetchTrendDataWithFrameAndDuration()

> **fetchTrendDataWithFrameAndDuration**(`timeFrame`, `duration`): `Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]\>\>

Defined in: src/lib/services/data/liquidation.ts:148

获取带时间粒度的趋势数据

#### Parameters

##### timeFrame

[`TrendTimeFrame`](../../types/type-aliases/TrendTimeFrame.md)

##### duration

[`TrendDuration`](../../types/type-aliases/TrendDuration.md)

#### Returns

`Promise`\<[`ApiResponse`](../../types/interfaces/ApiResponse.md)\<[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]\>\>

---

### startAutoRefresh()

> **startAutoRefresh**(`interval`): `void`

Defined in: src/lib/services/data/liquidation.ts:368

开始自动刷新

#### Parameters

##### interval

`number` = `60000`

#### Returns

`void`

---

### stopAutoRefresh()

> **stopAutoRefresh**(): `void`

Defined in: src/lib/services/data/liquidation.ts:382

停止自动刷新

#### Returns

`void`
