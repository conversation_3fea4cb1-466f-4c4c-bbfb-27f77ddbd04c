# 状态管理文档

本项目使用 Svelte 的内置状态管理系统，采用分层和模块化的组织方式。

## 🏗️ 架构概览

```text
src/lib/stores/
├── features/           # 业务状态管理
│   ├── dashboard.ts   # 仪表板状态
│   ├── dataQuery.ts   # 数据查询状态
│   └── liquidation.ts # 爆仓数据状态 🆕
├── ui/                # UI 状态管理
│   ├── sidebar.ts     # 侧边栏状态
│   └── tabs.ts        # 标签页状态
└── index.ts          # 统一导出
```

## 📊 业务状态管理

### Dashboard Store

仪表板状态管理，包含图表配置、数据和加载状态。

```typescript
import { dashboardStore } from '$lib/stores/features/dashboard';

// 订阅状态变化
$: console.log('Dashboard state:', $dashboardStore);

// 加载数据
await dashboardStore.loadDashboardData();

// 设置时间范围
dashboardStore.setTimeRange({
  selectedTimeRange: 'week',
  customStartTime: '2024-01-01',
  customEndTime: '2024-01-07',
  selectedTimeZone: 'UTC',
});

// 切换图表可见性
dashboardStore.toggleChartVisibility('chart-1');
```

**状态结构:**

```typescript
interface DashboardState {
  chartConfigs: ChartConfig[];
  stats: DashboardStats;
  chartData: ChartData;
  selectedTimeRange: TimeRange;
  customStartTime: string;
  customEndTime: string;
  selectedTimeZone: string;
  isLoading: boolean;
  error: string | null;
  refreshInterval: number | null;
}
```

**主要方法:**

- `loadDashboardData()` - 加载仪表板数据
- `refreshStats()` - 刷新统计数据
- `refreshChartData()` - 刷新图表数据
- `setTimeRange()` - 设置时间范围
- `toggleChartVisibility()` - 切换图表可见性
- `setRefreshInterval()` - 设置刷新间隔

### DataQuery Store

数据查询状态管理，包含查询参数、结果和分页信息。

```typescript
import { dataQueryStore } from '$lib/stores/features/dataQuery';

// 执行查询
await dataQueryStore.executeQuery();

// 更新查询参数
dataQueryStore.updateQueryParams({
  keyword: '新关键词',
  status: '有效',
});

// 分页导航
dataQueryStore.goToPage(2);

// 导出数据
await dataQueryStore.exportData();
```

**状态结构:**

```typescript
interface DataQueryState {
  queryParams: QueryParams;
  results: QueryResults;
  isFilterExpanded: boolean;
  error: string | null;
}
```

### Liquidation Store 🆕

专业的加密货币爆仓数据状态管理，支持快照数据、趋势分析和实时监控。

```typescript
import { liquidationStore } from '$lib/stores/features/liquidation';

// 加载快照数据
await liquidationStore.loadSnapshotData('24h');

// 加载趋势数据
await liquidationStore.loadDetailedTrendData('1d', '7d');

// 设置视图模式
liquidationStore.setTrendViewMode('comparison');

// 更新时间筛选器
liquidationStore.updateTimeFilters({
  snapshotTimeRange: '12h',
  trendTimeFrame: '1h',
  trendDuration: '3d',
});

// 开始自动刷新
liquidationStore.startAutoRefresh(60000);

// 手动刷新所有数据
await liquidationStore.refreshAllData();
```

**状态结构：**

```typescript
interface LiquidationState {
  chartConfigs: ChartConfig[];        // 图表配置
  stats: LiquidationStats;           // 统计数据
  timeFilters: TimeFilters;          // 时间筛选器
  snapshotData: LiquidationData[];   // 快照数据
  trendData: LiquidationData[];      // 趋势数据
  isLoading: boolean;                // 加载状态
  error: string | null;              // 错误信息
  refreshInterval: number | null;    // 刷新间隔
}
```

**主要方法：**

- `loadSnapshotData(timeRange)` - 加载快照数据
- `loadDetailedTrendData(timeFrame, duration)` - 加载趋势数据
- `updateTimeFilters(filters)` - 更新时间筛选器
- `setTrendViewMode(mode)` - 设置趋势视图模式
- `refreshAllData()` - 刷新所有数据
- `startAutoRefresh(interval)` - 开始自动刷新
- `stopAutoRefresh()` - 停止自动刷新

**派生 Stores：**

```typescript
// 快照图表（KPI + 饼图）
const snapshotCharts = liquidationStore.snapshotCharts;

// 趋势图表（线图 + 面积图）
const trendCharts = liquidationStore.trendCharts;

// 统计数据
const stats = derived(liquidationStore, ($store) => $store.stats);

// 加载状态
const isLoading = derived(liquidationStore, ($store) => $store.isLoading);
```

**支持的时间范围：**

- 快照数据：`'1h'`, `'4h'`, `'12h'`, `'24h'`
- 趋势时间框架：`'15m'`, `'1h'`, `'4h'`, `'1d'`
- 趋势持续时间：`'1h'`, `'4h'`, `'12h'`, `'1d'`, `'3d'`, `'7d'`, `'30d'`

**视图模式：**

- `'overall'` - 全市场视图
- `'comparison'` - 主流币 vs 山寨币对比视图

## 🎨 UI 状态管理

### Sidebar Store

侧边栏状态管理，控制侧边栏的展开/收起状态。

```typescript
import { sidebarStore } from '$lib/stores/ui/sidebar';

// 切换侧边栏
sidebarStore.toggle();

// 设置侧边栏状态
sidebarStore.setExpanded(true);

// 订阅状态变化
$: isExpanded = $sidebarStore.isExpanded;
```

### Tabs Store

标签页状态管理，支持动态添加和关闭标签页。

```typescript
import { tabs, activeTabId, openTab, closeTab } from '$lib/stores/ui/tabs';

// 打开新标签页
openTab({
  href: '/dashboard',
  label: '仪表板',
  icon: DashboardIcon,
});

// 关闭标签页
closeTab('tab-id');

// 设置活动标签页
setActiveTab('tab-id');

// 订阅标签页状态
$: currentTabs = $tabs;
$: currentActiveTab = $activeTabId;
```

## 🔄 响应式模式

### 派生状态 (Derived Stores)

使用派生状态自动计算衍生值。

```typescript
import { derived } from 'svelte/store';
import { dashboardStore } from '$lib/stores/features/dashboard';

// 派生可见图表
const visibleCharts = derived(dashboardStore, ($dashboard) =>
  $dashboard.chartConfigs.filter((chart) => chart.visible)
);

// 派生加载状态
const isLoading = derived(dashboardStore, ($dashboard) => $dashboard.isLoading);
```

### 自定义 Store

创建自定义的响应式 store。

```typescript
import { writable, derived } from 'svelte/store';

function createCustomStore() {
  const { subscribe, set, update } = writable(initialState);

  return {
    subscribe,
    // 自定义方法
    increment: () => update((n) => n + 1),
    decrement: () => update((n) => n - 1),
    reset: () => set(0),
  };
}

export const customStore = createCustomStore();
```

## 📝 最佳实践

### 1. 状态结构设计

- 保持状态结构扁平化
- 避免深层嵌套的对象
- 使用明确的类型定义

### 2. 状态更新

- 使用 `update` 函数进行状态更新
- 避免直接修改状态对象
- 保持状态更新的原子性

### 3. 异步操作

- 在 store 中处理异步逻辑
- 提供加载状态和错误处理
- 使用 try-catch 包装异步操作

### 4. 性能优化

- 使用派生状态避免重复计算
- 合理使用 `readable` 和 `writable`
- 避免不必要的状态订阅

## 🧪 测试状态管理

### 单元测试

```typescript
import { get } from 'svelte/store';
import { dashboardStore } from '$lib/stores/features/dashboard';

describe('Dashboard Store', () => {
  test('should load dashboard data', async () => {
    await dashboardStore.loadDashboardData();
    const state = get(dashboardStore);
    expect(state.isLoading).toBe(false);
    expect(state.stats).toBeDefined();
  });

  test('should toggle chart visibility', () => {
    dashboardStore.toggleChartVisibility('chart-1');
    const state = get(dashboardStore);
    const chart = state.chartConfigs.find((c) => c.id === 'chart-1');
    expect(chart?.visible).toBe(false);
  });
});
```

### 集成测试

```typescript
import { render, screen } from '@testing-library/svelte';
import Dashboard from '$lib/components/features/dashboard/Dashboard.svelte';

test('should display dashboard data', async () => {
  render(Dashboard);

  // 等待数据加载
  await screen.findByText('仪表板数据');

  // 验证状态更新
  expect(screen.getByText('总用户数')).toBeInTheDocument();
});
```

## 🔧 调试工具

### Svelte DevTools

使用 Svelte DevTools 浏览器扩展来调试状态。

### 自定义调试

```typescript
import { writable } from 'svelte/store';

function createDebuggableStore(name, initialValue) {
  const store = writable(initialValue);

  // 添加调试日志
  store.subscribe((value) => {
    console.log(`[${name}] State updated:`, value);
  });

  return store;
}
```
