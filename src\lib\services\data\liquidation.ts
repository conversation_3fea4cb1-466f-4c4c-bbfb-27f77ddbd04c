// 爆仓数据服务 - 整合原有的爆仓数据功能
import type { ApiResponse } from '$lib/types/core';
import type {
  LiquidationData,
  LiquidationRankResponse,
  LiquidationStats,
  RankQueryParams,
  SnapshotTimeRange,
  TrendDuration,
  TrendTimeFrame,
  TrendTimeRange,
} from '$lib/types/business';

/**
 * 爆仓数据服务类
 *
 * 专门处理加密货币市场的爆仓数据，提供快照数据、趋势分析和实时监控功能。
 * 支持多种时间粒度和视图模式，包括全市场视图和主流币/山寨币对比视图。
 *
 * @example
 * ```typescript
 * import { liquidationDataService } from '$lib/services/data/liquidation';
 *
 * // 获取24小时快照数据
 * const snapshotResponse = await liquidationDataService.fetchSnapshotData('24h');
 * if (snapshotResponse.status === 'success') {
 *   const { data, stats } = snapshotResponse.data;
 *   console.log(`总爆仓金额: $${stats.totalAmount.toLocaleString()}`);
 *   console.log(`多空比: ${stats.longShortRatio.toFixed(2)}`);
 * }
 *
 * // 获取7天趋势数据
 * const trendResponse = await liquidationDataService.fetchTrendData('7d');
 *
 * // 开始自动刷新（每分钟）
 * liquidationDataService.startAutoRefresh(60000);
 *
 * // 停止自动刷新
 * liquidationDataService.stopAutoRefresh();
 * ```
 *
 * @category Services
 */
export class LiquidationDataService {
  private baseURL: string;
  private refreshInterval: number | null = null;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || '/api';
  }

  /**
   * 获取快照数据
   */
  async fetchSnapshotData(timeRange: SnapshotTimeRange): Promise<
    ApiResponse<{
      data: LiquidationData[];
      stats: LiquidationStats;
    }>
  > {
    const response = await fetch(`${this.baseURL}/liquidation/snapshot`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ timeRange }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      status: 'success',
      data,
    };
  }

  /**
   * 获取趋势数据
   */
  async fetchTrendData(timeRange: TrendTimeRange): Promise<ApiResponse<LiquidationData[]>> {
    const response = await fetch(`${this.baseURL}/liquidation/trend`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ timeRange }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      status: 'success',
      data,
    };
  }

  /**
   * 获取带时间粒度的趋势数据
   */
  async fetchTrendDataWithFrameAndDuration(
    timeFrame: TrendTimeFrame,
    duration: TrendDuration
  ): Promise<ApiResponse<LiquidationData[]>> {
    const response = await fetch(`${this.baseURL}/liquidation/trend-detailed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ timeFrame, duration }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      status: 'success',
      data,
    };
  }

  /**
   * 开始自动刷新
   */
  startAutoRefresh(interval: number = 60000): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    this.refreshInterval = window.setInterval(() => {
      // 这里可以触发数据刷新的回调
      // 实际项目中可以在这里触发数据刷新事件
    }, interval);
  }

  /**
   * 停止自动刷新
   */
  stopAutoRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * 获取清算排行榜数据
   *
   * @param params 查询参数，包括排序、筛选、分页等
   * @returns 排行榜数据响应
   *
   * @example
   * ```typescript
   * // 获取按清算金额排序的前30名
   * const rankResponse = await liquidationDataService.fetchRankData({
   *   sortField: 'totalAmount',
   *   sortDirection: 'desc',
   *   coinTypeFilter: 'all',
   *   timeRange: '24h'
   * });
   *
   * if (rankResponse.status === 'success') {
   *   const { items, summary } = rankResponse.data;
   *   console.log(`显示前 ${items.length} 个币种的排行榜`);
   *   items.forEach(item => {
   *     console.log(`${item.rank}. ${item.symbol}: $${item.totalAmount.toLocaleString()}`);
   *   });
   * }
   * ```
   */
  async fetchRankData(params: RankQueryParams): Promise<ApiResponse<LiquidationRankResponse>> {
    try {
      const response = await fetch(`${this.baseURL}/liquidation/rank`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        status: 'success',
        data,
      };
    } catch (error) {
      console.error('获取清算排行榜数据失败:', error);
      return {
        status: 'error',
        data: { items: [], updateTime: '', timeRange: '' },
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }
}

// 创建默认实例
export const liquidationDataService = new LiquidationDataService();
