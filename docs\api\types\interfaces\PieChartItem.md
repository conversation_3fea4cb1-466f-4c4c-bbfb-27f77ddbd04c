[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / PieChartItem

# Interface: PieChartItem

Defined in: src/lib/types/dashboard.ts:56

饼图数据项

## Example

```typescript
const pieData: PieChartItem = {
  name: '直接访问',
  value: 320,
};
```

## Properties

### name

> **name**: `string`

Defined in: src/lib/types/dashboard.ts:58

扇形区域名称

---

### value

> **value**: `number`

Defined in: src/lib/types/dashboard.ts:60

数据值，决定扇形区域大小
