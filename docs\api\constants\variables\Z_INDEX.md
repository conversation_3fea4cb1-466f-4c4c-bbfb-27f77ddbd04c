[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / Z_INDEX

# Variable: Z_INDEX

> `const` **Z_INDEX**: `object`

Defined in: src/lib/constants/ui.ts:10

## Type declaration

### DROPDOWN

> `readonly` **DROPDOWN**: `1000` = `1000`

### FIXED

> `readonly` **FIXED**: `1030` = `1030`

### MODAL

> `readonly` **MODAL**: `1050` = `1050`

### MODAL_BACKDROP

> `readonly` **MODAL_BACKDROP**: `1040` = `1040`

### POPOVER

> `readonly` **POPOVER**: `1060` = `1060`

### STICKY

> `readonly` **STICKY**: `1020` = `1020`

### TOAST

> `readonly` **TOAST**: `1080` = `1080`

### TOOLTIP

> `readonly` **TOOLTIP**: `1070` = `1070`
