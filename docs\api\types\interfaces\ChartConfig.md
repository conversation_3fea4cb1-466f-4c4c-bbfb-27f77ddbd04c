[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / ChartConfig

# Interface: ChartConfig

Defined in: src/lib/types/dashboard.ts:156

图表配置信息

定义图表的显示配置和元数据

## Example

```typescript
const config: ChartConfig = {
  id: 'sales-chart',
  component: SalesChart,
  visible: true,
  order: 1,
  title: '销售趋势',
  subTitle: '最近30天的销售数据',
};
```

## Properties

### component

> **component**: `Component`\<`any`, `any`, `any`\>

Defined in: src/lib/types/dashboard.ts:160

图表 Svelte 组件

---

### id

> **id**: `string`

Defined in: src/lib/types/dashboard.ts:158

图表唯一标识符

---

### order

> **order**: `number`

Defined in: src/lib/types/dashboard.ts:164

显示顺序

---

### subTitle

> **subTitle**: `string`

Defined in: src/lib/types/dashboard.ts:168

图表副标题

---

### title

> **title**: `string`

Defined in: src/lib/types/dashboard.ts:166

图表标题

---

### visible

> **visible**: `boolean`

Defined in: src/lib/types/dashboard.ts:162

是否可见
