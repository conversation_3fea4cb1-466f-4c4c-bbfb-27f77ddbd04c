/**
 * 表单相关类型定义
 * 
 * @category UI Types
 */

/**
 * 表单字段类型
 */
export type FormFieldType = 
  | 'text'
  | 'number'
  | 'email'
  | 'password'
  | 'select'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'date'
  | 'daterange'
  | 'textarea'
  | 'file';

/**
 * 表单字段配置
 */
export interface FormField {
  /** 字段名称 */
  name: string;
  /** 字段标签 */
  label: string;
  /** 字段类型 */
  type: FormFieldType;
  /** 是否必填 */
  required?: boolean;
  /** 占位符 */
  placeholder?: string;
  /** 默认值 */
  defaultValue?: any;
  /** 选项（用于 select、radio 等） */
  options?: Array<{ value: any; label: string; disabled?: boolean }>;
  /** 验证规则 */
  validation?: FormValidationRule[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 帮助文本 */
  helpText?: string;
  /** 字段描述 */
  description?: string;
}

/**
 * 表单验证规则
 */
export interface FormValidationRule {
  /** 规则类型 */
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  /** 规则值 */
  value?: any;
  /** 错误消息 */
  message: string;
  /** 自定义验证函数 */
  validator?: (value: any) => boolean;
}

/**
 * 表单验证错误
 */
export interface FormValidationError {
  /** 字段名称 */
  field: string;
  /** 错误消息 */
  message: string;
  /** 错误类型 */
  type: string;
}

/**
 * 表单状态
 */
export interface FormState {
  /** 表单值 */
  values: Record<string, any>;
  /** 验证错误 */
  errors: Record<string, string>;
  /** 是否正在提交 */
  isSubmitting: boolean;
  /** 是否已修改 */
  isDirty: boolean;
  /** 是否有效 */
  isValid: boolean;
  /** 已访问的字段 */
  touchedFields: Set<string>;
}

/**
 * 表单配置
 */
export interface FormConfig {
  /** 表单字段 */
  fields: FormField[];
  /** 表单标题 */
  title?: string;
  /** 表单描述 */
  description?: string;
  /** 提交按钮文本 */
  submitText?: string;
  /** 重置按钮文本 */
  resetText?: string;
  /** 是否显示重置按钮 */
  showReset?: boolean;
  /** 表单布局 */
  layout?: 'vertical' | 'horizontal' | 'inline';
  /** 字段间距 */
  spacing?: 'compact' | 'normal' | 'loose';
}

/**
 * 表单事件
 */
export interface FormEvents {
  /** 提交事件 */
  onSubmit?: (values: Record<string, any>) => void | Promise<void>;
  /** 重置事件 */
  onReset?: () => void;
  /** 字段变化事件 */
  onChange?: (field: string, value: any) => void;
  /** 字段失焦事件 */
  onBlur?: (field: string) => void;
  /** 验证事件 */
  onValidate?: (errors: FormValidationError[]) => void;
}

/**
 * 表单选项
 */
export interface FormOptions {
  /** 是否自动验证 */
  autoValidate?: boolean;
  /** 验证触发时机 */
  validateOn?: 'change' | 'blur' | 'submit';
  /** 是否保留表单状态 */
  preserveState?: boolean;
  /** 是否启用自动保存 */
  autoSave?: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number;
}

/**
 * 表单步骤（用于多步表单）
 */
export interface FormStep {
  /** 步骤ID */
  id: string;
  /** 步骤标题 */
  title: string;
  /** 步骤描述 */
  description?: string;
  /** 步骤字段 */
  fields: string[];
  /** 是否可跳过 */
  optional?: boolean;
  /** 验证规则 */
  validation?: FormValidationRule[];
}

/**
 * 多步表单状态
 */
export interface MultiStepFormState extends FormState {
  /** 当前步骤索引 */
  currentStep: number;
  /** 已完成的步骤 */
  completedSteps: Set<number>;
  /** 步骤配置 */
  steps: FormStep[];
  /** 是否可以前进 */
  canGoNext: boolean;
  /** 是否可以后退 */
  canGoPrevious: boolean;
}

/**
 * 表单提交结果
 */
export interface FormSubmitResult {
  /** 是否成功 */
  success: boolean;
  /** 结果数据 */
  data?: any;
  /** 错误信息 */
  error?: string;
  /** 字段错误 */
  fieldErrors?: Record<string, string>;
}
