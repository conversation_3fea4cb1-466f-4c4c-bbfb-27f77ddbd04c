/**
 * 通用数据处理工具函数
 *
 * 提供数据转换、格式化、聚合等通用功能
 *
 * @category Utils
 */

import { formatDate,formatNumber } from './formatters';
import { createModuleLogger } from './logger';

const dataLogger = createModuleLogger('data-processors');

/**
 * 数据聚合类型
 */
export type AggregationType = 'sum' | 'avg' | 'max' | 'min' | 'count' | 'median' | 'first' | 'last';

/**
 * 排序方向
 */
export type SortDirection = 'asc' | 'desc';

/**
 * 分组配置
 */
export interface GroupByConfig {
  /** 分组字段 */
  field: string;
  /** 聚合配置 */
  aggregations: {
    [key: string]: AggregationType;
  };
}

/**
 * 排序配置
 */
export interface SortConfig {
  /** 排序字段 */
  field: string;
  /** 排序方向 */
  direction: SortDirection;
}

/**
 * 过滤配置
 */
export interface FilterConfig {
  /** 过滤字段 */
  field: string;
  /** 操作符 */
  operator:
    | 'eq'
    | 'ne'
    | 'gt'
    | 'gte'
    | 'lt'
    | 'lte'
    | 'in'
    | 'nin'
    | 'contains'
    | 'startsWith'
    | 'endsWith';
  /** 过滤值 */
  value: any;
}

/**
 * 数据转换配置
 */
export interface TransformConfig {
  /** 字段映射 */
  fieldMapping?: { [oldField: string]: string };
  /** 值转换函数 */
  valueTransforms?: { [field: string]: (value: any) => any };
  /** 计算字段 */
  computedFields?: { [newField: string]: (row: any) => any };
}

/**
 * 分页配置
 */
export interface PaginationConfig {
  /** 页码（从1开始） */
  page: number;
  /** 每页大小 */
  pageSize: number;
}

/**
 * 分页结果
 */
export interface PaginatedResult<T> {
  /** 数据 */
  data: T[];
  /** 总数 */
  total: number;
  /** 当前页 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
  /** 是否有下一页 */
  hasNext: boolean;
  /** 是否有上一页 */
  hasPrev: boolean;
}

/**
 * 数据处理器类
 */
export class DataProcessor<T = any> {
  private data: T[];

  constructor(data: T[]) {
    this.data = Array.isArray(data) ? [...data] : [];
  }

  /**
   * 过滤数据
   */
  filter(filters: FilterConfig[]): DataProcessor<T> {
    if (!filters || filters.length === 0) {
      return this;
    }

    const filteredData = this.data.filter((row) => {
      return filters.every((filter) => {
        const value = this.getNestedValue(row, filter.field);
        return this.applyFilter(value, filter.operator, filter.value);
      });
    });

    return new DataProcessor(filteredData);
  }

  /**
   * 排序数据
   */
  sort(sorts: SortConfig[]): DataProcessor<T> {
    if (!sorts || sorts.length === 0) {
      return this;
    }

    const sortedData = [...this.data].sort((a, b) => {
      for (const sort of sorts) {
        const aValue = this.getNestedValue(a, sort.field);
        const bValue = this.getNestedValue(b, sort.field);
        const comparison = this.compareValues(aValue, bValue);

        if (comparison !== 0) {
          return sort.direction === 'asc' ? comparison : -comparison;
        }
      }
      return 0;
    });

    return new DataProcessor(sortedData);
  }

  /**
   * 分组聚合数据
   */
  groupBy(config: GroupByConfig): DataProcessor<any> {
    const groups: { [key: string]: T[] } = {};

    // 分组
    this.data.forEach((row) => {
      const groupKey = String(this.getNestedValue(row, config.field));
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(row);
    });

    // 聚合
    const aggregatedData = Object.entries(groups).map(([groupKey, groupData]) => {
      const result: any = { [config.field]: groupKey };

      Object.entries(config.aggregations).forEach(([field, aggregationType]) => {
        result[field] = this.aggregate(groupData, field, aggregationType);
      });

      return result;
    });

    return new DataProcessor(aggregatedData);
  }

  /**
   * 转换数据
   */
  transform(config: TransformConfig): DataProcessor<any> {
    const transformedData = this.data.map((row) => {
      const newRow: any = { ...row };

      // 字段映射
      if (config.fieldMapping) {
        Object.entries(config.fieldMapping).forEach(([oldField, newField]) => {
          if (oldField in newRow) {
            newRow[newField] = newRow[oldField];
            delete newRow[oldField];
          }
        });
      }

      // 值转换
      if (config.valueTransforms) {
        Object.entries(config.valueTransforms).forEach(([field, transform]) => {
          if (field in newRow) {
            newRow[field] = transform(newRow[field]);
          }
        });
      }

      // 计算字段
      if (config.computedFields) {
        Object.entries(config.computedFields).forEach(([newField, compute]) => {
          newRow[newField] = compute(newRow);
        });
      }

      return newRow;
    });

    return new DataProcessor(transformedData);
  }

  /**
   * 分页数据
   */
  paginate(config: PaginationConfig): PaginatedResult<T> {
    const { page, pageSize } = config;
    const total = this.data.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const data = this.data.slice(startIndex, endIndex);

    return {
      data,
      total,
      page,
      pageSize,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 去重数据
   */
  distinct(field?: string): DataProcessor<T> {
    if (!field) {
      // 基于整个对象去重
      const seen = new Set();
      const distinctData = this.data.filter((row) => {
        const key = JSON.stringify(row);
        if (seen.has(key)) {
          return false;
        }
        seen.add(key);
        return true;
      });
      return new DataProcessor(distinctData);
    } else {
      // 基于特定字段去重
      const seen = new Set();
      const distinctData = this.data.filter((row) => {
        const value = this.getNestedValue(row, field);
        if (seen.has(value)) {
          return false;
        }
        seen.add(value);
        return true;
      });
      return new DataProcessor(distinctData);
    }
  }

  /**
   * 限制数据数量
   */
  limit(count: number): DataProcessor<T> {
    return new DataProcessor(this.data.slice(0, count));
  }

  /**
   * 跳过数据
   */
  skip(count: number): DataProcessor<T> {
    return new DataProcessor(this.data.slice(count));
  }

  /**
   * 获取处理后的数据
   */
  toArray(): T[] {
    return [...this.data];
  }

  /**
   * 获取数据统计信息
   */
  getStats(field: string): {
    count: number;
    sum: number;
    avg: number;
    min: number;
    max: number;
    median: number;
  } {
    const values = this.data
      .map((row) => this.getNestedValue(row, field))
      .filter((value) => typeof value === 'number' && !isNaN(value));

    if (values.length === 0) {
      return { count: 0, sum: 0, avg: 0, min: 0, max: 0, median: 0 };
    }

    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    const sorted = [...values].sort((a, b) => a - b);
    const median =
      sorted.length % 2 === 0
        ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
        : sorted[Math.floor(sorted.length / 2)];

    return { count: values.length, sum, avg, min, max, median };
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 应用过滤条件
   */
  private applyFilter(value: any, operator: FilterConfig['operator'], filterValue: any): boolean {
    switch (operator) {
      case 'eq':
        return value === filterValue;
      case 'ne':
        return value !== filterValue;
      case 'gt':
        return value > filterValue;
      case 'gte':
        return value >= filterValue;
      case 'lt':
        return value < filterValue;
      case 'lte':
        return value <= filterValue;
      case 'in':
        return Array.isArray(filterValue) && filterValue.includes(value);
      case 'nin':
        return Array.isArray(filterValue) && !filterValue.includes(value);
      case 'contains':
        return String(value).includes(String(filterValue));
      case 'startsWith':
        return String(value).startsWith(String(filterValue));
      case 'endsWith':
        return String(value).endsWith(String(filterValue));
      default:
        return true;
    }
  }

  /**
   * 比较值
   */
  private compareValues(a: any, b: any): number {
    if (a === b) return 0;
    if (a == null) return -1;
    if (b == null) return 1;

    // 数字比较
    if (typeof a === 'number' && typeof b === 'number') {
      return a - b;
    }

    // 日期比较
    if (a instanceof Date && b instanceof Date) {
      return a.getTime() - b.getTime();
    }

    // 字符串比较
    return String(a).localeCompare(String(b));
  }

  /**
   * 聚合计算
   */
  private aggregate(data: T[], field: string, type: AggregationType): any {
    const values = data
      .map((row) => this.getNestedValue(row, field))
      .filter((value) => value != null);

    if (values.length === 0) {
      return null;
    }

    switch (type) {
      case 'sum':
        return values.reduce((a, b) => Number(a) + Number(b), 0);
      case 'avg':
        const sum = values.reduce((a, b) => Number(a) + Number(b), 0);
        return sum / values.length;
      case 'max':
        return Math.max(...values.map(Number));
      case 'min':
        return Math.min(...values.map(Number));
      case 'count':
        return values.length;
      case 'median':
        const sorted = values.map(Number).sort((a, b) => a - b);
        return sorted.length % 2 === 0
          ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
          : sorted[Math.floor(sorted.length / 2)];
      case 'first':
        return values[0];
      case 'last':
        return values[values.length - 1];
      default:
        return null;
    }
  }
}

/**
 * 创建数据处理器
 */
export function createDataProcessor<T>(data: T[]): DataProcessor<T> {
  return new DataProcessor(data);
}

/**
 * 数组分块
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * 数组去重
 */
export function uniqueArray<T>(array: T[], keyFn?: (item: T) => any): T[] {
  if (!keyFn) {
    return [...new Set(array)];
  }

  const seen = new Set();
  return array.filter((item) => {
    const key = keyFn(item);
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

/**
 * 数组扁平化
 */
export function flattenArray<T>(array: (T | T[])[], depth: number = 1): T[] {
  if (depth <= 0) {
    return array as T[];
  }

  return array.reduce<T[]>((acc, item) => {
    if (Array.isArray(item)) {
      acc.push(...flattenArray(item, depth - 1));
    } else {
      acc.push(item);
    }
    return acc;
  }, []);
}

/**
 * 对象数组转换为键值对
 */
export function arrayToKeyValue<T>(
  array: T[],
  keyField: keyof T,
  valueField: keyof T
): { [key: string]: any } {
  return array.reduce(
    (acc, item) => {
      const key = String(item[keyField]);
      acc[key] = item[valueField];
      return acc;
    },
    {} as { [key: string]: any }
  );
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item)) as unknown as T;
  }

  const cloned = {} as T;
  Object.keys(obj).forEach((key) => {
    cloned[key as keyof T] = deepClone((obj as any)[key]);
  });

  return cloned;
}

/**
 * 安全的 JSON 解析
 */
export function safeJsonParse<T>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json);
  } catch (error) {
    dataLogger.warn('JSON parse failed', {
      json,
      error: error instanceof Error ? error.message : String(error),
    });
    return defaultValue;
  }
}

/**
 * 安全的 JSON 字符串化
 */
export function safeJsonStringify(obj: any, defaultValue: string = '{}'): string {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    dataLogger.warn('JSON stringify failed', {
      error: error instanceof Error ? error.message : String(error),
    });
    return defaultValue;
  }
}
