<script lang="ts">
  import { Search, X } from 'lucide-svelte';
  import { onMount } from 'svelte';

  import { Button } from '$lib/components/ui/button';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { coinSearchDataService } from '$lib/services/data/coinSearch';
  import type { CoinOption } from '$lib/types';

  interface Props {
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    class?: string;
    onValueChange?: (value: string) => void;
    onSelect?: (option: CoinOption) => void;
  }

  let {
    value = $bindable(''),
    placeholder = '请输入币种名称或符号',
    disabled = false,
    class: className = '',
    onValueChange,
    onSelect,
  }: Props = $props();

  // 本地状态用于输入框显示
  let inputValue = $state(value);

  // 同步外部 value 变化到内部 inputValue
  $effect(() => {
    inputValue = value;
  });

  let inputElement: HTMLInputElement;
  let isOpen = $state(false);
  let isLoading = $state(false);
  let options = $state<CoinOption[]>([]);
  let selectedIndex = $state(-1);
  let searchTimeout: ReturnType<typeof setTimeout>;

  // 防抖搜索
  async function handleSearch(query: string) {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    searchTimeout = setTimeout(async () => {
      isLoading = true;
      try {
        const results = await coinSearchDataService.searchCoins(query, 10);
        options = results;
        selectedIndex = -1;
        isOpen = results.length > 0;
      } catch (error) {
        console.error('搜索币种失败:', error);
        options = [];
        isOpen = false;
      } finally {
        isLoading = false;
      }
    }, 300);
  }

  // 处理输入变化
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = target.value;
    inputValue = newValue;
    value = newValue;

    if (onValueChange) {
      onValueChange(newValue);
    }

    if (newValue.trim()) {
      handleSearch(newValue);
    } else {
      // 空输入时显示热门币种
      loadPopularCoins();
    }
  }

  // 加载热门币种
  async function loadPopularCoins() {
    isLoading = true;
    try {
      const results = await coinSearchDataService.getPopularCoins(10);
      options = results;
      selectedIndex = -1;
      isOpen = true;
    } catch (error) {
      console.error('加载热门币种失败:', error);
      options = [];
      isOpen = false;
    } finally {
      isLoading = false;
    }
  }

  // 处理选项选择
  function handleSelect(option: CoinOption) {
    inputValue = option.label;
    value = option.label;
    isOpen = false;
    selectedIndex = -1;

    if (onSelect) {
      onSelect(option);
    }
    if (onValueChange) {
      onValueChange(option.value);
    }
  }

  // 处理键盘导航
  function handleKeydown(event: KeyboardEvent) {
    if (!isOpen || options.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        selectedIndex = selectedIndex < options.length - 1 ? selectedIndex + 1 : 0;
        break;
      case 'ArrowUp':
        event.preventDefault();
        selectedIndex = selectedIndex > 0 ? selectedIndex - 1 : options.length - 1;
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < options.length) {
          handleSelect(options[selectedIndex]);
        }
        break;
      case 'Escape':
        event.preventDefault();
        isOpen = false;
        selectedIndex = -1;
        break;
    }
  }

  // 处理输入框聚焦
  function handleFocus() {
    if (value.trim()) {
      handleSearch(value);
    } else {
      loadPopularCoins();
    }
  }

  // 清除输入
  function clearInput() {
    inputValue = '';
    value = '';
    isOpen = false;
    selectedIndex = -1;
    if (onValueChange) {
      onValueChange('');
    }
    inputElement?.focus();
  }

  // 组件挂载时清理定时器
  onMount(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  });
</script>

<div class="relative {className}">
  <div class="relative">
    <Search class="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
    <input
      bind:this={inputElement}
      bind:value={inputValue}
      {placeholder}
      {disabled}
      class="border-input file:text-foreground placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 pr-9 pl-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
      oninput={handleInput}
      onfocus={handleFocus}
      onkeydown={handleKeydown}
    />
    {#if inputValue}
      <Button
        variant="ghost"
        size="sm"
        class="absolute top-1/2 right-1 h-7 w-7 -translate-y-1/2 p-0 hover:bg-transparent"
        onclick={clearInput}
      >
        <X class="h-4 w-4" />
      </Button>
    {/if}
  </div>

  {#if isOpen}
    <div
      class="bg-popover text-popover-foreground absolute z-50 mt-1 w-full rounded-md border p-0 shadow-md outline-none"
    >
      <div class="max-h-60 overflow-y-auto">
        {#if isLoading}
          <div class="space-y-2 p-2">
            {#each Array(5) as _}
              <div class="flex items-center space-x-3 p-2">
                <Skeleton class="h-6 w-6 rounded-full" />
                <div class="flex-1 space-y-1">
                  <Skeleton class="h-4 w-3/4" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
              </div>
            {/each}
          </div>
        {:else if options.length > 0}
          <div class="p-1">
            {#each options as option, index}
              <button
                class="hover:bg-accent hover:text-accent-foreground flex w-full items-center space-x-3 rounded-sm px-2 py-2 text-left text-sm {selectedIndex ===
                index
                  ? 'bg-accent text-accent-foreground'
                  : ''}"
                onclick={() => handleSelect(option)}
              >
                <img
                  src={option.image}
                  alt={option.symbol}
                  class="h-6 w-6 rounded-full"
                  loading="lazy"
                />
                <div class="min-w-0 flex-1">
                  <div class="truncate font-medium">{option.label}</div>
                  <div class="text-muted-foreground text-xs">{option.symbol}</div>
                </div>
              </button>
            {/each}
          </div>
        {:else}
          <div class="text-muted-foreground p-4 text-center text-sm">未找到相关币种</div>
        {/if}
      </div>
    </div>
  {/if}
</div>
