[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [services](../README.md) / BaseApiService

# Class: BaseApiService

Defined in: src/lib/services/api/base.ts:8

基础 API 服务类
提供统一的请求处理、错误处理和响应格式化

## Extended by

- [`ChartsApiService`](ChartsApiService.md)
- [`DashboardApiService`](DashboardApiService.md)

## Constructors

### Constructor

> **new BaseApiService**(`baseURL?`): `BaseApiService`

Defined in: src/lib/services/api/base.ts:12

#### Parameters

##### baseURL?

`string`

#### Returns

`BaseApiService`
