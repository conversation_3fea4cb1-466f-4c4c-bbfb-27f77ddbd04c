[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [stores](../README.md) / legacyDashboardStore

# Variable: legacyDashboardStore

> `const` **legacyDashboardStore**: `object`

Defined in: src/lib/stores/dashboard/liquidation.ts:177

## Type declaration

### charts

> **charts**: `Readable`\<[`ChartConfig`](../../types/interfaces/ChartConfig.md)[]\>

### loading

> **loading**: `Readable`\<`boolean`\>

### refreshInterval

> **refreshInterval**: `Readable`\<`null` \| `number`\>

### reorderCharts()

> **reorderCharts**: (`newOrder`) => `void`

#### Parameters

##### newOrder

`string`[]

#### Returns

`void`

### resetChartConfigs()

> **resetChartConfigs**: () => `void`

#### Returns

`void`

### setLoading()

> **setLoading**: (`isLoading`) => `void`

#### Parameters

##### isLoading

`boolean`

#### Returns

`void`

### setRefreshInterval()

> **setRefreshInterval**: (`refreshInterval`) => `void`

#### Parameters

##### refreshInterval

`null` | `number`

#### Returns

`void`

### setTimeRange()

> **setTimeRange**: (`__namedParameters`) => `void`

#### Parameters

##### \_\_namedParameters

###### customEndTime?

`string` = `''`

###### customStartTime?

`string` = `''`

###### selectedTimeRange

`string`

###### selectedTimeZone?

`string` = `'UTC'`

#### Returns

`void`

### stats

> **stats**: `Readable`\<[`DashboardStats`](../../types/interfaces/DashboardStats.md)\>

### subscribe()

> **subscribe**: (`this`, `run`, `invalidate?`) => `Unsubscriber`

Subscribe on value changes.

#### Parameters

##### this

`void`

##### run

`Subscriber`\<\{ `chartConfigs`: [`ChartConfig`](../../types/interfaces/ChartConfig.md)[]; `chartData`: [`ChartData`](../../types/interfaces/ChartData.md); `customEndTime`: `string`; `customStartTime`: `string`; `isLoading`: `boolean`; `refreshInterval`: `null` \| `number`; `selectedTimeRange`: [`TimeRange`](../../types/type-aliases/TimeRange.md); `selectedTimeZone`: `string`; `stats`: [`DashboardStats`](../../types/interfaces/DashboardStats.md); \}\>

subscription callback

##### invalidate?

() => `void`

cleanup callback

#### Returns

`Unsubscriber`

### toggleChartVisibility()

> **toggleChartVisibility**: (`chartId`) => `void`

#### Parameters

##### chartId

`string`

#### Returns

`void`

### updateChartData()

> **updateChartData**: (`chartData`) => `void`

#### Parameters

##### chartData

[`ChartData`](../../types/interfaces/ChartData.md)

#### Returns

`void`

### updateStats()

> **updateStats**: (`stats`) => `void`

#### Parameters

##### stats

[`DashboardStats`](../../types/interfaces/DashboardStats.md)

#### Returns

`void`
