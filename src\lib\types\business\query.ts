/**
 * 查询相关类型定义
 * 
 * @category Business Types
 */

import type { MarketEventType } from './market';
import type { PaginationState, AmountRange, TimeRange } from '../core';

/**
 * 基础查询参数
 */
export interface BaseQueryParams {
  /** 关键词搜索 */
  keyword: string;
  /** 选中的币种 */
  selectedCoins: string[];
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
  /** 状态过滤 */
  status: string;
  /** 事件类型 */
  type: MarketEventType[];
}

/**
 * 复合条件查询参数
 */
export interface CompoundQueryParams {
  /** 时间范围 */
  timeRange: TimeRange;
  /** 自定义日期范围 */
  customDateRange?: {
    start: string;
    end: string;
  };
  /** 数据类型 */
  dataType: DataType;
  /** 消息类型 */
  messageType: MessageType;
  /** 金额范围 */
  amountRange: AmountRange;
  /** 排序配置 */
  sort: {
    field: SortField;
    order: SortOrder;
  };
  /** 高级选项是否展开 */
  showAdvanced: boolean;
}

/**
 * 数据类型选项
 */
export type DataType =
  | 'all'          // 全部数据
  | 'liquidation'  // 清算数据
  | 'trade'        // 交易数据
  | 'market'       // 市场数据
  | 'notification' // 通知数据
  | 'system';      // 系统数据

/**
 * 消息类型选项
 */
export type MessageType =
  | 'all'          // 全部类型
  | 'info'         // 信息
  | 'success'      // 成功
  | 'warning'      // 警告
  | 'error'        // 错误
  | 'notification'; // 通知

/**
 * 排序字段选项
 */
export type SortField =
  | 'timestamp'    // 时间戳
  | 'amount'       // 金额
  | 'type'         // 类型
  | 'priority';    // 优先级

/**
 * 排序方向选项
 */
export type SortOrder = 'asc' | 'desc';

/**
 * 查询结果
 */
export interface QueryResults<T = any> {
  /** 数据项 */
  items: T[];
  /** 加载状态 */
  loading: boolean;
  /** 分页信息 */
  pagination: PaginationState;
}

/**
 * 查询状态
 */
export type QueryStatus = 
  | 'idle'      // 空闲状态
  | 'loading'   // 查询中
  | 'success'   // 查询成功
  | 'error';    // 查询失败

/**
 * 查询错误信息
 */
export interface QueryError {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 详细信息 */
  details?: string;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 查询表单状态
 */
export interface QueryFormState {
  /** 查询参数 */
  params: CompoundQueryParams;
  /** 查询状态 */
  status: QueryStatus;
  /** 错误信息 */
  error: QueryError | null;
  /** 表单验证错误 */
  validationErrors: Record<string, string>;
  /** 是否已修改 */
  isDirty: boolean;
}

/**
 * 导出参数
 */
export interface ExportParams {
  /** 查询参数 */
  queryParams: BaseQueryParams | CompoundQueryParams;
  /** 导出格式 */
  format: 'csv' | 'excel' | 'json';
  /** 是否包含表头 */
  includeHeaders: boolean;
  /** 文件名 */
  filename?: string;
}

/**
 * 查询条件摘要
 */
export interface QuerySummary {
  /** 时间范围描述 */
  timeRangeText: string;
  /** 数据类型描述 */
  dataTypeText: string;
  /** 金额范围描述 */
  amountRangeText: string;
  /** 其他条件数量 */
  otherConditionsCount: number;
  /** 是否有活跃条件 */
  hasActiveConditions: boolean;
}

/**
 * 查询历史记录
 */
export interface QueryHistory {
  /** 记录ID */
  id: string;
  /** 查询参数 */
  params: CompoundQueryParams;
  /** 查询时间 */
  timestamp: number;
  /** 结果数量 */
  resultCount: number;
  /** 查询名称 */
  name?: string;
}

/**
 * 保存的查询
 */
export interface SavedQuery {
  /** 查询ID */
  id: string;
  /** 查询名称 */
  name: string;
  /** 查询描述 */
  description?: string;
  /** 查询参数 */
  params: CompoundQueryParams;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
  /** 是否为收藏 */
  isFavorite: boolean;
}

/**
 * 消息类型选项
 */
export interface MessageTypeOption {
  /** 选项值 */
  value: MessageType;
  /** 显示标签 */
  label: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 描述信息 */
  description?: string;
  /** 颜色 */
  color?: string;
}

/**
 * 数据类型选项
 */
export interface DataTypeOption {
  /** 选项值 */
  value: DataType;
  /** 显示标签 */
  label: string;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 查询表单数据
 */
export interface QueryFormData {
  /** 时间范围 */
  timeRange: {
    startTime: string;
    endTime: string;
    timezone: string;
  };
  /** 自定义开始时间 */
  startTime?: string;
  /** 自定义结束时间 */
  endTime?: string;
  /** 数据类型 */
  dataType: DataType;
  /** 消息类型 */
  messageType: MessageType;
  /** 金额范围 */
  amountRange: AmountRange & {
    currency?: string;
  };
  /** 关键词 */
  keyword?: string;
  /** 排序字段 */
  sortField: SortField;
  /** 排序方向 */
  sortOrder: SortOrder;
  /** 是否只显示未读 */
  unreadOnly?: boolean;
  /** 优先级 */
  priority?: string;
}

/**
 * 查询表单状态
 */
export interface QueryFormState {
  /** 表单数据 */
  data: QueryFormData;
  /** 是否正在查询 */
  isQuerying: boolean;
  /** 查询错误 */
  error: QueryError | null;
  /** 表单验证错误 */
  validationErrors: Record<string, string>;
  /** 是否已修改 */
  isDirty: boolean;
}
