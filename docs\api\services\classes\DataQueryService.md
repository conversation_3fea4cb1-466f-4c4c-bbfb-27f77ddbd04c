[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [services](../README.md) / DataQueryService

# Class: DataQueryService

Defined in: src/lib/services/data/dataQuery.ts:40

数据查询服务类

提供灵活的数据查询、筛选、分页和导出功能。
支持多种查询条件组合，包括关键词搜索、状态筛选、日期范围等。
当 API 不可用时，会自动使用本地模拟数据。

## Example

```typescript
import { dataQueryService } from '$lib/services/data/dataQuery';

// 执行查询
const result = await dataQueryService.queryData({
  keyword: '搜索关键词',
  status: '有效',
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  page: 1,
  pageSize: 10,
});

console.log(`找到 ${result.total} 条记录`);
console.log('数据:', result.items);

// 导出数据
const blob = await dataQueryService.exportData(queryParams);
// 创建下载链接...
```

## Constructors

### Constructor

> **new DataQueryService**(): `DataQueryService`

Defined in: src/lib/services/data/dataQuery.ts:44

#### Returns

`DataQueryService`

## Methods

### exportData()

> **exportData**(`params`): `Promise`\<`Blob`\>

Defined in: src/lib/services/data/dataQuery.ts:189

导出数据

#### Parameters

##### params

[`QueryParams`](../../types/interfaces/QueryParams.md)

#### Returns

`Promise`\<`Blob`\>

---

### queryData()

> **queryData**(`params`): `Promise`\<`QueryResult`\>

Defined in: src/lib/services/data/dataQuery.ts:91

查询数据

根据提供的查询参数执行数据查询，支持分页。

#### Parameters

##### params

[`QueryParams`](../../types/interfaces/QueryParams.md) & `object`

查询参数对象

#### Returns

`Promise`\<`QueryResult`\>

Promise 包含查询结果和总数的对象

#### Example

```typescript
const result = await dataQueryService.queryData({
  keyword: 'test',
  status: '有效',
  type: '类型A',
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  page: 1,
  pageSize: 20,
});

console.log(`总共 ${result.total} 条记录`);
result.items.forEach((item) => {
  console.log(`${item.name} - ${item.status}`);
});
```
