<!-- src/lib/components/features/settings/CacheManagement.svelte -->
<script lang="ts">
  import { Database, HardDrive, RefreshCw, Trash2, TrendingUp } from 'lucide-svelte';
  import { onMount } from 'svelte';

  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Separator } from '$lib/components/ui/separator';
  import { currencyCacheStore } from '$lib/stores/features/currencyCache';
  import { notifications } from '$lib/stores/ui/notifications';
  import { formatBytes, formatNumber } from '$lib/utils/formatters';

  import type { CacheStats } from '$lib/services/cache/currencyPersistentCache';

  // 缓存统计状态
  let stats: CacheStats = $state({
    totalItems: 0,
    sizeInBytes: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    lastCleanupAt: Date.now(),
  });

  let isClearing = $state(false);
  let isRefreshing = $state(false);

  /**
   * 刷新统计信息
   */
  function refreshStats(): void {
    stats = currencyCacheStore.getStats();
  }

  /**
   * 清除所有缓存
   */
  async function clearAllCache(): Promise<void> {
    if (isClearing) return;

    try {
      isClearing = true;
      currencyCacheStore.clearCache();
      refreshStats();
      
      notifications.add({
        type: 'success',
        title: '缓存清除成功',
        message: '所有货币信息缓存已清除',
        duration: 3000,
      });
    } catch (error) {
      notifications.add({
        type: 'error',
        title: '缓存清除失败',
        message: error instanceof Error ? error.message : '未知错误',
        duration: 5000,
      });
    } finally {
      isClearing = false;
    }
  }

  /**
   * 刷新缓存统计
   */
  async function handleRefreshStats(): Promise<void> {
    if (isRefreshing) return;

    try {
      isRefreshing = true;
      refreshStats();
      
      notifications.add({
        type: 'info',
        title: '统计信息已刷新',
        message: '缓存统计信息已更新',
        duration: 2000,
      });
    } catch (error) {
      notifications.add({
        type: 'error',
        title: '刷新失败',
        message: error instanceof Error ? error.message : '未知错误',
        duration: 5000,
      });
    } finally {
      isRefreshing = false;
    }
  }

  /**
   * 格式化时间显示
   */
  function formatLastCleanup(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}小时${minutes}分钟前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  }

  /**
   * 获取命中率颜色
   */
  function getHitRateColor(hitRate: number): string {
    if (hitRate >= 0.8) return 'text-green-600 dark:text-green-400';
    if (hitRate >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  }

  /**
   * 获取缓存大小状态颜色
   */
  function getCacheSizeColor(sizeInBytes: number): string {
    const sizeMB = sizeInBytes / (1024 * 1024);
    if (sizeMB < 2) return 'text-green-600 dark:text-green-400';
    if (sizeMB < 4) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  }

  // 组件挂载时刷新统计信息
  onMount(() => {
    refreshStats();
    
    // 每30秒自动刷新统计信息
    const interval = setInterval(refreshStats, 30000);
    
    return () => {
      clearInterval(interval);
    };
  });
</script>

<Card>
  <CardHeader>
    <CardTitle class="flex items-center gap-2">
      <Database class="h-5 w-5" />
      缓存管理
    </CardTitle>
  </CardHeader>
  
  <CardContent class="space-y-6">
    <!-- 缓存统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- 缓存项数量 -->
      <div class="space-y-2">
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <HardDrive class="h-4 w-4" />
          缓存项数量
        </div>
        <div class="text-2xl font-bold">
          {formatNumber(stats.totalItems)}
        </div>
      </div>

      <!-- 缓存大小 -->
      <div class="space-y-2">
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <Database class="h-4 w-4" />
          缓存大小
        </div>
        <div class="text-2xl font-bold {getCacheSizeColor(stats.sizeInBytes)}">
          {formatBytes(stats.sizeInBytes)}
        </div>
      </div>

      <!-- 命中率 -->
      <div class="space-y-2">
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <TrendingUp class="h-4 w-4" />
          命中率
        </div>
        <div class="text-2xl font-bold {getHitRateColor(stats.hitRate)}">
          {(stats.hitRate * 100).toFixed(1)}%
        </div>
      </div>

      <!-- 总请求数 -->
      <div class="space-y-2">
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <RefreshCw class="h-4 w-4" />
          总请求数
        </div>
        <div class="text-2xl font-bold">
          {formatNumber(stats.hitCount + stats.missCount)}
        </div>
      </div>
    </div>

    <Separator />

    <!-- 详细统计 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
      <div class="space-y-1">
        <div class="text-muted-foreground">缓存命中</div>
        <div class="font-medium text-green-600 dark:text-green-400">
          {formatNumber(stats.hitCount)} 次
        </div>
      </div>

      <div class="space-y-1">
        <div class="text-muted-foreground">缓存未命中</div>
        <div class="font-medium text-red-600 dark:text-red-400">
          {formatNumber(stats.missCount)} 次
        </div>
      </div>

      <div class="space-y-1">
        <div class="text-muted-foreground">最后清理</div>
        <div class="font-medium">
          {formatLastCleanup(stats.lastCleanupAt)}
        </div>
      </div>
    </div>

    <Separator />

    <!-- 操作按钮 -->
    <div class="flex flex-col sm:flex-row gap-3">
      <Button
        variant="outline"
        onclick={handleRefreshStats}
        disabled={isRefreshing}
        class="flex items-center gap-2"
      >
        <RefreshCw class="h-4 w-4 {isRefreshing ? 'animate-spin' : ''}" />
        {isRefreshing ? '刷新中...' : '刷新统计'}
      </Button>

      <Button
        variant="destructive"
        onclick={clearAllCache}
        disabled={isClearing}
        class="flex items-center gap-2"
      >
        <Trash2 class="h-4 w-4" />
        {isClearing ? '清除中...' : '清除所有缓存'}
      </Button>
    </div>

    <!-- 缓存说明 -->
    <div class="rounded-lg bg-muted/50 p-4 text-sm">
      <h4 class="font-medium mb-2">缓存机制说明</h4>
      <ul class="space-y-1 text-muted-foreground">
        <li>• 货币信息会自动缓存到本地存储，减少 API 调用</li>
        <li>• 系统会自动检测图片资源有效性，失效时自动更新</li>
        <li>• 缓存会定期清理过期数据，保持最佳性能</li>
        <li>• 清除缓存后，下次访问时会重新获取最新数据</li>
      </ul>
    </div>
  </CardContent>
</Card>
