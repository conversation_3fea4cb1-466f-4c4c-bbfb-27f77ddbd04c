[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / TimeFilters

# Interface: TimeFilters

Defined in: src/lib/types/liquidation.ts:53

## Properties

### snapshotTimeRange

> **snapshotTimeRange**: [`SnapshotTimeRange`](../type-aliases/SnapshotTimeRange.md)

Defined in: src/lib/types/liquidation.ts:54

---

### trendDuration

> **trendDuration**: [`TrendDuration`](../type-aliases/TrendDuration.md)

Defined in: src/lib/types/liquidation.ts:57

---

### trendTimeFrame

> **trendTimeFrame**: [`TrendTimeFrame`](../type-aliases/TrendTimeFrame.md)

Defined in: src/lib/types/liquidation.ts:56

---

### trendTimeRange

> **trendTimeRange**: [`TrendTimeRange`](../type-aliases/TrendTimeRange.md)

Defined in: src/lib/types/liquidation.ts:55

---

### trendViewMode

> **trendViewMode**: [`TrendViewMode`](../type-aliases/TrendViewMode.md)

Defined in: src/lib/types/liquidation.ts:58
