import { derived,writable } from 'svelte/store';

import { browser } from '$app/environment';

/**
 * 时区配置接口
 */
export interface TimezoneConfig {
  /** 时区标识符 (IANA 时区名称) */
  id: string;
  /** 显示名称 */
  displayName: string;
  /** UTC 偏移量显示 */
  offset: string;
  /** 描述信息 */
  description: string;
  /** 是否为常用时区 */
  isCommon?: boolean;
}

/**
 * 时区设置状态接口
 */
export interface TimezoneState {
  /** 当前选中的时区 */
  selectedTimezone: string;
  /** 是否使用浏览器本地时区 */
  useLocalTimezone: boolean;
  /** 时区设置是否已初始化 */
  isInitialized: boolean;
}

/**
 * 默认时区配置
 */
const DEFAULT_TIMEZONE = 'UTC';

/**
 * 本地存储键名
 */
const STORAGE_KEY = 'svelte-demo-timezone-settings';

/**
 * 获取浏览器本地时区
 */
function getBrowserTimezone(): string {
  if (!browser) return DEFAULT_TIMEZONE;

  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch {
    return DEFAULT_TIMEZONE;
  }
}

/**
 * 从本地存储加载时区设置
 */
function loadTimezoneSettings(): TimezoneState {
  if (!browser) {
    return {
      selectedTimezone: DEFAULT_TIMEZONE,
      useLocalTimezone: true,
      isInitialized: false,
    };
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored) as Partial<TimezoneState>;
      return {
        selectedTimezone: parsed.selectedTimezone || getBrowserTimezone(),
        useLocalTimezone: parsed.useLocalTimezone ?? true,
        isInitialized: true,
      };
    }
  } catch (error) {
    console.warn('Failed to load timezone settings from localStorage:', error);
  }

  // 默认使用浏览器时区
  return {
    selectedTimezone: getBrowserTimezone(),
    useLocalTimezone: true,
    isInitialized: true,
  };
}

/**
 * 保存时区设置到本地存储
 */
function saveTimezoneSettings(state: TimezoneState): void {
  if (!browser) return;

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to save timezone settings to localStorage:', error);
  }
}

/**
 * 创建时区设置状态管理
 */
function createTimezoneStore() {
  const initialState = loadTimezoneSettings();
  const { subscribe, update, set } = writable<TimezoneState>(initialState);

  return {
    subscribe,

    /**
     * 设置时区
     * @param timezone 时区标识符
     */
    setTimezone: (timezone: string) => {
      update((state) => {
        const browserTimezone = getBrowserTimezone();
        const newState = {
          ...state,
          selectedTimezone: timezone,
          useLocalTimezone: timezone === browserTimezone,
          isInitialized: true,
        };
        saveTimezoneSettings(newState);
        return newState;
      });
    },

    /**
     * 切换到浏览器本地时区
     */
    useLocalTimezone: () => {
      update((state) => {
        const browserTimezone = getBrowserTimezone();
        const newState = {
          ...state,
          selectedTimezone: browserTimezone,
          useLocalTimezone: true,
          isInitialized: true,
        };
        saveTimezoneSettings(newState);
        return newState;
      });
    },

    /**
     * 重置时区设置
     */
    reset: () => {
      const defaultState = {
        selectedTimezone: getBrowserTimezone(),
        useLocalTimezone: true,
        isInitialized: true,
      };
      set(defaultState);
      saveTimezoneSettings(defaultState);
    },

    /**
     * 初始化时区设置（用于 SSR 环境）
     */
    initialize: () => {
      if (browser) {
        const settings = loadTimezoneSettings();
        set(settings);
      }
    },
  };
}

/**
 * 时区设置状态管理实例
 */
export const timezoneStore = createTimezoneStore();

/**
 * 当前选中的时区（派生 store）
 */
export const currentTimezone = derived(timezoneStore, ($timezone) => $timezone.selectedTimezone);

/**
 * 是否使用本地时区（派生 store）
 */
export const isUsingLocalTimezone = derived(
  timezoneStore,
  ($timezone) => $timezone.useLocalTimezone
);

/**
 * 时区设置是否已初始化（派生 store）
 */
export const isTimezoneInitialized = derived(timezoneStore, ($timezone) => $timezone.isInitialized);

/**
 * 获取当前时区的显示信息
 */
export function getCurrentTimezoneInfo(timezone: string): {
  offset: string;
  displayName: string;
} {
  if (!browser) {
    return {
      offset: 'UTC+00:00',
      displayName: 'UTC',
    };
  }

  // 硬编码的常用时区映射，避免循环依赖
  const timezoneMap: Record<string, { offset: string; displayName: string }> = {
    UTC: { offset: 'UTC+00:00', displayName: '协调世界时' },
    'Asia/Shanghai': { offset: 'UTC+08:00', displayName: '北京时间' },
    'America/New_York': { offset: 'UTC-05:00/-04:00', displayName: '纽约时间' },
    'Europe/London': { offset: 'UTC+00:00/+01:00', displayName: '伦敦时间' },
    'Asia/Tokyo': { offset: 'UTC+09:00', displayName: '东京时间' },
    'America/Los_Angeles': { offset: 'UTC-08:00/-07:00', displayName: '洛杉矶时间' },
    'Europe/Paris': { offset: 'UTC+01:00/+02:00', displayName: '巴黎时间' },
    'Asia/Hong_Kong': { offset: 'UTC+08:00', displayName: '香港时间' },
    'Asia/Singapore': { offset: 'UTC+08:00', displayName: '新加坡时间' },
    browser: { offset: '自动检测', displayName: '浏览器本地时区' },
  };

  // 首先尝试从映射中查找
  if (timezoneMap[timezone]) {
    return timezoneMap[timezone];
  }

  // 降级到简单的时区处理
  try {
    // 简单的偏移量计算
    let offset = 'UTC+00:00';
    let displayName = timezone;

    // 对于常见时区，使用简单的映射
    if (timezone === 'UTC') {
      offset = 'UTC+00:00';
      displayName = '协调世界时';
    } else if (timezone === 'Asia/Shanghai') {
      offset = 'UTC+08:00';
      displayName = '北京时间';
    } else {
      // 尝试使用 Intl API 获取偏移量
      try {
        const now = new Date();
        const utcDate = new Date(now.toISOString());
        const localDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
        const offsetMs = localDate.getTime() - utcDate.getTime();
        const offsetMinutes = Math.round(offsetMs / (1000 * 60));

        const hours = Math.floor(Math.abs(offsetMinutes) / 60);
        const minutes = Math.abs(offsetMinutes) % 60;
        const sign = offsetMinutes >= 0 ? '+' : '-';
        offset = `UTC${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

        // 使用时区ID作为显示名称
        displayName = timezone;
      } catch {
        offset = 'UTC+00:00';
        displayName = timezone;
      }
    }

    return {
      offset,
      displayName,
    };
  } catch {
    return {
      offset: 'UTC+00:00',
      displayName: timezone,
    };
  }
}

/**
 * 获取时区偏移分钟数
 */
function getTimezoneOffsetMinutes(timezone: string, date: Date = new Date()): number {
  try {
    // 创建两个相同时间的Date对象，一个用UTC，一个用目标时区
    const utcDate = new Date(date.toISOString());

    // 使用Intl.DateTimeFormat获取目标时区的时间
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

    const parts = formatter.formatToParts(date);
    const partsObj = parts.reduce(
      (acc, part) => {
        acc[part.type] = part.value;
        return acc;
      },
      {} as Record<string, string>
    );

    const targetDate = new Date(
      `${partsObj.year}-${partsObj.month}-${partsObj.day}T${partsObj.hour}:${partsObj.minute}:${partsObj.second}`
    );

    // 计算差值（分钟）
    const diffMs = targetDate.getTime() - utcDate.getTime();
    return Math.round(diffMs / (1000 * 60));
  } catch {
    return 0; // 默认返回UTC偏移
  }
}

/**
 * 格式化时间到指定时区
 * @param date 日期对象或日期字符串
 * @param timezone 目标时区
 * @param options 格式化选项
 */
export function formatTimeInTimezone(
  date: Date | string,
  timezone: string,
  options: Intl.DateTimeFormatOptions = {}
): string {
  if (!browser) return '';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: timezone,
      hour12: false,
      ...options,
    };

    return new Intl.DateTimeFormat('zh-CN', defaultOptions).format(dateObj);
  } catch (error) {
    console.warn('Failed to format time in timezone:', error);
    return '';
  }
}
