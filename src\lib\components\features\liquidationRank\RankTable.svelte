<script lang="ts">
  import ChevronDownIcon from '@lucide/svelte/icons/chevron-down';
  // 图标导入
  import ChevronUpIcon from '@lucide/svelte/icons/chevron-up';

  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
  } from '$lib/components/ui/table';
  import type { LiquidationRankItem } from '$lib/types';

  // 本地类型定义
  type RankSortField =
    | 'amount'
    | 'count'
    | 'changePercent'
    | 'totalAmount'
    | 'totalCount'
    | 'longAmount'
    | 'shortAmount'
    | 'longCount'
    | 'shortCount'
    | 'longShortRatio'
    | 'avgAmount'
    | 'maxAmount'
    | 'minAmount';
  type RankType = 'amount' | 'count';
  type SortDirection = 'asc' | 'desc';

  interface Props {
    /** 排行榜类型 */
    rankType: RankType;
    /** 排行榜数据 */
    items: LiquidationRankItem[];
    /** 当前排序字段 */
    sortField: RankSortField;
    /** 当前排序方向 */
    sortDirection: SortDirection;
    /** 是否正在加载 */
    loading?: boolean;
    /** 排序变更回调 */
    onSort?: (field: RankSortField, direction: SortDirection) => void;
  }

  const { rankType, items = [], sortField, sortDirection, loading = false, onSort }: Props = $props();

  // 处理排序点击
  function handleSort(field: RankSortField) {
    let newDirection: SortDirection = 'desc';

    // 如果点击的是当前排序字段，则切换方向
    if (field === sortField) {
      newDirection = sortDirection === 'desc' ? 'asc' : 'desc';
    }

    // 调用回调函数
    onSort?.(field, newDirection);
  }

  // 格式化金额显示
  function formatAmount(amount: number): string {
    if (amount >= 1000000000) {
      return `$${(amount / 1000000000).toFixed(2)}B`;
    } else if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(2)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(2)}K`;
    }
    return `$${amount.toFixed(2)}`;
  }

  // 获取排序图标
  function getSortIcon(field: RankSortField) {
    if (field !== sortField) return null;
    return sortDirection === 'desc' ? ChevronDownIcon : ChevronUpIcon;
  }
</script>

<div class="w-full">
  <div class="rounded-md border">
    <Table>
      <TableHeader>
        <TableRow>
          <!-- 排名 -->
          <TableHead class="w-[60px] text-center">排名</TableHead>

          <!-- 币种 -->
          <TableHead class="min-w-[120px]">币种</TableHead>

          {#if rankType === 'amount'}
            <!-- 金额排行榜列 -->
            <!-- 总清算金额 - 可排序 -->
            <TableHead class="text-right">
              <Button
                variant="ghost"
                size="sm"
                class="h-auto p-0 font-semibold hover:bg-transparent"
                onclick={() => handleSort('totalAmount')}
              >
                总清算金额
                {#if getSortIcon('totalAmount')}
                  {@const SortIcon = getSortIcon('totalAmount')}
                  <SortIcon class="ml-1 h-4 w-4" />
                {/if}
              </Button>
            </TableHead>

            <!-- 多头清算金额 - 在大屏幕显示 -->
            <TableHead class="hidden text-right lg:table-cell">
              <Button
                variant="ghost"
                size="sm"
                class="h-auto p-0 font-semibold hover:bg-transparent"
                onclick={() => handleSort('longAmount')}
              >
                多头金额
                {#if getSortIcon('longAmount')}
                  {@const SortIcon = getSortIcon('longAmount')}
                  <SortIcon class="ml-1 h-4 w-4" />
                {/if}
              </Button>
            </TableHead>

            <!-- 空头清算金额 - 在大屏幕显示 -->
            <TableHead class="hidden text-right lg:table-cell">
              <Button
                variant="ghost"
                size="sm"
                class="h-auto p-0 font-semibold hover:bg-transparent"
                onclick={() => handleSort('shortAmount')}
              >
                空头金额
                {#if getSortIcon('shortAmount')}
                  {@const SortIcon = getSortIcon('shortAmount')}
                  <SortIcon class="ml-1 h-4 w-4" />
                {/if}
              </Button>
            </TableHead>
          {:else}
            <!-- 笔数排行榜列 -->
            <!-- 总清算笔数 - 可排序 -->
            <TableHead class="text-right">
              <Button
                variant="ghost"
                size="sm"
                class="h-auto p-0 font-semibold hover:bg-transparent"
                onclick={() => handleSort('totalCount')}
              >
                总清算笔数
                {#if getSortIcon('totalCount')}
                  {@const SortIcon = getSortIcon('totalCount')}
                  <SortIcon class="ml-1 h-4 w-4" />
                {/if}
              </Button>
            </TableHead>

            <!-- 多头清算笔数 - 在大屏幕显示 -->
            <TableHead class="hidden text-right lg:table-cell">
              <Button
                variant="ghost"
                size="sm"
                class="h-auto p-0 font-semibold hover:bg-transparent"
                onclick={() => handleSort('longCount')}
              >
                多头笔数
                {#if getSortIcon('longCount')}
                  {@const SortIcon = getSortIcon('longCount')}
                  <SortIcon class="ml-1 h-4 w-4" />
                {/if}
              </Button>
            </TableHead>

            <!-- 空头清算笔数 - 在大屏幕显示 -->
            <TableHead class="hidden text-right lg:table-cell">
              <Button
                variant="ghost"
                size="sm"
                class="h-auto p-0 font-semibold hover:bg-transparent"
                onclick={() => handleSort('shortCount')}
              >
                空头笔数
                {#if getSortIcon('shortCount')}
                  {@const SortIcon = getSortIcon('shortCount')}
                  <SortIcon class="ml-1 h-4 w-4" />
                {/if}
              </Button>
            </TableHead>
          {/if}
        </TableRow>
      </TableHeader>
      <TableBody>
        {#if loading}
          <!-- 加载状态 -->
          {#each Array(10) as _, i}
            <TableRow>
              <TableCell class="text-center">
                <div class="bg-muted mx-auto h-4 w-8 animate-pulse rounded"></div>
              </TableCell>
              <TableCell>
                <div class="flex items-center gap-2">
                  <div class="bg-muted h-4 w-12 animate-pulse rounded"></div>
                  <div class="bg-muted h-3 w-16 animate-pulse rounded"></div>
                </div>
              </TableCell>
              <TableCell class="text-right">
                <div class="bg-muted ml-auto h-4 w-20 animate-pulse rounded"></div>
              </TableCell>
              <TableCell class="hidden text-right lg:table-cell">
                <div class="bg-muted ml-auto h-4 w-16 animate-pulse rounded"></div>
              </TableCell>
              <TableCell class="hidden text-right xl:table-cell">
                <div class="bg-muted ml-auto h-4 w-12 animate-pulse rounded"></div>
              </TableCell>
              <TableCell class="hidden text-right lg:table-cell">
                <div class="bg-muted ml-auto h-4 w-16 animate-pulse rounded"></div>
              </TableCell>
              <TableCell class="text-center">
                <div class="bg-muted mx-auto h-4 w-4 animate-pulse rounded"></div>
              </TableCell>
            </TableRow>
          {/each}
        {:else if items.length > 0}
          <!-- 数据行 -->
          {#each items as item (item.symbol)}
            <TableRow>
              <!-- 排名 -->
              <TableCell class="text-center font-medium">
                {item.rank}
              </TableCell>

              <!-- 币种信息 -->
              <TableCell>
                <div class="flex items-center gap-2">
                  <span class="font-semibold">{item.symbol}</span>
                  <Badge
                    variant={item.coinType === 'Major' ? 'default' : 'secondary'}
                    class="text-xs"
                  >
                    {item.coinType === 'Major' ? '主流' : '山寨'}
                  </Badge>
                </div>
              </TableCell>

              {#if rankType === 'amount'}
                <!-- 金额排行榜数据 -->
                <!-- 总清算金额 -->
                <TableCell class="text-right font-medium">
                  {formatAmount(item.totalAmount)}
                </TableCell>

                <!-- 多头清算金额 -->
                <TableCell class="hidden text-right text-green-600 lg:table-cell">
                  {formatAmount(item.longAmount)}
                </TableCell>

                <!-- 空头清算金额 -->
                <TableCell class="hidden text-right text-red-600 lg:table-cell">
                  {formatAmount(item.shortAmount)}
                </TableCell>
              {:else}
                <!-- 笔数排行榜数据 -->
                <!-- 总清算笔数 -->
                <TableCell class="text-right font-medium">
                  {item.totalCount.toLocaleString()}
                </TableCell>

                <!-- 多头清算笔数 -->
                <TableCell class="hidden text-right text-green-600 lg:table-cell">
                  {item.longCount.toLocaleString()}
                </TableCell>

                <!-- 空头清算笔数 -->
                <TableCell class="hidden text-right text-red-600 lg:table-cell">
                  {item.shortCount.toLocaleString()}
                </TableCell>
              {/if}
            </TableRow>
          {/each}
        {:else}
          <!-- 空状态 -->
          <TableRow>
            <TableCell colspan={7} class="h-24 text-center">
              <div class="flex flex-col items-center gap-2">
                <span class="text-muted-foreground">暂无排行数据</span>
                <span class="text-muted-foreground text-sm">请尝试调整筛选条件</span>
              </div>
            </TableCell>
          </TableRow>
        {/if}
      </TableBody>
    </Table>
  </div>
</div>
