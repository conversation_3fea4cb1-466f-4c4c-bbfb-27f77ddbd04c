<!-- WeekView.svelte -->
<script lang="ts">
  import { onMount } from 'svelte';

  // 类型定义
  interface Event {
    id: string;
    title: string;
    startTime: string; // 格式: "HH:MM"
    endTime: string;   // 格式: "HH:MM"
    date: string;      // 格式: "YYYY-MM-DD"
    color?: string;
  }

  interface WeekViewProps {
    events?: Event[];
    startHour?: number;
    endHour?: number;
    hourHeight?: number;
    currentWeek?: Date;
  }

  // Props
  export let events: Event[] = [];
  export let startHour: number = 0;
  export let endHour: number = 24;
  export let hourHeight: number = 60;
  export let currentWeek: Date = new Date();

  // 响应式变量
  let scrollContainer: HTMLDivElement;
  let timeColumn: HTMLDivElement;

  // 生成时间标签
  const timeLabels = Array.from({ length: endHour - startHour }, (_, i) => {
    const hour = startHour + i;
    return `${hour.toString().padStart(2, '0')}:00`;
  });

  // 获取当前周的日期
  function getWeekDates(date: Date): Date[] {
    const week = [];
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // 周一开始
    startOfWeek.setDate(diff);

    for (let i = 0; i < 7; i++) {
      const weekDay = new Date(startOfWeek);
      weekDay.setDate(startOfWeek.getDate() + i);
      week.push(weekDay);
    }
    return week;
  }

  $: weekDates = getWeekDates(currentWeek);
  $: weekDaysNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

  // 格式化日期
  function formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  function formatDisplayDate(date: Date): string {
    return `${date.getMonth() + 1}/${date.getDate()}`;
  }

  // 获取指定日期的事件
  function getEventsForDate(date: string): Event[] {
    return events.filter(event => event.date === date);
  }

  // 计算事件位置
  function calculateEventPosition(event: Event) {
    const [startHourNum, startMinute] = event.startTime.split(':').map(Number);
    const [endHourNum, endMinute] = event.endTime.split(':').map(Number);
    
    const startMinutes = (startHourNum - startHour) * 60 + startMinute;
    const endMinutes = (endHourNum - startHour) * 60 + endMinute;
    
    const top = (startMinutes / 60) * hourHeight;
    const height = ((endMinutes - startMinutes) / 60) * hourHeight;
    
    return { top, height };
  }

  // 处理重叠事件的布局
  function layoutEvents(dayEvents: Event[]) {
    // 按开始时间排序
    const sortedEvents = [...dayEvents].sort((a, b) => {
      const aStart = a.startTime.localeCompare(b.startTime);
      return aStart !== 0 ? aStart : a.endTime.localeCompare(b.endTime);
    });

    // 计算每个事件的列位置和宽度
    const eventLayouts = sortedEvents.map((event, index) => {
      const position = calculateEventPosition(event);
      
      // 简单的重叠检测：检查与前面事件的重叠
      let column = 0;
      let maxColumns = 1;
      
      for (let i = 0; i < index; i++) {
        const otherEvent = sortedEvents[i];
        const otherPosition = calculateEventPosition(otherEvent);
        
        // 检查时间重叠
        if (position.top < otherPosition.top + otherPosition.height &&
            position.top + position.height > otherPosition.top) {
          column = Math.max(column, (eventLayouts[i]?.column || 0) + 1);
          maxColumns = Math.max(maxColumns, column + 1);
        }
      }
      
      return {
        event,
        ...position,
        column,
        maxColumns
      };
    });

    // 更新所有事件的最大列数
    return eventLayouts.map(layout => ({
      ...layout,
      maxColumns: Math.max(...eventLayouts.map(l => l.maxColumns))
    }));
  }

  // 同步滚动处理
  function handleScroll() {
    if (scrollContainer && timeColumn) {
      timeColumn.scrollTop = scrollContainer.scrollTop;
    }
  }

  // 导航函数
  function goToPreviousWeek() {
    const newDate = new Date(currentWeek);
    newDate.setDate(newDate.getDate() - 7);
    currentWeek = newDate;
  }

  function goToNextWeek() {
    const newDate = new Date(currentWeek);
    newDate.setDate(newDate.getDate() + 7);
    currentWeek = newDate;
  }

  function goToToday() {
    currentWeek = new Date();
  }

  onMount(() => {
    // 滚动到早上8点
    if (scrollContainer) {
      scrollContainer.scrollTop = (8 - startHour) * hourHeight;
    }
  });
</script>

<div class="week-view w-full flex flex-col bg-white overflow-x-auto">
  <!-- 头部导航栏 -->
  <div class="flex items-center justify-between p-4 bg-gray-50 border-b">
    <div class="flex items-center space-x-2">
      <button 
        on:click={goToPreviousWeek}
        class="p-2 rounded-lg hover:bg-gray-200 transition-colors"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button 
        on:click={goToNextWeek}
        class="p-2 rounded-lg hover:bg-gray-200 transition-colors"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
      <button 
        on:click={goToToday}
        class="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
      >
        今天
      </button>
    </div>
    <h2 class="text-lg font-semibold">
      {weekDates[0].getFullYear()}年{weekDates[0].getMonth() + 1}月
    </h2>
  </div>

  <!-- 日历主体 -->
  <div class="flex-1 flex overflow-auto">
    <!-- 冻结的时间列 -->
    <div class="flex flex-col bg-gray-50 border-r">
      <!-- 时间列头部占位 -->
      <div class="h-16 bg-gray-100 border-b flex items-center justify-center text-sm font-medium">
        时间
      </div>
      
      <!-- 时间标签列 -->
      <div 
        bind:this={timeColumn}
        class="flex-1 overflow-y-hidden"
        style="height: {(endHour - startHour) * hourHeight}px;"
      >
        {#each timeLabels as time, i}
          <div 
            class="border-b border-gray-200 flex items-start justify-end pr-2 text-xs text-gray-600"
            style="height: {hourHeight}px; line-height: {hourHeight}px;"
          >
            {time}
          </div>
        {/each}
      </div>
    </div>

    <!-- 可滚动的日历区域 -->
    <div class="flex-1 flex flex-col overflow-x-auto">
      <!-- 日期头部 -->
      <div class="h-16 bg-gray-100 border-b flex overflow-x-auto">
        {#each weekDates as date, dayIndex}
          <div class="min-w-32 flex-shrink-0 flex flex-col items-center justify-center border-r border-gray-200 p-2">
            <div class="text-xs text-gray-600 mb-1">{weekDaysNames[dayIndex]}</div>
            <div class="text-sm font-medium {formatDate(date) === formatDate(new Date()) ? 'text-blue-600 bg-blue-100 rounded-full w-6 h-6 flex items-center justify-center' : ''}">
              {formatDisplayDate(date)}
            </div>
          </div>
        {/each}
      </div>

      <!-- 时间网格和事件区域 -->
      <div 
        bind:this={scrollContainer}
        class="flex-1 overflow-auto"
        on:scroll={handleScroll}
      >
        <div class="flex" style="height: {(endHour - startHour) * hourHeight}px;">
          {#each weekDates as date, dayIndex}
            <div class="min-w-32 flex-shrink-0 relative border-r border-gray-200">
              <!-- 时间网格背景 -->
              {#each timeLabels as _, hourIndex}
                <div 
                  class="border-b border-gray-100 absolute w-full"
                  style="top: {hourIndex * hourHeight}px; height: {hourHeight}px;"
                ></div>
              {/each}

              <!-- 当前时间指示线 -->
              {#if formatDate(date) === formatDate(new Date())}
                {@const now = new Date()}
                {@const currentMinutes = (now.getHours() - startHour) * 60 + now.getMinutes()}
                {#if currentMinutes >= 0 && currentMinutes <= (endHour - startHour) * 60}
                  <div 
                    class="absolute w-full h-0.5 bg-red-500 z-20"
                    style="top: {(currentMinutes / 60) * hourHeight}px;"
                  >
                    <div class="w-2 h-2 bg-red-500 rounded-full -mt-1 -ml-1"></div>
                  </div>
                {/if}
              {/if}

              <!-- 事件 -->
              {#each layoutEvents(getEventsForDate(formatDate(date))) as eventLayout}
                <div 
                  class="absolute rounded-md p-1 text-xs text-white shadow-sm cursor-pointer hover:shadow-md transition-shadow z-10 overflow-hidden"
                  style="
                    top: {eventLayout.top}px; 
                    height: {Math.max(eventLayout.height, 20)}px;
                    left: {(eventLayout.column / eventLayout.maxColumns) * 100}%;
                    width: {(1 / eventLayout.maxColumns) * 100}%;
                    background-color: {eventLayout.event.color || '#3b82f6'};
                    margin: 1px;
                  "
                  title="{eventLayout.event.title} ({eventLayout.event.startTime}-{eventLayout.event.endTime})"
                >
                  <div class="font-medium text-xs leading-tight">
                    {eventLayout.event.title}
                  </div>
                  <div class="text-xs opacity-90">
                    {eventLayout.event.startTime}-{eventLayout.event.endTime}
                  </div>
                </div>
              {/each}
            </div>
          {/each}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .week-view {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* 自定义滚动条样式 */
  :global(.week-view *::-webkit-scrollbar) {
    width: 6px;
    height: 6px;
  }

  :global(.week-view *::-webkit-scrollbar-track) {
    background: #f1f1f1;
  }

  :global(.week-view *::-webkit-scrollbar-thumb) {
    background: #c1c1c1;
    border-radius: 3px;
  }

  :global(.week-view *::-webkit-scrollbar-thumb:hover) {
    background: #a8a8a8;
  }
</style>