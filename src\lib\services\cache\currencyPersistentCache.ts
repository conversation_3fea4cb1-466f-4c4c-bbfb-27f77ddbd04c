/**
 * 智能货币信息持久化缓存服务
 * 
 * 功能特性：
 * - 持久化存储到 localStorage
 * - 图片资源有效性检测
 * - 自动缓存更新机制
 * - 请求去重和性能优化
 */

import { browser } from '$app/environment';
import { STORAGE_KEYS } from '$lib/constants/app';
import { appConfig } from '$lib/config';
import type { CoinInfo } from '$lib/types';
import { logApiError } from '$lib/utils/logger';

/**
 * 缓存项接口
 */
export interface CurrencyCacheItem {
  /** 货币信息 */
  data: CoinInfo;
  /** 缓存创建时间戳 */
  timestamp: number;
  /** 图片最后验证时间戳 */
  imageValidatedAt: number;
  /** 图片是否有效 */
  imageValid: boolean;
  /** 缓存版本 */
  version: string;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** 总缓存项数 */
  totalItems: number;
  /** 缓存大小（字节） */
  sizeInBytes: number;
  /** 命中次数 */
  hitCount: number;
  /** 未命中次数 */
  missCount: number;
  /** 命中率 */
  hitRate: number;
  /** 最后清理时间 */
  lastCleanupAt: number;
}

/**
 * 图片验证结果
 */
interface ImageValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * 智能货币信息持久化缓存类
 */
export class CurrencyPersistentCache {
  private readonly CACHE_KEY = STORAGE_KEYS.API_CACHE + '_currency';
  private readonly STATS_KEY = STORAGE_KEYS.API_CACHE + '_currency_stats';
  private readonly CACHE_VERSION = '1.0.0';
  
  // 缓存配置
  private readonly MAX_AGE = appConfig.api.cacheTime; // 默认5分钟
  private readonly IMAGE_VALIDATION_INTERVAL = 24 * 60 * 60 * 1000; // 24小时
  private readonly MAX_CACHE_SIZE = 100; // 最大缓存项数
  private readonly MAX_STORAGE_SIZE = 5 * 1024 * 1024; // 5MB
  
  // 运行时状态
  private cache: Map<string, CurrencyCacheItem> = new Map();
  private stats: CacheStats;
  private pendingRequests: Map<string, Promise<CoinInfo | null>> = new Map();
  private imageValidationQueue: Set<string> = new Set();

  constructor() {
    this.stats = this.getDefaultStats();
    this.loadFromStorage();
    this.startPeriodicCleanup();
  }

  /**
   * 获取默认统计信息
   */
  private getDefaultStats(): CacheStats {
    return {
      totalItems: 0,
      sizeInBytes: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      lastCleanupAt: Date.now(),
    };
  }

  /**
   * 从 localStorage 加载缓存
   */
  private loadFromStorage(): void {
    if (!browser) return;

    try {
      // 加载缓存数据
      const cacheData = localStorage.getItem(this.CACHE_KEY);
      if (cacheData) {
        const parsed = JSON.parse(cacheData);
        Object.entries(parsed).forEach(([key, item]) => {
          const cacheItem = item as CurrencyCacheItem;
          // 验证缓存版本
          if (cacheItem.version === this.CACHE_VERSION) {
            this.cache.set(key, cacheItem);
          }
        });
      }

      // 加载统计信息
      const statsData = localStorage.getItem(this.STATS_KEY);
      if (statsData) {
        this.stats = { ...this.getDefaultStats(), ...JSON.parse(statsData) };
      }

      this.updateStats();
    } catch (error) {
      logApiError(error as Error, 'loadFromStorage', 'GET', undefined, {
        operation: 'cache_load',
        context: {},
      });
      this.clearCache();
    }
  }

  /**
   * 保存缓存到 localStorage
   */
  private saveToStorage(): void {
    if (!browser) return;

    try {
      // 检查存储大小限制
      const cacheObject = Object.fromEntries(this.cache);
      const serialized = JSON.stringify(cacheObject);
      
      if (serialized.length > this.MAX_STORAGE_SIZE) {
        this.performCleanup();
        return;
      }

      localStorage.setItem(this.CACHE_KEY, serialized);
      localStorage.setItem(this.STATS_KEY, JSON.stringify(this.stats));
    } catch (error) {
      logApiError(error as Error, 'saveToStorage', 'POST', undefined, {
        operation: 'cache_save',
        context: {
          cacheSize: this.cache.size,
        },
      });
    }
  }

  /**
   * 验证图片资源是否有效
   */
  private async validateImageUrl(imageUrl: string): Promise<ImageValidationResult> {
    if (!imageUrl || !browser) {
      return { isValid: false, error: 'Invalid URL or not in browser' };
    }

    try {
      // 使用 HEAD 请求检查图片资源
      const response = await fetch(imageUrl, {
        method: 'HEAD',
        mode: 'cors',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000), // 5秒超时
      });

      const isValid = response.ok && response.headers.get('content-type')?.startsWith('image/');
      
      return {
        isValid,
        error: isValid ? undefined : `HTTP ${response.status}: ${response.statusText}`,
      };
    } catch (error) {
      // 如果 HEAD 请求失败，尝试使用 Image 对象验证
      return this.validateImageWithImageObject(imageUrl);
    }
  }

  /**
   * 使用 Image 对象验证图片
   */
  private async validateImageWithImageObject(imageUrl: string): Promise<ImageValidationResult> {
    return new Promise((resolve) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        resolve({ isValid: false, error: 'Image load timeout' });
      }, 5000);

      img.onload = () => {
        clearTimeout(timeout);
        resolve({ isValid: true });
      };

      img.onerror = () => {
        clearTimeout(timeout);
        resolve({ isValid: false, error: 'Image load failed' });
      };

      img.src = imageUrl;
    });
  }

  /**
   * 检查缓存项是否需要验证图片
   */
  private shouldValidateImage(item: CurrencyCacheItem): boolean {
    const now = Date.now();
    return (
      !item.imageValid ||
      (now - item.imageValidatedAt) > this.IMAGE_VALIDATION_INTERVAL
    );
  }

  /**
   * 异步验证图片并更新缓存
   */
  private async validateAndUpdateImage(currencyId: string, item: CurrencyCacheItem): Promise<void> {
    if (this.imageValidationQueue.has(currencyId)) {
      return; // 已在验证队列中
    }

    this.imageValidationQueue.add(currencyId);

    try {
      const validation = await this.validateImageUrl(item.data.image);
      
      // 更新缓存项
      const updatedItem: CurrencyCacheItem = {
        ...item,
        imageValid: validation.isValid,
        imageValidatedAt: Date.now(),
      };

      this.cache.set(currencyId, updatedItem);
      this.saveToStorage();

      if (!validation.isValid) {
        logApiError(
          new Error(`Image validation failed: ${validation.error}`),
          'validateImage',
          'HEAD',
          undefined,
          {
            operation: 'image_validation',
            context: {
              currencyId,
              imageUrl: item.data.image,
            },
          }
        );
      }
    } catch (error) {
      logApiError(error as Error, 'validateAndUpdateImage', 'HEAD', undefined, {
        operation: 'image_validation',
        context: {
          currencyId,
        },
      });
    } finally {
      this.imageValidationQueue.delete(currencyId);
    }
  }

  /**
   * 获取缓存项（如果存在且有效）
   */
  public getCachedItem(currencyId: string): CurrencyCacheItem | null {
    const item = this.cache.get(currencyId);
    if (!item) {
      this.stats.missCount++;
      this.updateStats();
      return null;
    }

    const now = Date.now();
    const isExpired = (now - item.timestamp) > this.MAX_AGE;
    
    if (isExpired) {
      this.cache.delete(currencyId);
      this.saveToStorage();
      this.stats.missCount++;
      this.updateStats();
      return null;
    }

    // 检查是否需要验证图片
    if (this.shouldValidateImage(item)) {
      // 异步验证图片，不阻塞当前请求
      this.validateAndUpdateImage(currencyId, item);
      
      // 如果图片已知无效，返回 null 触发重新获取
      if (!item.imageValid) {
        this.stats.missCount++;
        this.updateStats();
        return null;
      }
    }

    this.stats.hitCount++;
    this.updateStats();
    return item;
  }

  /**
   * 设置缓存项
   */
  public setCachedItem(currencyId: string, data: CoinInfo): void {
    const now = Date.now();
    const item: CurrencyCacheItem = {
      data,
      timestamp: now,
      imageValidatedAt: now,
      imageValid: true, // 新数据假设图片有效
      version: this.CACHE_VERSION,
    };

    this.cache.set(currencyId, item);
    this.saveToStorage();
    this.updateStats();

    // 异步验证新图片
    if (data.image) {
      this.validateAndUpdateImage(currencyId, item);
    }
  }

  /**
   * 检查是否有正在进行的请求
   */
  public hasPendingRequest(currencyId: string): boolean {
    return this.pendingRequests.has(currencyId);
  }

  /**
   * 设置正在进行的请求
   */
  public setPendingRequest(currencyId: string, promise: Promise<CoinInfo | null>): void {
    this.pendingRequests.set(currencyId, promise);
    
    // 请求完成后清理
    promise.finally(() => {
      this.pendingRequests.delete(currencyId);
    });
  }

  /**
   * 获取正在进行的请求
   */
  public getPendingRequest(currencyId: string): Promise<CoinInfo | null> | undefined {
    return this.pendingRequests.get(currencyId);
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalItems = this.cache.size;
    this.stats.sizeInBytes = JSON.stringify(Object.fromEntries(this.cache)).length;
    this.stats.hitRate = this.stats.hitCount + this.stats.missCount > 0
      ? this.stats.hitCount / (this.stats.hitCount + this.stats.missCount)
      : 0;
  }

  /**
   * 获取缓存统计信息
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 执行缓存清理
   */
  private performCleanup(): void {
    const now = Date.now();
    const itemsToRemove: string[] = [];

    // 移除过期项
    for (const [key, item] of this.cache) {
      if ((now - item.timestamp) > this.MAX_AGE) {
        itemsToRemove.push(key);
      }
    }

    // 如果还是太多，移除最旧的项
    if (this.cache.size - itemsToRemove.length > this.MAX_CACHE_SIZE) {
      const sortedItems = Array.from(this.cache.entries())
        .filter(([key]) => !itemsToRemove.includes(key))
        .sort(([, a], [, b]) => a.timestamp - b.timestamp);

      const excessCount = this.cache.size - itemsToRemove.length - this.MAX_CACHE_SIZE;
      for (let i = 0; i < excessCount; i++) {
        itemsToRemove.push(sortedItems[i][0]);
      }
    }

    // 执行移除
    itemsToRemove.forEach(key => this.cache.delete(key));
    
    this.stats.lastCleanupAt = now;
    this.saveToStorage();
    this.updateStats();
  }

  /**
   * 启动定期清理
   */
  private startPeriodicCleanup(): void {
    if (!browser) return;

    // 每小时执行一次清理
    setInterval(() => {
      this.performCleanup();
    }, 60 * 60 * 1000);
  }

  /**
   * 清空所有缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.pendingRequests.clear();
    this.imageValidationQueue.clear();
    this.stats = this.getDefaultStats();
    
    if (browser) {
      localStorage.removeItem(this.CACHE_KEY);
      localStorage.removeItem(this.STATS_KEY);
    }
  }

  /**
   * 移除特定货币的缓存
   */
  public removeCurrency(currencyId: string): void {
    this.cache.delete(currencyId);
    this.pendingRequests.delete(currencyId);
    this.imageValidationQueue.delete(currencyId);
    this.saveToStorage();
    this.updateStats();
  }
}

/**
 * 导出单例实例
 */
export const currencyPersistentCache = new CurrencyPersistentCache();
