[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [stores](../README.md) / dataQueryStore

# Variable: dataQueryStore

> `const` **dataQueryStore**: `object`

Defined in: src/lib/stores/features/dataQuery.ts:238

## Type declaration

### error

> **error**: `Readable`\<`null` \| `string`\>

### goToPage()

> **goToPage**: (`page`) => `void`

#### Parameters

##### page

`number`

#### Returns

`void`

### isFilterExpanded

> **isFilterExpanded**: `Readable`\<`boolean`\>

### loading

> **loading**: `Readable`\<`boolean`\>

### pagination

> **pagination**: `Readable`\<[`Pagination`](../../types/interfaces/Pagination.md)\>

### queryParams

> **queryParams**: `Readable`\<[`QueryParams`](../../types/interfaces/QueryParams.md)\>

### reset()

> **reset**: () => `void`

#### Returns

`void`

### results

> **results**: `Readable`\<[`QueryResults`](../../types/interfaces/QueryResults.md)\>

### setDateRange()

> **setDateRange**: (`startDate`, `endDate`) => `void`

#### Parameters

##### startDate

`string`

##### endDate

`string`

#### Returns

`void`

### setItemsPerPage()

> **setItemsPerPage**: (`itemsPerPage`) => `void`

#### Parameters

##### itemsPerPage

`number`

#### Returns

`void`

### setKeyword()

> **setKeyword**: (`keyword`) => `void`

#### Parameters

##### keyword

`string`

#### Returns

`void`

### setStatus()

> **setStatus**: (`status`) => `void`

#### Parameters

##### status

`string`

#### Returns

`void`

### setType()

> **setType**: (`type`) => `void`

#### Parameters

##### type

`string`

#### Returns

`void`

### subscribe()

> **subscribe**: (`this`, `run`, `invalidate?`) => `Unsubscriber`

Subscribe on value changes.

#### Parameters

##### this

`void`

##### run

`Subscriber`\<\{ `error`: `null` \| `string`; `isFilterExpanded`: `boolean`; `queryParams`: [`QueryParams`](../../types/interfaces/QueryParams.md); `results`: [`QueryResults`](../../types/interfaces/QueryResults.md); \}\>

subscription callback

##### invalidate?

() => `void`

cleanup callback

#### Returns

`Unsubscriber`

### toggleFilterExpanded()

> **toggleFilterExpanded**: () => `void`

#### Returns

`void`

### updateQueryParams()

> **updateQueryParams**: (`params`) => `void`

#### Parameters

##### params

`Partial`\<[`QueryParams`](../../types/interfaces/QueryParams.md)\>

#### Returns

`void`

### executeQuery()

> **executeQuery**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>

### exportData()

> **exportData**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>
