/**
 * 类型定义统一导出
 *
 * 重构后的类型组织结构：
 * - core: 核心共享类型（API、分页、通用类型）
 * - business: 业务领域类型（清算、市场、查询）
 * - ui: UI 相关类型（图表、表单、布局）
 *
 * @category Types
 */

// 核心类型
export * from './core';

// 业务类型
export * from './business';

// UI 类型
export * from './ui';

// 向后兼容：重新导出常用类型
export type {
  // API 相关
  ApiResponse,
  ApiErrorInfo,
  PaginationParams,
  PaginatedResponse,

  // 通用类型
  TimeRange,
  DateRange,
  CoinOption,
  DataTypeOption,
  AmountRange,
} from './core';

export type {
  // 清算相关
  LiquidationData,
  LiquidationStats,
  LiquidationRankItem,
  LiquidationRankResponse,

  // 市场相关
  MarketEvent,
  MarketEventType,
  CoinInfo,
  HistoricalDataItem,

  // 查询相关
  BaseQueryParams,
  QueryResults,
  CompoundQueryParams,
} from './business';

export type {
  // 图表相关
  ChartData,
  ChartConfig,
  BarChartItem,
  LineChartItem,
  PieChartItem,

  // 布局相关
  DashboardStats,
  NavigationItem,
  NotificationItem,
} from './ui';
