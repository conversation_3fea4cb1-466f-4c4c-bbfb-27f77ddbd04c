/**
 * 清算业务相关类型定义
 * 
 * @category Business Types
 */

import type { Component } from 'svelte';
import type { ApiResponse } from '../core';

/**
 * 清算数据接口
 */
export interface LiquidationData {
  /** 唯一标识符 */
  id: string;
  /** 日期时间 */
  datetime: string;
  /** 清算金额（USD） */
  amountUsd: number;
  /** 方向：1=多头, 0=空头 */
  side: number;
  /** 交易对符号 */
  symbol: string;
  /** 订单规模阈值 */
  threshold: string;
  /** 币种类型 */
  coinType: 'Major' | 'Altcoin';
}

/**
 * 清算统计数据
 */
export interface LiquidationStats {
  /** 总清算金额 */
  totalAmount: number;
  /** 总清算笔数 */
  totalCount: number;
  /** 多头清算金额 */
  longAmount: number;
  /** 空头清算金额 */
  shortAmount: number;
  /** 多空比例 */
  longShortRatio: number;
}

/**
 * 清算排行榜数据项
 */
export interface LiquidationRankItem {
  /** 排名 */
  rank: number;
  /** 币种符号 */
  symbol: string;
  /** 币种名称 */
  name: string;
  /** 清算金额 */
  amount: number;
  /** 清算笔数 */
  count: number;
  /** 变化百分比 */
  changePercent: number;
  /** 图标URL */
  icon?: string;
  /** 币种类型 */
  coinType: 'Major' | 'Altcoin';
  /** 总清算金额 */
  totalAmount: number;
  /** 总清算笔数 */
  totalCount: number;
  /** 多头清算金额 */
  longAmount: number;
  /** 空头清算金额 */
  shortAmount: number;
  /** 多头清算笔数 */
  longCount: number;
  /** 空头清算笔数 */
  shortCount: number;
  /** 多空比例 */
  longShortRatio: number;
  /** 平均清算金额 */
  avgAmount: number;
  /** 最大清算金额 */
  maxAmount: number;
  /** 最小清算金额 */
  minAmount: number;
}

/**
 * 清算排行榜响应
 */
export interface LiquidationRankResponse {
  /** 排行榜数据 */
  items: LiquidationRankItem[];
  /** 更新时间 */
  updateTime: string;
  /** 统计时间范围 */
  timeRange: string;
}

/**
 * 清算排行榜 API 响应
 */
export type LiquidationRankApiResponse = ApiResponse<LiquidationRankResponse>;

/**
 * 快照时间范围选项
 */
export type SnapshotTimeRange = '1h' | '4h' | '12h' | '24h';

/**
 * 趋势时间范围选项
 */
export type TrendTimeRange = '7d' | '30d' | '90d';

/**
 * 趋势时间粒度选项（X轴每格对应的时间间隔）
 */
export type TrendTimeFrame = '1h' | '4h' | '1d' | '1w';

/**
 * 趋势总时间范围选项（整个X轴覆盖的时间范围）
 */
export type TrendDuration = '1d' | '7d' | '30d' | '90d';

/**
 * 趋势视图模式选项
 */
export type TrendViewMode = 'overall' | 'comparison';

/**
 * 清算图表配置类型
 */
export interface LiquidationChartConfig {
  /** 图表ID */
  id: string;
  /** 图表组件 */
  component: Component;
  /** 是否可见 */
  visible: boolean;
  /** 显示顺序 */
  order: number;
  /** 图表标题 */
  title: string;
  /** 图表副标题 */
  subTitle: string;
  /** 系列类型 */
  series?: 'snapshot' | 'trend';
  /** 额外配置选项 */
  options?: Record<string, unknown>;
  /** 分组维度 */
  groupBy?: 'side' | 'coinType' | null;
  /** 适用的视图模式 */
  viewMode?: TrendViewMode;
}

/**
 * 清算查询参数
 */
export interface LiquidationQueryParams {
  /** 时间范围 */
  timeRange: SnapshotTimeRange | TrendTimeRange;
  /** 币种类型过滤 */
  coinTypeFilter?: 'all' | 'major' | 'altcoin';
  /** 方向过滤 */
  sideFilter?: 'all' | 'long' | 'short';
  /** 最小金额过滤 */
  minAmount?: number;
  /** 最大金额过滤 */
  maxAmount?: number;
}

/**
 * 时间过滤器配置
 */
export interface TimeFilters {
  /** 快照时间范围 */
  snapshot: SnapshotTimeRange;
  /** 快照时间范围（向后兼容） */
  snapshotTimeRange: SnapshotTimeRange;
  /** 趋势时间范围 */
  trend: TrendTimeRange;
  /** 趋势时间范围（向后兼容） */
  trendTimeRange: TrendTimeRange;
  /** 趋势时间粒度 */
  trendTimeFrame: TrendTimeFrame;
  /** 趋势总时间范围 */
  trendDuration: TrendDuration;
  /** 趋势视图模式 */
  trendViewMode: TrendViewMode;
}

/**
 * 清算排行榜排序字段类型
 */
export type RankSortField =
  | 'amount'
  | 'count'
  | 'changePercent'
  | 'totalAmount'
  | 'totalCount'
  | 'longAmount'
  | 'shortAmount'
  | 'longCount'
  | 'shortCount'
  | 'longShortRatio'
  | 'avgAmount'
  | 'maxAmount'
  | 'minAmount';

/**
 * 清算排行榜查询参数
 */
export interface RankQueryParams {
  /** 时间范围 */
  timeRange: string;
  /** 币种类型过滤 */
  coinTypeFilter?: 'all' | 'Major' | 'Altcoin';
  /** 排行类型 */
  rankType?: 'amount' | 'count';
  /** 排序字段 */
  sortField?: RankSortField;
  /** 排序方向 */
  sortDirection?: 'asc' | 'desc';
  /** 限制数量 */
  limit?: number;
}
