<script lang="ts">
  import AlertCircleIcon from '@lucide/svelte/icons/alert-circle';
  import BellIcon from '@lucide/svelte/icons/bell';
  import CheckCircleIcon from '@lucide/svelte/icons/check-circle';
  import ChevronDownIcon from '@lucide/svelte/icons/chevron-down';
  import ChevronUpIcon from '@lucide/svelte/icons/chevron-up';
  import ChevronsDownIcon from '@lucide/svelte/icons/chevrons-down';
  import ChevronsUpIcon from '@lucide/svelte/icons/chevrons-up';
  import InfoIcon from '@lucide/svelte/icons/info';
  import MessageSquareIcon from '@lucide/svelte/icons/message-square';
  import RefreshCwIcon from '@lucide/svelte/icons/refresh-cw';
  import XCircleIcon from '@lucide/svelte/icons/x-circle';
  import { onDestroy,onMount } from 'svelte';

  import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
  } from '$lib/components/ui/accordion';
  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
  } from '$lib/components/ui/tooltip';
  import { realTimeMessagesStore } from '$lib/stores/features/realTimeMessages';
  import type { QueryFormData } from '$lib/types';

  // 导入新的查询表单组件
  import RealTimeQueryForm from './QueryForm.svelte';

  // 消息类型图标映射
  const messageTypeIcons: Record<string, any> = {
    info: InfoIcon,
    success: CheckCircleIcon,
    warning: AlertCircleIcon,
    error: XCircleIcon,
    notification: BellIcon,
  };

  // 消息类型样式映射 - 使用 CSS 类
  const messageTypeCardClasses: Record<string, string> = {
    info: 'message-card-info',
    success: 'message-card-success',
    warning: 'message-card-warning',
    error: 'message-card-error',
    notification: 'message-card-notification',
  };

  // 消息类型图标样式映射
  const messageTypeIconClasses: Record<string, string> = {
    info: 'message-icon-info',
    success: 'message-icon-success',
    warning: 'message-icon-warning',
    error: 'message-icon-error',
    notification: 'message-icon-notification',
  };

  // Badge 变体映射
  const messageBadgeVariants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> =
    {
      info: 'outline',
      success: 'default',
      warning: 'secondary',
      error: 'destructive',
      notification: 'outline',
    };

  // 格式化时间（时区感知）
  function formatTime(timestamp: string): string {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });
    } catch (error) {
      console.warn('Failed to format time:', error);
      return new Date(timestamp).toISOString();
    }
  }

  // 手动刷新消息
  function handleRefresh() {
    realTimeMessagesStore.refreshMessages();
  }

  // 标记消息为已读
  function markAsRead(messageId: string) {
    realTimeMessagesStore.markAsRead(messageId);
  }

  // 清除所有消息
  function clearAllMessages() {
    realTimeMessagesStore.clearAllMessages();
  }

  // 标记所有消息为已读
  function markAllAsRead() {
    realTimeMessagesStore.markAllAsRead();
  }

  // 查询相关状态
  let queryLoading = false;
  let showAdvancedQuery = false;
  let currentQuery: QueryFormData | null = null;
  let queryResultStats = {
    totalCount: 0,
    filteredCount: 0,
    queryTime: 0,
  };

  // Accordion 状态管理
  let accordionValue: string[] = [];

  // 展开所有时间段
  function expandAllTimeSlots() {
    accordionValue = groupedMessages.map((slot) => slot.slotKey);
  }

  // 折叠所有时间段
  function collapseAllTimeSlots() {
    accordionValue = [];
  }

  // 过滤消息的函数
  function filterMessages(messages: any[], query: QueryFormData | null) {
    if (!query) return messages;

    return messages.filter((message) => {
      // 关键词过滤
      if (query.keyword && query.keyword.trim()) {
        const keyword = query.keyword.toLowerCase();
        const matchesKeyword =
          message.title.toLowerCase().includes(keyword) ||
          message.content.toLowerCase().includes(keyword);
        if (!matchesKeyword) return false;
      }

      // 消息类型过滤
      if (query.messageType !== 'all' && message.type !== query.messageType) {
        return false;
      }

      // 未读消息过滤
      if (query.unreadOnly && message.isRead) {
        return false;
      }

      // 时间范围过滤
      if (query.timeRange.startTime && query.timeRange.endTime) {
        const messageTime = new Date(message.timestamp);
        const startTime = new Date(query.timeRange.startTime);
        const endTime = new Date(query.timeRange.endTime);

        if (messageTime < startTime || messageTime > endTime) {
          return false;
        }
      }

      return true;
    });
  }

  // 排序消息的函数
  function sortMessages(messages: any[], query: QueryFormData | null) {
    if (!query) return messages;

    const sorted = [...messages];

    sorted.sort((a, b) => {
      let aValue, bValue;

      switch (query.sortField) {
        case 'timestamp':
          aValue = new Date(a.timestamp).getTime();
          bValue = new Date(b.timestamp).getTime();
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'priority':
          // 假设消息有优先级字段，如果没有则使用类型作为优先级
          const priorityMap: Record<string, number> = {
            error: 3,
            warning: 2,
            info: 1,
            success: 1,
            notification: 1,
          };
          aValue = priorityMap[a.type] || 0;
          bValue = priorityMap[b.type] || 0;
          break;
        default:
          return 0;
      }

      if (query.sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });

    return sorted;
  }

  // 按15分钟时间段分组消息
  function groupMessagesByTimeSlot(messages: any[]) {
    const groups = new Map<string, any[]>();

    messages.forEach((message) => {
      const messageTime = new Date(message.timestamp);
      // 计算15分钟时间段的起始时间
      const minutes = messageTime.getMinutes();
      const slotMinutes = Math.floor(minutes / 15) * 15;
      const slotStart = new Date(messageTime);
      slotStart.setMinutes(slotMinutes, 0, 0);

      // 生成时间段标识符
      const slotKey = slotStart.toISOString();

      if (!groups.has(slotKey)) {
        groups.set(slotKey, []);
      }
      groups.get(slotKey)!.push(message);
    });

    // 按时间段排序，最新的在前面
    const sortedGroups = Array.from(groups.entries())
      .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
      .map(([slotKey, messages]) => ({
        slotKey,
        slotStart: new Date(slotKey),
        messages: messages.sort(
          (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        ),
      }));

    return sortedGroups;
  }

  // 格式化时间段标题
  function formatTimeSlot(slotStart: Date): string {
    const endTime = new Date(slotStart.getTime() + 15 * 60 * 1000);
    const formatOptions: Intl.DateTimeFormatOptions = {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    };

    const start = slotStart.toLocaleString('zh-CN', formatOptions);
    const end = endTime.toLocaleString('zh-CN', formatOptions);

    return `${start} - ${end}`;
  }

  // 获取过滤和排序后的消息，然后按时间段分组
  $: groupedMessages = (() => {
    const startTime = performance.now();
    const filtered = filterMessages($realTimeMessagesStore.messages, currentQuery);
    const sorted = sortMessages(filtered, currentQuery);
    const grouped = groupMessagesByTimeSlot(sorted);
    const endTime = performance.now();

    queryResultStats = {
      totalCount: $realTimeMessagesStore.messages.length,
      filteredCount: sorted.length,
      queryTime: Math.round(endTime - startTime),
    };

    return grouped;
  })();

  // 保持向后兼容性，获取扁平化的消息列表
  $: filteredAndSortedMessages = groupedMessages.flatMap((group) => group.messages);

  // 处理查询提交
  function handleQuery(event: CustomEvent<QueryFormData>) {
    const queryData = event.detail;
    console.log('执行查询:', queryData);

    queryLoading = true;

    // 模拟查询延迟以显示加载状态
    setTimeout(() => {
      currentQuery = queryData;
      queryLoading = false;
      console.log('查询完成，结果统计:', queryResultStats);
    }, 500);
  }

  // 处理查询重置
  function handleQueryReset() {
    console.log('重置查询条件');
    queryLoading = true;

    setTimeout(() => {
      currentQuery = null;
      queryLoading = false;
      console.log('查询条件已重置');
    }, 300);
  }

  // 组件生命周期管理：标签页感知轮询
  onMount(() => {
    console.log('RealTimeMessages 组件挂载，启动标签页感知轮询');
    realTimeMessagesStore.startTabAwarePolling();

    // 当用户查看实时消息页面时，自动标记所有消息为已读
    // 这样徽章就不会在当前页面显示
    setTimeout(() => {
      realTimeMessagesStore.markAllAsRead();
    }, 1000); // 延迟1秒，确保消息已加载
  });

  onDestroy(() => {
    console.log('RealTimeMessages 组件卸载，检查是否需要停止轮询');
    realTimeMessagesStore.stopTabAwarePolling();
  });
</script>

<TooltipProvider>
  <div class="space-y-4 sm:space-y-6">
    <!-- 页面标题和操作区域 -->
    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div class="flex-center space-x-3">
        <MessageSquareIcon class="text-primary h-6 w-6 sm:h-8 sm:w-8" />
        <div>
          <h1 class="text-foreground text-2xl font-bold sm:text-3xl">实时消息</h1>
          <p class="text-muted-foreground text-sm sm:text-base">实时系统通知和市场消息监控</p>
        </div>
      </div>

      <div class="flex flex-wrap items-center gap-2">
        <Button variant="outline" size="sm" onclick={handleRefresh}>
          <RefreshCwIcon class="mr-2 h-4 w-4" />
          刷新
        </Button>
        <Button variant="outline" size="sm" onclick={markAllAsRead}>全部已读</Button>
        <Button variant="outline" size="sm" onclick={clearAllMessages}>清除全部</Button>

        <!-- 折叠/展开控制 -->
        {#if groupedMessages.length > 0}
          <div class="bg-border h-4 w-px"></div>
          <Button variant="outline" size="sm" onclick={expandAllTimeSlots}>
            <ChevronsDownIcon class="mr-2 h-4 w-4" />
            展开全部
          </Button>
          <Button variant="outline" size="sm" onclick={collapseAllTimeSlots}>
            <ChevronsUpIcon class="mr-2 h-4 w-4" />
            折叠全部
          </Button>
        {/if}
      </div>
    </div>

    <!-- 复合条件查询框 -->
    <RealTimeQueryForm
      loading={queryLoading}
      bind:showAdvanced={showAdvancedQuery}
      on:query={handleQuery}
      on:reset={handleQueryReset}
    />

    <!-- 消息列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex-between">
          <span>消息列表</span>
          {#if currentQuery}
            <div class="flex-center text-muted-foreground space-x-2 text-sm">
              <Badge variant="outline" class="text-small">
                {queryResultStats.filteredCount} / {queryResultStats.totalCount}
              </Badge>
              <span class="text-small">查询耗时: {queryResultStats.queryTime}ms</span>
            </div>
          {/if}
        </CardTitle>
        <CardDescription>
          {currentQuery ? '根据查询条件过滤的消息结果' : '实时接收的系统通知和市场消息'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {#if groupedMessages.length === 0}
          <div class="text-muted-foreground py-8 text-center">
            <MessageSquareIcon class="text-muted-foreground/50 mx-auto mb-4 h-12 w-12" />
            <p>{currentQuery ? '没有符合条件的消息' : '暂无消息'}</p>
            {#if currentQuery && $realTimeMessagesStore.messages.length > 0}
              <p class="mt-2 text-sm">尝试调整查询条件或重置筛选器</p>
            {/if}
          </div>
        {:else}
          <Accordion type="multiple" bind:value={accordionValue} class="space-y-4">
            {#each groupedMessages as timeSlot (timeSlot.slotKey)}
              <AccordionItem value={timeSlot.slotKey}>
                <AccordionTrigger class="px-3 py-2 hover:no-underline sm:px-4 sm:py-3">
                  <div class="flex-center w-full space-x-3">
                    <div class="flex-center space-x-2">
                      <div class="bg-primary h-3 w-3 rounded-full"></div>
                      <h3 class="text-foreground text-base font-semibold sm:text-lg">
                        {formatTimeSlot(timeSlot.slotStart)}
                      </h3>
                    </div>
                    <Badge variant="outline" class="text-small mr-2 ml-auto">
                      {timeSlot.messages.length} 条消息
                    </Badge>
                  </div>
                </AccordionTrigger>
                <AccordionContent class="px-3 pb-3 sm:px-4 sm:pb-4">
                  <!-- 消息网格布局 -->
                  <div
                    class="grid grid-cols-1 gap-3 sm:grid-cols-1 sm:gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                  >
                    {#each timeSlot.messages as message (message.id)}
                      <Card
                        class={`transition-shadow duration-300 hover:shadow-md ${messageTypeCardClasses[message.type]} ${!message.isRead ? 'ring-primary/20 shadow-lg ring-2' : 'hover:shadow-sm'} flex h-full flex-col`}
                      >
                        <CardHeader class="pb-2">
                          <div class="flex items-start space-x-2">
                            <svelte:component
                              this={messageTypeIcons[message.type]}
                              class={`mt-0.5 h-4 w-4 flex-shrink-0 transition-colors duration-200 ${messageTypeIconClasses[message.type]} ${!message.isRead ? '' : 'opacity-75'}`}
                            />
                            <div class="min-w-0 flex-1">
                              <Tooltip>
                                <TooltipTrigger
                                  class={`message-title ${!message.isRead ? 'text-current' : 'opacity-90'}`}
                                >
                                  {message.title}
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p class="tooltip-content-small">{message.title}</p>
                                </TooltipContent>
                              </Tooltip>
                              <div class="flex-center mt-1 space-x-1">
                                <Badge
                                  variant={messageBadgeVariants[message.type] || 'outline'}
                                  class="badge-small"
                                >
                                  {message.type}
                                </Badge>
                                {#if !message.isRead}
                                  <Badge variant="default" class="badge-small animate-pulse">
                                    未读
                                  </Badge>
                                {/if}
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent class="flex flex-1 flex-col justify-between pt-0 pb-3">
                          <Tooltip>
                            <TooltipTrigger
                              class={`message-content ${!message.isRead ? 'opacity-95' : 'opacity-80'}`}
                            >
                              {message.content}
                            </TooltipTrigger>
                            <TooltipContent>
                              <p class="tooltip-content-medium">{message.content}</p>
                            </TooltipContent>
                          </Tooltip>
                          <div class="flex-between gap-2">
                            <span class="text-small-muted flex-shrink-0">
                              {new Date(message.timestamp).toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                hour12: false,
                              })}
                            </span>
                            {#if !message.isRead}
                              <Button
                                variant="ghost"
                                size="sm"
                                class="text-small hover:bg-accent hover:text-accent-foreground h-6 px-2 transition-colors duration-200"
                                onclick={() => markAsRead(message.id)}
                              >
                                已读
                              </Button>
                            {/if}
                          </div>
                        </CardContent>
                      </Card>
                    {/each}
                  </div>
                </AccordionContent>
              </AccordionItem>
            {/each}
          </Accordion>
        {/if}
      </CardContent>
    </Card>
  </div>
</TooltipProvider>
