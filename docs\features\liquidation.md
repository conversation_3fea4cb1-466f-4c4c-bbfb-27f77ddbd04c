# 爆仓分析功能文档

专业的加密货币爆仓数据分析系统，提供实时监控、多维度分析和专业图表展示。

## 🎯 功能概览

爆仓分析功能是一个专业的金融数据可视化模块，专门用于分析加密货币市场的爆仓数据。它提供了两种主要的分析视角：

- **快照分析**：特定时间段内的爆仓数据概览
- **趋势分析**：长期趋势和模式识别

## 📊 核心功能

### 1. 实时数据监控

- **自动刷新**：支持自定义刷新间隔（默认每分钟）
- **实时更新**：数据变化时自动更新图表
- **状态指示**：清晰的加载和错误状态提示

### 2. 多维度数据分析

#### 快照数据分析
- **KPI 指标卡**：总清算金额、笔数、多空比例等核心指标
- **多空分布**：多头 vs 空头清算金额占比分析
- **订单大小分布**：按阈值分类的清算笔数分布

#### 趋势数据分析
- **时间序列分析**：支持多种时间粒度（15分钟到1天）
- **币种分类对比**：主流币 vs 山寨币清算趋势对比
- **多空趋势**：长期多空清算金额变化趋势

### 3. 专业图表展示

#### KPI 指标卡
```typescript
// 显示核心清算指标
- 总清算金额 (USD)
- 总清算笔数
- 多空比例
- 平均清算金额
- 主流币占比
```

#### 双轴线图
```typescript
// 主流币 vs 山寨币对比
- 左Y轴：主流币清算金额
- 右Y轴：山寨币清算金额
- 不同颜色区分币种类型
- 不同线型区分多空方向
```

#### 堆叠面积图
```typescript
// 多空清算金额趋势
- 支持绝对值和百分比模式
- 颜色区分多空方向
- 时间轴交互缩放
```

#### 饼图分析
```typescript
// 多维度占比分析
- 多空清算金额占比
- 订单大小阈值分布
- 支持环形图模式
```

## 🔧 使用指南

### 基本使用

```svelte
<script>
  import { LiquidationDashboard } from '$lib/components/features/liquidation';
</script>

<LiquidationDashboard />
```

### 高级配置

```typescript
import { liquidationStore } from '$lib/stores/features/liquidation';

// 设置时间范围
liquidationStore.updateTimeFilters({
  snapshotTimeRange: '24h',
  trendTimeFrame: '1h',
  trendDuration: '7d',
});

// 开始自动刷新
liquidationStore.startAutoRefresh(60000); // 每分钟刷新

// 手动刷新数据
await liquidationStore.refreshAllData();
```

## 📈 数据模型

### LiquidationData 接口

```typescript
interface LiquidationData {
  id: string;                    // 唯一标识
  datetime: string;              // 时间戳 (ISO 8601)
  amountUsd: number;            // 清算金额 (USD)
  side: 0 | 1;                  // 方向 (0: 空头, 1: 多头)
  symbol: string;               // 交易对符号
  threshold: string;            // 订单大小阈值
  coinType: 'Major' | 'Altcoin'; // 币种类型
}
```

### LiquidationStats 接口

```typescript
interface LiquidationStats {
  totalAmount: number;          // 总清算金额
  totalCount: number;           // 总清算笔数
  longAmount: number;           // 多头清算金额
  shortAmount: number;          // 空头清算金额
  longShortRatio: number;       // 多空比例
  averageAmount: number;        // 平均清算金额
  majorCoinAmount: number;      // 主流币清算金额
  altcoinAmount: number;        // 山寨币清算金额
  majorCoinRatio: number;       // 主流币占比
}
```

## ⚙️ 配置选项

### 时间范围配置

```typescript
// 快照数据时间范围
type SnapshotTimeRange = '1h' | '4h' | '12h' | '24h';

// 趋势数据时间框架
type TrendTimeFrame = '15m' | '1h' | '4h' | '1d';

// 趋势数据持续时间
type TrendDuration = '1h' | '4h' | '12h' | '1d' | '3d' | '7d' | '30d';
```

### 视图模式配置

```typescript
// 趋势视图模式
type TrendViewMode = 'overall' | 'comparison';

// overall: 全市场视图 - 显示整体多空趋势
// comparison: 对比视图 - 显示主流币 vs 山寨币对比
```

## 🎨 自定义样式

### 图表主题配置

```typescript
// 在图表组件中自定义颜色
const chartColors = {
  long: '#10B981',      // 多头颜色 (绿色)
  short: '#EF4444',     // 空头颜色 (红色)
  major: '#3B82F6',     // 主流币颜色 (蓝色)
  altcoin: '#8B5CF6',   // 山寨币颜色 (紫色)
};
```

### 响应式设计

```css
/* 移动端适配 */
@media (max-width: 768px) {
  .liquidation-dashboard {
    grid-template-columns: 1fr;
  }
  
  .chart-panel {
    height: 300px;
  }
}
```

## 🔍 故障排除

### 常见问题

1. **数据加载失败**
   - 检查网络连接
   - 验证 API 端点配置
   - 查看浏览器控制台错误信息

2. **图表显示异常**
   - 确认数据格式正确
   - 检查图表容器尺寸
   - 验证 ECharts 版本兼容性

3. **自动刷新不工作**
   - 检查刷新间隔设置
   - 确认组件未被销毁
   - 验证数据服务状态

### 调试技巧

```typescript
// 启用调试模式
liquidationStore.subscribe(state => {
  console.log('Liquidation state:', state);
});

// 监控数据变化
$: console.log('Snapshot data updated:', $liquidationStore.snapshotData);
```

## 🚀 性能优化

### 数据缓存

- 自动缓存最近的查询结果
- 避免重复的 API 请求
- 智能的数据更新策略

### 图表优化

- 按需加载图表组件
- 虚拟化大数据集
- 优化重绘性能

### 内存管理

- 自动清理过期数据
- 限制历史数据保留量
- 及时销毁事件监听器

## 📝 最佳实践

1. **合理设置刷新间隔**：根据数据更新频率和用户需求平衡
2. **选择合适的时间粒度**：避免过细的粒度导致性能问题
3. **监控错误状态**：及时处理网络错误和数据异常
4. **优化用户体验**：提供清晰的加载状态和错误提示

## 🔗 相关文档

- [组件文档 - LiquidationDashboard](../components/README.md#liquidationdashboard-组件-🆕)
- [状态管理 - Liquidation Store](../stores/README.md#liquidation-store-🆕)
- [服务层 - LiquidationDataService](../services/README.md#liquidationdataservice-🆕)
- [API 文档 - Liquidation Types](../api/types/README.md)
