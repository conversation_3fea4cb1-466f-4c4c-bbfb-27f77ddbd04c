[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / LiquidationData

# Interface: LiquidationData

Defined in: src/lib/types/liquidation.ts:4

## Properties

### amountUsd

> **amountUsd**: `number`

Defined in: src/lib/types/liquidation.ts:7

---

### coinType

> **coinType**: `"Major"` \| `"Altcoin"`

Defined in: src/lib/types/liquidation.ts:11

---

### datetime

> **datetime**: `string`

Defined in: src/lib/types/liquidation.ts:6

---

### id

> **id**: `string`

Defined in: src/lib/types/liquidation.ts:5

---

### side

> **side**: `number`

Defined in: src/lib/types/liquidation.ts:8

---

### symbol

> **symbol**: `string`

Defined in: src/lib/types/liquidation.ts:9

---

### threshold

> **threshold**: `string`

Defined in: src/lib/types/liquidation.ts:10
