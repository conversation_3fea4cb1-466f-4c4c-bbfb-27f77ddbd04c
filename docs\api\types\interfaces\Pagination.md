[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / Pagination

# Interface: Pagination

Defined in: src/lib/types/dataQuery.ts:20

## Properties

### currentPage

> **currentPage**: `number`

Defined in: src/lib/types/dataQuery.ts:21

---

### itemsPerPage

> **itemsPerPage**: `number`

Defined in: src/lib/types/dataQuery.ts:22

---

### totalItems

> **totalItems**: `number`

Defined in: src/lib/types/dataQuery.ts:23

---

### totalPages

> **totalPages**: `number`

Defined in: src/lib/types/dataQuery.ts:24
