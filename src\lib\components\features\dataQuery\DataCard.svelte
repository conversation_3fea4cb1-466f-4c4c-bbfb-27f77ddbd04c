<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import type { MarketEvent } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';

  interface Props {
    item: MarketEvent;
    isHighlighted?: boolean;
  }

  const { item, isHighlighted = false }: Props = $props();

  // 辅助函数：从MarketEvent中提取特定参数值
  function getParameterValue(event: MarketEvent, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  // 辅助函数：格式化Unix时间戳为可读日期
  function formatEventDate(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  }

  // 辅助函数：获取事件类型的显示名称
  function getEventTypeLabel(eventType: string): string {
    return MARKET_EVENT_TYPE_LABELS[eventType as keyof typeof MARKET_EVENT_TYPE_LABELS] || eventType;
  }

  // 获取事件的显示名称
  function getEventDisplayName(event: MarketEvent): string {
    const base = getParameterValue(event, 'base') as string;
    const pair = getParameterValue(event, 'pair') as string;
    const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;

    if (base) {
      return `${base} - ${getEventTypeLabel(event.marketEventType)}`;
    } else if (pair) {
      return `${pair} - ${getEventTypeLabel(event.marketEventType)}`;
    } else if (exchangeLabel) {
      return `${exchangeLabel} - ${getEventTypeLabel(event.marketEventType)}`;
    }

    return getEventTypeLabel(event.marketEventType);
  }

  // 获取事件类型对应的 Badge 变体
  function getEventTypeVariant(eventType: string): 'default' | 'secondary' | 'destructive' | 'outline' {
    switch (eventType) {
      case 'LIQUIDATION_ORDER':
        return 'destructive';
      case 'TWAP_DETECTION':
        return 'default';
      case 'VOLUME_INCREASE':
        return 'secondary';
      default:
        return 'outline';
    }
  }

  // 获取事件类型对应的颜色
  function getEventTypeColor(eventType: string): string {
    const colors: Record<string, string> = {
      LIQUIDATION_ORDER: 'text-red-600 dark:text-red-400',
      TWAP_DETECTION: 'text-blue-600 dark:text-blue-400',
      VOLUME_INCREASE: 'text-green-600 dark:text-green-400',
      FUNDING_RATE_SWITCH: 'text-purple-600 dark:text-purple-400',
      EXCHANGE_TRANSFER: 'text-orange-600 dark:text-orange-400',
    };
    return colors[eventType] || 'text-gray-600 dark:text-gray-400';
  }
</script>

<Card
  class="transition-all duration-200 {isHighlighted
    ? 'ring-primary border-primary/50 shadow-lg ring-2'
    : 'hover:shadow-md'} w-full"
>
  <CardHeader class="pb-3">
    <CardTitle class="flex items-center justify-between text-base">
      <span class="font-semibold">{getEventDisplayName(item)}</span>
      <Badge variant={getEventTypeVariant(item.marketEventType)} class="text-xs">
        {getEventTypeLabel(item.marketEventType)}
      </Badge>
    </CardTitle>
  </CardHeader>

  <CardContent class="space-y-3 pt-0">
    <!-- 基本信息 -->
    <div class="grid grid-cols-1 gap-3 text-sm sm:grid-cols-2">
      <div class="space-y-1">
        <div class="text-muted-foreground">ID</div>
        <div class="text-foreground font-mono text-xs">{item.id}</div>
      </div>

      <div class="space-y-1">
        <div class="text-muted-foreground">事件类型</div>
        <div class="font-medium {getEventTypeColor(item.marketEventType)}">{getEventTypeLabel(item.marketEventType)}</div>
      </div>
    </div>

    <!-- 关键参数信息 -->
    <div class="space-y-2">
      {#if getParameterValue(item, 'amountUsd')}
        <div class="space-y-1">
          <div class="text-muted-foreground text-sm">金额 (USD)</div>
          <div class="text-foreground bg-muted rounded px-2 py-1 font-mono text-sm">
            ${Number(getParameterValue(item, 'amountUsd')).toLocaleString()}
          </div>
        </div>
      {/if}

      {#if getParameterValue(item, 'exchangeLabel')}
        <div class="space-y-1">
          <div class="text-muted-foreground text-sm">交易所</div>
          <div class="text-foreground bg-muted rounded px-2 py-1 text-sm">
            {getParameterValue(item, 'exchangeLabel')}
          </div>
        </div>
      {/if}
    </div>

    <!-- 时间信息 -->
    <div class="space-y-1">
      <div class="text-muted-foreground text-sm">事件时间</div>
      <div class="text-foreground bg-muted rounded px-2 py-1 font-mono text-sm">
        {formatEventDate(item.date)}
      </div>
    </div>

    <!-- 预留扩展区域 -->
    <div class="border-border border-t pt-2">
      <div class="text-muted-foreground py-2 text-center text-xs">
        <!-- 这里预留给后续的详细信息展示 -->
        详细信息区域（待实现）
      </div>
    </div>

    <!-- 操作区域 -->
    <div class="border-border flex items-center justify-between border-t pt-2">
      <div class="text-muted-foreground text-xs">
        {#if isHighlighted}
          <span class="inline-flex items-center">
            <div class="bg-primary mr-1 h-2 w-2 rounded-full"></div>
            已选中
          </span>
        {:else}
          点击时间轴节点查看详情
        {/if}
      </div>

      <!-- 预留操作按钮区域 -->
      <div class="flex items-center space-x-1">
        <!-- 这里可以添加编辑、删除等操作按钮 -->
      </div>
    </div>
  </CardContent>
</Card>
