<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  import CurrencyIcon from '$lib/components/features/CurrencyIcon.svelte';

  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    formatUsdAmount,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取订单簿失衡相关参数
  const deltaUsd = getParameterValue(event, 'deltaUsd') as number;
  const side = getParameterValue(event, 'side') as number;
  const variationPercent = getParameterValue(event, 'variationPercent') as number;
  const bidsSumUsd = getParameterValue(event, 'bidsSumUsd') as number;
  const asksSumUsd = getParameterValue(event, 'asksSumUsd') as number;
  const priceUsd = getParameterValue(event, 'priceUsd') as number;
  const base = getParameterValue(event, 'base') as string;
  const datetime = getParameterValue(event, 'datetime') as string;
  const currencyId = event.currency;



  // 获取失衡方向和描述
  const isMoreAsks = side === 2; // side 2 表示卖盘优势
  const imbalanceDirection = isMoreAsks ? 'more aggregated asks than bids' : 'more aggregated bids than asks';
  const imbalanceBadgeVariant = isMoreAsks ? 'destructive' : 'default';

  // 格式化时间显示为 HH:mm 格式
  function formatTime(dateTimeStr: string): string {
    try {
      const eventTime = new Date(dateTimeStr);
      const hours = eventTime.getHours().toString().padStart(2, '0');
      const minutes = eventTime.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    } catch {
      return '';
    }
  }

  // 卡片样式 - 使用 Tailwind 类优化
  const cardClasses = [
    'w-full transition-all duration-200',
    isHighlighted
      ? 'ring-2 ring-primary border-primary/50 shadow-lg'
      : 'hover:shadow-md',
    onCardClick ? 'cursor-pointer' : ''
  ].filter(Boolean).join(' ');

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }


</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3 space-y-2">
    <!-- 顶部：币种信息 -->
    <div class="flex items-center gap-2">
      <!-- 货币图标 -->
      <CurrencyIcon
        currencyId={currencyId}
        symbol={base}
        size="size-5"
      />

      <!-- 币种符号 -->
      <span class="font-bold text-sm text-foreground">
        {(base || '未知').toUpperCase()}
      </span>

      <!-- 时间 -->
      <span class="text-xs text-muted-foreground">
        {formatTime(datetime)}
      </span>
    </div>

    <!-- 中间：失衡信息 -->
    <div class="text-xs text-muted-foreground">
      <span class="font-bold">{(base || '').toUpperCase()}</span> has
      <Badge variant={imbalanceBadgeVariant} class="mx-1 text-xs">
        {variationPercent?.toFixed(2)}%
      </Badge>
      {imbalanceDirection}
    </div>

    <!-- 底部：买盘、价格、卖盘并排显示 -->
    <div class="grid grid-cols-3 gap-2 text-sm">
      <!-- 聚合买盘 -->
      <div class="text-left space-y-1">
        <div class="text-xs text-muted-foreground">Aggregated bids</div>
        <div class="font-semibold text-green-600 dark:text-green-400">
          {formatUsdAmount(bidsSumUsd || 0)}
        </div>
      </div>

      <!-- 价格 -->
      <div class="text-center space-y-1">
        <div class="text-xs text-muted-foreground">Price</div>
        <div class="font-semibold text-foreground">
          ${priceUsd?.toFixed(8) || '0.00000000'}
        </div>
      </div>

      <!-- 聚合卖盘 -->
      <div class="text-right space-y-1">
        <div class="text-xs text-muted-foreground">Aggregated asks</div>
        <div class="font-semibold text-red-600 dark:text-red-400">
          {formatUsdAmount(asksSumUsd || 0)}
        </div>
      </div>
    </div>

    <!-- 买卖盘比例条 -->
    {#if bidsSumUsd && asksSumUsd}
      {@const totalSum = bidsSumUsd + asksSumUsd}
      {@const bidsPercentage = (bidsSumUsd / totalSum) * 100}
      {@const asksPercentage = (asksSumUsd / totalSum) * 100}

      <div class="flex h-1 rounded-full overflow-hidden bg-muted">
        <!-- 买盘部分 -->
        <div
          class="bg-green-500 dark:bg-green-400 transition-all duration-300"
          style="width: {bidsPercentage}%"
          title="Bids: {bidsPercentage.toFixed(1)}%"
        ></div>
        <!-- 卖盘部分 -->
        <div
          class="bg-red-500 dark:bg-red-400 transition-all duration-300"
          style="width: {asksPercentage}%"
          title="Asks: {asksPercentage.toFixed(1)}%"
        ></div>
      </div>
    {/if}
  </CardContent>
</Card>
