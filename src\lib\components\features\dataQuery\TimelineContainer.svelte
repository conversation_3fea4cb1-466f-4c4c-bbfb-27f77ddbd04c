<script lang="ts">
  import type { MarketEvent } from '$lib/types';

  import Timeline from './Timeline.svelte';

  interface Props {
    items: MarketEvent[];
    hasMore?: boolean;
    loadingMore?: boolean;
    onLoadMore?: () => void;
  }

  const { items = [], hasMore = false, loadingMore = false, onLoadMore }: Props = $props();

  // 排序后的数据（按时间倒序）
  const sortedItems: MarketEvent[] = $derived(
    [...items].sort((a, b) => b.date - a.date) // 使用Unix时间戳进行排序
  );

  // 滚动容器引用
  let scrollContainer: HTMLDivElement | undefined = $state();

  // 处理滚动事件，检测是否需要加载更多
  function handleScroll() {
    if (!scrollContainer || !hasMore || loadingMore || !onLoadMore) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

    // 当滚动到80%时触发加载更多
    if (scrollPercentage > 0.8) {
      onLoadMore();
    }
  }
</script>

<div class="w-full">
  {#if sortedItems.length === 0}
    <!-- 空状态 -->
    <div class="flex flex-col items-center justify-center py-16 text-center">
      <div class="bg-muted mb-6 flex h-16 w-16 items-center justify-center rounded-full">
        <svg
          class="text-muted-foreground h-8 w-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
          ></path>
        </svg>
      </div>
      <h3 class="text-foreground mb-2 text-lg font-semibold">暂无数据</h3>
      <p class="text-muted-foreground max-w-md">
        没有找到匹配的查询结果。请尝试调整查询条件或检查数据源。
      </p>
    </div>
  {:else}
    <!-- 时间轴布局 -->
    <div class="h-full w-full">
      <!-- 时间轴滚动容器 -->
      <div
        bind:this={scrollContainer}
        class="scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent overflow-y-auto overflow-x-hidden pr-2"
        onscroll={handleScroll}
      >
        <Timeline items={sortedItems} />

        <!-- 加载更多指示器 -->
        {#if loadingMore}
          <div class="flex items-center justify-center py-8">
            <div class="flex items-center space-x-2">
              <div
                class="border-primary h-4 w-4 animate-spin rounded-full border-2 border-t-transparent"
              ></div>
              <span class="text-muted-foreground text-sm">加载更多数据...</span>
            </div>
          </div>
        {:else if hasMore}
          <div class="flex items-center justify-center py-6">
            <button
              onclick={() => onLoadMore?.()}
              class="text-muted-foreground hover:text-foreground border-border/50 hover:border-border bg-card/50 hover:bg-card rounded-lg border px-4 py-2 text-sm transition-colors duration-200"
            >
              点击加载更多
            </button>
          </div>
        {:else if sortedItems.length > 0}
          <div class="flex items-center justify-center py-6">
            <span class="text-muted-foreground text-sm">已显示全部数据</span>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>
