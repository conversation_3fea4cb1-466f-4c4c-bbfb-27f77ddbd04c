[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / API_ENDPOINTS

# Variable: API_ENDPOINTS

> `const` **API_ENDPOINTS**: `object`

Defined in: src/lib/constants/api.ts:23

API 端点常量

定义了应用中所有 API 端点的路径常量，便于统一管理和维护。

## Type declaration

### CHARTS_ALL

> `readonly` **CHARTS_ALL**: `"/charts/all"` = `'/charts/all'`

### CHARTS_BAR

> `readonly` **CHARTS_BAR**: `"/charts/bar"` = `'/charts/bar'`

### CHARTS_LINE

> `readonly` **CHARTS_LINE**: `"/charts/line"` = `'/charts/line'`

### CHARTS_PIE

> `readonly` **CHARTS_PIE**: `"/charts/pie"` = `'/charts/pie'`

### CHARTS_SCATTER

> `readonly` **CHARTS_SCATTER**: `"/charts/scatter"` = `'/charts/scatter'`

### DASHBOARD_OVERVIEW

> `readonly` **DASHBOARD_OVERVIEW**: `"/dashboard/overview"` = `'/dashboard/overview'`

### DASHBOARD_STATS

> `readonly` **DASHBOARD_STATS**: `"/dashboard/stats"` = `'/dashboard/stats'`

### DATA_EXPORT

> `readonly` **DATA_EXPORT**: `"/data/export"` = `'/data/export'`

### DATA_QUERY

> `readonly` **DATA_QUERY**: `"/data/query"` = `'/data/query'`

### LIQUIDATION_SNAPSHOT

> `readonly` **LIQUIDATION_SNAPSHOT**: `"/liquidation/snapshot"` = `'/liquidation/snapshot'`

### LIQUIDATION_TREND

> `readonly` **LIQUIDATION_TREND**: `"/liquidation/trend"` = `'/liquidation/trend'`

### LIQUIDATION_TREND_DETAILED

> `readonly` **LIQUIDATION_TREND_DETAILED**: `"/liquidation/trend-detailed"` = `'/liquidation/trend-detailed'`

## Example

```typescript
import { API_ENDPOINTS } from '$lib/constants/api';

// 构建完整的 API URL
const url = `${API_CONFIG.BASE_URL}${API_ENDPOINTS.DASHBOARD_STATS}`;

// 发起请求
const response = await fetch(url);
```
