[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / QueryResults

# Interface: QueryResults

Defined in: src/lib/types/dataQuery.ts:28

## Properties

### items

> **items**: [`DataItem`](DataItem.md)[]

Defined in: src/lib/types/dataQuery.ts:29

---

### loading

> **loading**: `boolean`

Defined in: src/lib/types/dataQuery.ts:30

---

### pagination

> **pagination**: [`Pagination`](Pagination.md)

Defined in: src/lib/types/dataQuery.ts:31
