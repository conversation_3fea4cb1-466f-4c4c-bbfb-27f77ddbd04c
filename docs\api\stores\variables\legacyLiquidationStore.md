[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [stores](../README.md) / legacyLiquidationStore

# Variable: legacyLiquidationStore

> `const` **legacyLiquidationStore**: `object`

Defined in: src/lib/stores/liquidation/liquidation.ts:245

## Type declaration

### charts

> **charts**: `Readable`\<[`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]\>

### loading

> **loading**: `Readable`\<`boolean`\>

### setLoading()

> **setLoading**: (`isLoading`) => `void`

#### Parameters

##### isLoading

`boolean`

#### Returns

`void`

### setSnapshotTimeRange()

> **setSnapshotTimeRange**: (`range`) => `void`

#### Parameters

##### range

[`SnapshotTimeRange`](../../types/type-aliases/SnapshotTimeRange.md)

#### Returns

`void`

### setTimeFilters()

> **setTimeFilters**: (`filters`) => `void`

#### Parameters

##### filters

`Partial`\<[`TimeFilters`](../../types/interfaces/TimeFilters.md)\>

#### Returns

`void`

### setTrendDuration()

> **setTrendDuration**: (`duration`) => `void`

#### Parameters

##### duration

[`TrendDuration`](../../types/type-aliases/TrendDuration.md)

#### Returns

`void`

### setTrendTimeFrame()

> **setTrendTimeFrame**: (`timeFrame`) => `void`

#### Parameters

##### timeFrame

[`TrendTimeFrame`](../../types/type-aliases/TrendTimeFrame.md)

#### Returns

`void`

### setTrendTimeRange()

> **setTrendTimeRange**: (`range`) => `void`

#### Parameters

##### range

[`TrendTimeRange`](../../types/type-aliases/TrendTimeRange.md)

#### Returns

`void`

### setTrendViewMode()

> **setTrendViewMode**: (`viewMode`) => `void`

#### Parameters

##### viewMode

[`TrendViewMode`](../../types/type-aliases/TrendViewMode.md)

#### Returns

`void`

### snapshotCharts

> **snapshotCharts**: `Readable`\<[`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]\>

### stats

> **stats**: `Readable`\<[`LiquidationStats`](../../types/interfaces/LiquidationStats.md)\>

### subscribe()

> **subscribe**: (`this`, `run`, `invalidate?`) => `Unsubscriber`

Subscribe on value changes.

#### Parameters

##### this

`void`

##### run

`Subscriber`\<\{ `chartConfigs`: [`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]; `isLoading`: `boolean`; `snapshotData`: [`LiquidationData`](../../types/interfaces/LiquidationData.md)[]; `stats`: [`LiquidationStats`](../../types/interfaces/LiquidationStats.md); `timeFilters`: [`TimeFilters`](../../types/interfaces/TimeFilters.md); `trendData`: [`LiquidationData`](../../types/interfaces/LiquidationData.md)[]; \}\>

subscription callback

##### invalidate?

() => `void`

cleanup callback

#### Returns

`Unsubscriber`

### timeFilters

> **timeFilters**: `Readable`\<[`TimeFilters`](../../types/interfaces/TimeFilters.md)\>

### toggleChartVisibility()

> **toggleChartVisibility**: (`chartId`) => `void`

#### Parameters

##### chartId

`string`

#### Returns

`void`

### trendCharts

> **trendCharts**: `Readable`\<[`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]\>

### updateSnapshotData()

> **updateSnapshotData**: (`data`) => `void`

#### Parameters

##### data

[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]

#### Returns

`void`

### updateStats()

> **updateStats**: (`stats`) => `void`

#### Parameters

##### stats

[`LiquidationStats`](../../types/interfaces/LiquidationStats.md)

#### Returns

`void`

### updateTrendData()

> **updateTrendData**: (`data`) => `void`

#### Parameters

##### data

[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]

#### Returns

`void`
