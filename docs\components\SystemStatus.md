# SystemStatus 组件文档

## 📋 概述

SystemStatus 组件是一个高级的系统状态监控组件，集成了强大的网络监控功能。该组件使用 Svelte 5 语法重构，提供实时的网络状态、质量和连接信息展示。

## 🎯 功能特性

### 1. 网络状态监控
- **实时检测**：自动检测在线/离线状态变化
- **质量评估**：提供优秀/良好/较差/离线四级质量指示
- **延迟测量**：实时测量网络延迟（毫秒）
- **连接类型识别**：显示连接类型（4G/WiFi/以太网等）
- **下载速度监控**：检测网络下载速度（Mbps）

### 2. 用户界面
- **视觉指示器**：彩色圆点和图标表示网络状态
- **动画效果**：脉冲动画增强视觉反馈
- **详细 Tooltip**：悬停显示完整的网络状态详情
- **响应式设计**：适配不同屏幕尺寸

### 3. 技术特性
- **Svelte 5 语法**：使用 $state、$effect、$derived 等现代语法
- **生命周期管理**：自动订阅和清理网络状态
- **类型安全**：完整的 TypeScript 类型定义
- **shadcn-svelte 集成**：使用 Badge 和 Tooltip 组件

## 🏗️ 技术架构

### 核心依赖
```typescript
import { networkStatus, networkQuality, isOnline } from '$lib/utils/networkMonitor';
import type { NetworkStatus } from '$lib/utils/networkMonitor';
import { Badge } from '$lib/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '$lib/components/ui/tooltip';
```

### 状态管理
```typescript
// Svelte 5 响应式状态
let currentTime = $state(new Date());

// 订阅网络状态
const status = $derived($networkStatus);
const quality = $derived($networkQuality);
const online = $derived($isOnline);
```

### 生命周期管理
```typescript
$effect(() => {
  // 启动时间更新定时器
  updateInterval = setInterval(() => {
    currentTime = new Date();
  }, 1000);

  // 清理函数
  return () => {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
  };
});
```

## 🎨 UI 设计

### 网络状态指示器
- **绿色圆点 + 🟢**：优秀网络质量
- **蓝色圆点 + 🔵**：良好网络质量  
- **黄色圆点 + 🟡**：较差网络质量
- **红色圆点 + 🔴**：离线状态

### Badge 变体
- **default**：在线且网络质量良好/优秀
- **secondary**：在线但网络质量较差
- **destructive**：离线状态

### Tooltip 详情
显示完整的网络状态信息：
- 连接状态：正常/断开
- 网络质量：优秀/良好/较差/离线
- 延迟：具体毫秒数
- 连接类型：4G/WiFi/以太网等
- 下载速度：Mbps
- 最后检测时间

## 📝 使用指南

### 基本使用
```svelte
<script>
  import { SystemStatus } from '$lib/components/features/system';
</script>

<SystemStatus />
```

### 在侧边栏中使用
```svelte
<!-- AppSidebar.svelte -->
<Sidebar.Footer>
  <div class="group-data-[collapsible=icon]:hidden px-3 py-2">
    <SystemStatus />
  </div>
</Sidebar.Footer>
```

### 在错误恢复界面中使用
```svelte
<!-- ErrorRecoveryHelper.svelte -->
<CardContent class="space-y-4">
  <!-- 网络状态 -->
  <SystemStatus />
  
  <!-- 其他恢复策略 -->
  <!-- ... -->
</CardContent>
```

## 🔧 配置选项

### networkMonitor 配置
SystemStatus 组件依赖全局的 networkMonitor 实例，可以通过以下方式配置：

```typescript
import { networkMonitor } from '$lib/utils/networkMonitor';

// 更新配置
networkMonitor.updateConfig({
  enabled: true,
  checkInterval: 30000, // 30秒检测间隔
  latencyTestUrl: '/api/health',
  showNotifications: true
});
```

## 🧪 测试验证

### 功能测试清单
- [ ] 网络状态正确显示（在线/离线）
- [ ] 网络质量评估准确
- [ ] 延迟信息实时更新
- [ ] 连接类型正确识别
- [ ] Tooltip 详情完整显示
- [ ] 时间显示实时更新
- [ ] 组件生命周期正常
- [ ] 响应式布局适配

### 测试场景
1. **网络状态变化**：断开/连接网络验证状态更新
2. **质量检测**：不同网络环境下的质量评估
3. **页面导航**：跨页面状态保持
4. **长时间运行**：内存泄漏和性能测试

## 🔄 重构历程

### 重构前（Svelte 4）
- 使用 onMount/onDestroy 生命周期钩子
- 简单的 navigator.onLine 检测
- 基础的在线/离线状态显示
- 手动事件监听管理

### 重构后（Svelte 5 + networkMonitor）
- 使用 $state/$effect/$derived 语法
- 集成完整的网络监控系统
- 丰富的网络质量和连接信息
- 自动化的状态管理和清理

### 重构优势
1. **功能增强**：从简单状态检测到完整网络监控
2. **现代语法**：使用 Svelte 5 最新特性
3. **更好的 UX**：丰富的视觉反馈和详细信息
4. **代码质量**：更清晰的状态管理和生命周期
5. **可维护性**：模块化设计和类型安全

## 📚 相关文档

- [网络监控工具文档](../utils/networkMonitor.md)
- [Svelte 5 语法指南](https://svelte.dev/docs/svelte/overview)
- [shadcn-svelte 组件库](https://www.shadcn-svelte.com/)
- [项目开发指南](../../.augment-guidelines)
