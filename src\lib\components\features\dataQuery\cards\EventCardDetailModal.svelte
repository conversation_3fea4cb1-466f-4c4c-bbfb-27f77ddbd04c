<script lang="ts">
  import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON>eader, SheetTitle } from '$lib/components/ui/sheet';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import type { MarketEvent } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';

  // 本地实现 getEventTypeLabel 函数
  function getEventTypeLabel(eventType: string): string {
    return MARKET_EVENT_TYPE_LABELS[eventType as keyof typeof MARKET_EVENT_TYPE_LABELS] || eventType;
  }
  import { EventCardFactory } from './index';
  import { getEventDisplayName, getEventTypeBadgeVariant } from './utils';

  interface Props {
    open: boolean;
    event: MarketEvent | null;
    onClose: () => void;
  }

  const { open, event, onClose }: Props = $props();

  // 获取事件类型标签和变体
  const eventTypeLabel = $derived(event ? getEventTypeLabel(event.marketEventType) : '');
  const badgeVariant = $derived(event ? getEventTypeBadgeVariant(event.marketEventType) : 'outline');
  const displayName = $derived(event ? getEventDisplayName(event) : '');

  // 处理复制事件ID
  function handleCopyId() {
    if (event && navigator.clipboard) {
      navigator.clipboard.writeText(event.id);
      // 这里可以添加一个toast通知
    }
  }

  // 处理导出事件数据
  function handleExport() {
    if (!event) return;
    
    const dataStr = JSON.stringify(event, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `event-${event.id}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
</script>

<Sheet {open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <SheetContent side="right" class="w-full sm:max-w-4xl overflow-y-auto">
    {#if event}
      <SheetHeader>
        <SheetTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <span class="text-xl font-semibold">{displayName}</span>
            <Badge variant={badgeVariant} class="text-sm">
              {eventTypeLabel}
            </Badge>
          </div>
          
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onclick={handleCopyId}
              class="text-xs"
            >
              复制ID
            </Button>
            <Button
              variant="outline"
              size="sm"
              onclick={handleExport}
              class="text-xs"
            >
              导出数据
            </Button>
          </div>
        </SheetTitle>
      </SheetHeader>

      <div class="space-y-6">
        <!-- 详细卡片展示 -->
        <div class="border rounded-lg p-1">
          <EventCardFactory
            {event}
            size="lg"
            variant="detailed"
            showActions={false}
            useSpecificCard={true}
          />
        </div>

        <!-- 原始数据展示 -->
        <div class="space-y-3">
          <h3 class="text-lg font-medium">原始数据</h3>
          <div class="bg-muted rounded-lg p-4">
            <div class="space-y-2">
              <!-- 基本信息 -->
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-muted-foreground">事件ID:</span>
                  <span class="font-mono ml-2">{event.id}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">事件类型:</span>
                  <span class="ml-2">{event.marketEventType}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">时间戳:</span>
                  <span class="font-mono ml-2">{event.date}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">货币ID:</span>
                  <span class="font-mono ml-2">{event.currency}</span>
                </div>
              </div>

              <!-- 参数列表 -->
              <div class="mt-4">
                <h4 class="text-sm font-medium mb-2">参数详情:</h4>
                <div class="bg-background rounded border p-3 max-h-60 overflow-y-auto">
                  <div class="space-y-1">
                    {#each event.parameters as param}
                      <div class="flex justify-between items-center text-xs">
                        <span class="text-muted-foreground font-mono">{param.parameterId}:</span>
                        <span class="font-mono text-right max-w-xs truncate" title={String(param.value)}>
                          {param.value}
                        </span>
                      </div>
                    {/each}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3 pt-4 border-t">
          <Button variant="outline" onclick={onClose}>
            关闭
          </Button>
        </div>
      </div>
    {/if}
  </SheetContent>
</Sheet>
