<!-- src/lib/components/charts/BaseChart/BaseChart.svelte -->
<script lang="ts">
  import type { EChartsOption } from 'echarts';
  import * as echarts from 'echarts';
  import { createEventDispatcher, onDestroy, onMount } from 'svelte';

  import { createThemeObserver,getChartColorMap } from '$lib/utils/chartColors';
  import { chartUpdateManager } from '$lib/utils/chartUpdateManager';
  import { createModuleLogger } from '$lib/utils/logger';

  // 泛型类型定义
  type ChartData = any;
  type ChartType = 'line' | 'bar' | 'pie' | 'scatter' | 'area';

  // Props
  export let chartType: ChartType;
  export let data: ChartData = [];
  export let options: EChartsOption = {};
  export let title: string = '';
  export let subtitle: string = '';
  export let height: string = '100%';
  export let width: string = '100%';
  export let enableLazyLoad: boolean = true;
  export let enableResize: boolean = true;
  export let enableThemeWatch: boolean = true;
  export let debounceDelay: number = 100;
  export let ariaLabel: string = '';

  // 事件分发器
  const dispatch = createEventDispatcher<{
    chartClick: { chartType: ChartType; name: string; value: any; [key: string]: any };
    chartReady: { chart: echarts.ECharts };
    chartError: { error: Error };
    chartUpdate: { duration: number };
  }>();

  // 内部状态
  let chartDom: HTMLDivElement;
  let myChart: echarts.ECharts | null = null;
  let chartInitialized = false;
  let themeUnsubscribe: (() => void) | null = null;
  let resizeObserver: ResizeObserver | null = null;
  let intersectionObserver: IntersectionObserver | null = null;
  let lastDataHash = '';

  // Logger
  const chartLogger = createModuleLogger(`chart-${chartType}`);

  /**
   * 生成数据哈希用于变化检测
   */
  function generateDataHash(data: any, options: any, title: string, subtitle: string): string {
    const hashData = {
      data: JSON.stringify(data),
      options: JSON.stringify(options),
      title,
      subtitle,
      timestamp: Date.now(),
    };
    return btoa(JSON.stringify(hashData)).slice(0, 16);
  }

  /**
   * 初始化图表
   */
  function initializeChart(): void {
    if (chartInitialized || !chartDom) {
      return;
    }

    try {
      myChart = echarts.init(chartDom);
      chartInitialized = true;

      // 设置点击事件监听
      myChart.on('click', (params: any) => {
        dispatch('chartClick', {
          chartType,
          name: params.name || params.seriesName,
          value: params.value,
          ...params,
        });
      });

      // 初始更新
      updateChart();

      // 分发就绪事件
      dispatch('chartReady', { chart: myChart });

      chartLogger.debug('Chart initialized', { chartType, title });
    } catch (error) {
      const chartError = error instanceof Error ? error : new Error(String(error));
      chartLogger.error('Chart initialization failed', {
        chartType,
        error: chartError.message,
      });
      dispatch('chartError', { error: chartError });
    }
  }

  /**
   * 更新图表 - 由子组件实现
   */
  function updateChart(): void {
    if (!myChart) {
      return;
    }

    const startTime = performance.now();

    try {
      // 获取颜色映射
      const colorMap = getChartColorMap();

      // 构建基础配置
      const baseConfig: EChartsOption = {
        title: {
          text: title,
          subtext: subtitle,
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          bottom: '0%',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '15%',
          containLabel: true,
        },
        ...options,
      };

      // 分发更新事件给父组件处理具体的图表配置
      const updateEvent = new CustomEvent('chartUpdate', {
        detail: {
          chart: myChart,
          baseConfig,
          colorMap,
          data,
        },
      });

      chartDom.dispatchEvent(updateEvent);

      const duration = performance.now() - startTime;
      dispatch('chartUpdate', { duration });

      chartLogger.debug('Chart updated', {
        chartType,
        duration: Math.round(duration),
      });
    } catch (error) {
      const chartError = error instanceof Error ? error : new Error(String(error));
      chartLogger.error('Chart update failed', {
        chartType,
        error: chartError.message,
      });
      dispatch('chartError', { error: chartError });
    }
  }

  /**
   * 调整图表大小
   */
  function resizeChart(): void {
    if (myChart) {
      myChart.resize();
    }
  }

  /**
   * 销毁图表
   */
  function destroyChart(): void {
    if (myChart) {
      myChart.dispose();
      myChart = null;
    }
    chartInitialized = false;
  }

  // 响应式更新
  $: {
    if (myChart && chartInitialized) {
      const currentHash = generateDataHash(data, options, title, subtitle);

      if (currentHash !== lastDataHash) {
        chartUpdateManager.scheduleUpdate(`${chartType}-${title}`, updateChart, 0, debounceDelay);
        lastDataHash = currentHash;
      }
    }
  }

  // 生命周期管理
  onMount(() => {
    // 懒加载支持
    if (enableLazyLoad) {
      intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              initializeChart();
              intersectionObserver?.unobserve(chartDom);
            }
          });
        },
        { threshold: 0.1 }
      );
      intersectionObserver.observe(chartDom);
    } else {
      // 直接初始化
      initializeChart();
    }

    // 窗口大小变化监听
    if (enableResize) {
      window.addEventListener('resize', resizeChart);

      // 使用 ResizeObserver 监听容器大小变化
      if (typeof ResizeObserver !== 'undefined') {
        resizeObserver = new ResizeObserver(() => {
          resizeChart();
        });
        resizeObserver.observe(chartDom);
      }
    }

    // 主题变化监听
    if (enableThemeWatch) {
      themeUnsubscribe = createThemeObserver(() => {
        if (myChart && chartInitialized) {
          updateChart();
        }
      });
    }

    chartLogger.debug('Chart component mounted', { chartType, title });
  });

  onDestroy(() => {
    // 清理事件监听器
    if (enableResize) {
      window.removeEventListener('resize', resizeChart);
    }

    // 清理观察器
    if (intersectionObserver) {
      intersectionObserver.disconnect();
    }

    if (resizeObserver) {
      resizeObserver.disconnect();
    }

    // 清理主题监听
    if (themeUnsubscribe) {
      themeUnsubscribe();
    }

    // 取消待处理的更新
    chartUpdateManager.cancelUpdate(`${chartType}-${title}`);

    // 销毁图表
    destroyChart();

    chartLogger.debug('Chart component destroyed', { chartType, title });
  });

  // 导出方法供外部调用
  export function getChart(): echarts.ECharts | null {
    return myChart;
  }

  export function forceUpdate(): void {
    if (myChart && chartInitialized) {
      updateChart();
    }
  }

  export function resize(): void {
    resizeChart();
  }
</script>

<div
  bind:this={chartDom}
  class="chart-container"
  style="height: {height}; width: {width};"
  aria-label={ariaLabel || `${title} ${chartType} chart`}
  role="img"
>
  <slot {myChart} {chartInitialized} {updateChart} />
</div>

<style>
  .chart-container {
    position: relative;
    overflow: hidden;
  }
</style>
