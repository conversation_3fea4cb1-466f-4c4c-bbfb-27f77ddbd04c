<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Label } from '$lib/components/ui/label';
  import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';
  import { CoinMultiSelect } from '$lib/components/features/coin-multi-select';
  import { TimeRangeSelector } from '$lib/components/features/dashboard';
  import { createModuleLogger } from '$lib/utils/logger';
  import type { HistoricalQueryParams, HistoricalQueryFormState, MarketEventType } from '$lib/types';

  const historicalFormLogger = createModuleLogger('historical-query-form');
  const dispatch = createEventDispatcher<{
    query: HistoricalQueryParams;
    reset: void;
  }>();

  // 数据类型选项列表（使用中文标签）
  const typeOptions: { value: MarketEventType; label: string }[] = [
    { value: 'TWAP_DETECTION', label: 'TWAP' },
    { value: 'LIQUIDATION_ORDER', label: '清算订单' },
    { value: 'EXCHANGE_TRANSFER', label: '交易所转账' },
    { value: 'VOLUME_INCREASE', label: '交易量异动' },
  ];

  // 组件属性
  export let loading = false;
  export let initialParams: Partial<HistoricalQueryParams> = {};

  // 生成默认日期范围（最近7天）
  const getDefaultDateRange = () => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 7);

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    };
  };

  const defaultDates = getDefaultDateRange();

  // 表单状态
  let formState: HistoricalQueryFormState = {
    selectedCoins: initialParams.selectedCoins || ['BTC'],
    startDate: initialParams.startDate || defaultDates.startDate,
    endDate: initialParams.endDate || defaultDates.endDate,
    selectedDataTypes: initialParams.dataTypes || 'LIQUIDATION_ORDER',
    isQuerying: false,
    error: null,
    validationErrors: {},
    isDirty: false,
  };

  // 时间范围选择器状态
  let selectedTimeRange: string = 'custom';
  let customStartTime = formState.startDate ? `${formState.startDate}T00:00` : `${defaultDates.startDate}T00:00`;
  let customEndTime = formState.endDate ? `${formState.endDate}T23:59` : `${defaultDates.endDate}T23:59`;
  let selectedTimeZone = 'UTC';

  // 数据类型选择器状态（用于 Select 组件的字符串值）
  let selectedDataTypeValue = formState.selectedDataTypes || 'LIQUIDATION_ORDER';

  // 响应式更新表单状态
  $: {
    formState.isQuerying = loading;
  }

  // 同步数据类型选择状态
  $: {
    if (formState.selectedDataTypes !== selectedDataTypeValue) {
      selectedDataTypeValue = formState.selectedDataTypes || 'LIQUIDATION_ORDER';
    }
  }

  // 处理币种选择变化
  function handleCoinSelectionChange(selectedCoins: string[]) {
    formState.selectedCoins = selectedCoins;
    clearValidationError('selectedCoins');
    historicalFormLogger.debug('Coin selection changed', { selectedCoins });
  }

  // 处理币种添加
  function handleCoinAdd(coin: any) {
    historicalFormLogger.debug('Coin added', { coin });
  }

  // 处理币种移除
  function handleCoinRemove(coinSymbol: string) {
    historicalFormLogger.debug('Coin removed', { coinSymbol });
  }

  // 处理数据类型选择变化
  function handleDataTypeSelectionChange(selectedType: string) {
    formState.selectedDataTypes = selectedType as MarketEventType;
    clearValidationError('selectedDataTypes');
    historicalFormLogger.debug('Data type selection changed', { selectedType });
  }

  // 处理时间范围变化
  function handleTimeRangeChange(params: {
    selectedTimeRange: string;
    customStartTime: string;
    customEndTime: string;
    selectedTimeZone: string;
  }) {
    selectedTimeRange = params.selectedTimeRange;
    customStartTime = params.customStartTime;
    customEndTime = params.customEndTime;
    selectedTimeZone = params.selectedTimeZone;

    // 更新表单状态中的日期
    if (params.customStartTime) {
      formState.startDate = params.customStartTime.split('T')[0];
    }
    if (params.customEndTime) {
      formState.endDate = params.customEndTime.split('T')[0];
    }

    clearValidationError('startDate');
    clearValidationError('endDate');

    historicalFormLogger.debug('Time range changed', {
      selectedTimeRange,
      startDate: formState.startDate,
      endDate: formState.endDate,
      selectedTimeZone,
    });
  }

  // 验证表单
  function validateForm(): boolean {
    const errors: Record<string, string> = {};

    if (formState.selectedCoins.length === 0) {
      errors.selectedCoins = '请至少选择一个币种';
    }

    if (!formState.startDate) {
      errors.startDate = '请选择开始日期';
    }

    if (!formState.endDate) {
      errors.endDate = '请选择结束日期';
    }

    if (formState.startDate && formState.endDate) {
      const startDate = new Date(formState.startDate);
      const endDate = new Date(formState.endDate);
      
      if (startDate > endDate) {
        errors.dateRange = '开始日期不能晚于结束日期';
      }

      // 检查日期范围是否过大（不超过1年）
      const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        errors.dateRange = '查询日期范围不能超过1年';
      }
    }

    if (!formState.selectedDataTypes) {
      errors.selectedDataTypes = '请选择一种数据类型';
    }

    formState.validationErrors = errors;
    return Object.keys(errors).length === 0;
  }

  // 清除验证错误
  function clearValidationError(field: string) {
    if (formState.validationErrors[field]) {
      const { [field]: removed, ...rest } = formState.validationErrors;
      formState.validationErrors = rest;
    }
  }

  // 处理查询提交
  function handleQuery() {
    historicalFormLogger.info('Submitting historical query', { formState });

    if (!validateForm()) {
      historicalFormLogger.warn('Form validation failed', { errors: formState.validationErrors });
      return;
    }

    const queryParams: HistoricalQueryParams = {
      selectedCoins: formState.selectedCoins,
      startDate: formState.startDate,
      endDate: formState.endDate,
      dataTypes: formState.selectedDataTypes!,
      sortBy: 'date',
      sortOrder: 'desc',
    };

    dispatch('query', queryParams);
  }

  // 处理重置表单
  function handleReset() {
    historicalFormLogger.info('Resetting historical query form');

    formState = {
      selectedCoins: [],
      startDate: '',
      endDate: '',
      selectedDataTypes: null,
      isQuerying: false,
      error: null,
      validationErrors: {},
      isDirty: false,
    };

    selectedTimeRange = 'custom';
    customStartTime = '';
    customEndTime = '';
    selectedTimeZone = 'UTC';
    selectedDataTypeValue = 'LIQUIDATION_ORDER';

    dispatch('reset');
  }

  // 获取表单摘要信息
  function getFormSummary(): string {
    const parts: string[] = [];

    if (formState.selectedCoins.length > 0) {
      parts.push(`币种: ${formState.selectedCoins.join(', ')}`);
    }

    if (formState.startDate && formState.endDate) {
      parts.push(`时间: ${formState.startDate} 至 ${formState.endDate}`);
    }

    if (formState.selectedDataTypes) {
      const selectedOption = typeOptions.find(option => option.value === formState.selectedDataTypes);
      parts.push(`类型: ${selectedOption?.label || formState.selectedDataTypes}`);
    }

    return parts.length > 0 ? parts.join(' | ') : '未设置查询条件';
  }
</script>

<Card class="mb-6">
  <CardHeader class="pb-4">
    <CardTitle class="text-xl font-semibold">历史数据查询条件</CardTitle>
  </CardHeader>
  <CardContent class="pt-0">
    <div class="space-y-6">
      <!-- 查询条件表单 -->
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        <!-- 币种多选框 -->
        <div class="space-y-2">
          <Label for="coins">币种选择 *</Label>
          <CoinMultiSelect
            selectedCoins={formState.selectedCoins}
            placeholder="请选择币种（可多选）"
            maxSelections={10}
            onSelectionChange={handleCoinSelectionChange}
            onCoinAdd={handleCoinAdd}
            onCoinRemove={handleCoinRemove}
          />
          {#if formState.validationErrors.selectedCoins}
            <p class="text-sm text-destructive">{formState.validationErrors.selectedCoins}</p>
          {/if}
        </div>

        <!-- 时间范围选择器 -->
        <div class="space-y-2">
          <Label for="dateRange">时间范围 *</Label>
          <TimeRangeSelector
            {selectedTimeRange}
            {customStartTime}
            {customEndTime}
            {selectedTimeZone}
            onTimeRangeChange={handleTimeRangeChange}
          />
          {#if formState.validationErrors.startDate || formState.validationErrors.endDate || formState.validationErrors.dateRange}
            <p class="text-sm text-destructive">
              {formState.validationErrors.startDate || formState.validationErrors.endDate || formState.validationErrors.dateRange}
            </p>
          {/if}
        </div>

        <!-- 数据类型单选框 -->
        <div class="space-y-2">
          <Label for="dataTypes">数据类型 *</Label>
          <Select
            type="single"
            bind:value={selectedDataTypeValue}
            onValueChange={handleDataTypeSelectionChange}
          >
            <SelectTrigger class="w-full flex-shrink-0 text-sm">
              {typeOptions.find((option) => option.value === selectedDataTypeValue)?.label ||
                '请选择数据类型'}
            </SelectTrigger>
            <SelectContent>
              {#each typeOptions as option (option.value)}
                <SelectItem value={option.value} label={option.label}>
                  {option.label}
                </SelectItem>
              {/each}
            </SelectContent>
          </Select>
          {#if formState.validationErrors.selectedDataTypes}
            <p class="text-sm text-destructive">{formState.validationErrors.selectedDataTypes}</p>
          {/if}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Button 
            onclick={handleQuery} 
            disabled={loading}
            class="min-w-[100px]"
          >
            {loading ? '查询中...' : '开始查询'}
          </Button>
          <Button 
            variant="outline" 
            onclick={handleReset}
            disabled={loading}
          >
            重置条件
          </Button>
        </div>

        <!-- 查询条件摘要 -->
        <div class="text-sm text-muted-foreground">
          {getFormSummary()}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
