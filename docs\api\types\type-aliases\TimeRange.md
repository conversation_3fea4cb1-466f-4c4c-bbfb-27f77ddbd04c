[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / TimeRange

# Type Alias: TimeRange

> **TimeRange** = `"today"` \| `"yesterday"` \| `"week"` \| `"month"` \| `"last24h"` \| `"lastWeek"` \| `"lastMonth"` \| `"custom"`

Defined in: src/lib/types/dashboard.ts:186

时间范围选项

定义仪表板支持的时间范围类型

## Example

```typescript
const timeRange: TimeRange = 'month';

// 根据时间范围获取数据
if (timeRange === 'custom') {
  // 使用自定义时间范围
}
```
