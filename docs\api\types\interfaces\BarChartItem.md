[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / BarChartItem

# Interface: BarChartItem

Defined in: src/lib/types/dashboard.ts:20

柱状图数据项

## Example

```typescript
const barData: BarChartItem = {
  name: '一月',
  value: 1200,
};
```

## Properties

### name

> **name**: `string`

Defined in: src/lib/types/dashboard.ts:22

数据项名称，通常用作 X 轴标签

---

### value

> **value**: `number`

Defined in: src/lib/types/dashboard.ts:24

数据值，用作柱状图的高度
