[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / BaseQueryParams

# Interface: BaseQueryParams

Defined in: src/lib/types/api.ts:46

查询参数基础接口

## Properties

### endDate?

> `optional` **endDate**: `string`

Defined in: src/lib/types/api.ts:48

---

### filters?

> `optional` **filters**: `Record`\<`string`, `any`\>

Defined in: src/lib/types/api.ts:49

---

### startDate?

> `optional` **startDate**: `string`

Defined in: src/lib/types/api.ts:47
