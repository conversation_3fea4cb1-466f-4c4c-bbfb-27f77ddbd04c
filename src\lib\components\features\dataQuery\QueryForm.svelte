<script lang="ts">
  // shadcn-svelte 组件导入
  import { Button } from '$lib/components/ui/button';

  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Label } from '$lib/components/ui/label';
  import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';

  import { TimeRangeSelector } from '$lib/components/features/dashboard';
  import { dataQueryStore } from '$lib/stores/features/dataQuery';
  import type { CoinOption, MarketEventType } from '$lib/types';


  import { CoinMultiSelect } from '../coin-multi-select';
  import { TypeMultiSelect } from '../type-multi-select';

  interface Props {
    expanded: boolean;
  }

  const { expanded }: Props = $props();

  // 切换筛选区域展开/收起
  function toggleExpanded() {
    dataQueryStore.toggleFilterExpanded();
  }

  // 执行查询
  function submitQuery() {
    dataQueryStore.executeQuery();
  }

  // 重置表单
  function handleReset() {
    dataQueryStore.reset();
  }

  // 处理币种选择变化
  function handleCoinSelectionChange(selectedCoins: string[]) {
    dataQueryStore.setSelectedCoins(selectedCoins);
  }

  // 处理添加币种
  function handleCoinAdd(option: CoinOption) {
    dataQueryStore.addSelectedCoin(option.value);
  }

  // 处理移除币种
  function handleCoinRemove(coinSymbol: string) {
    dataQueryStore.removeSelectedCoin(coinSymbol);
  }

  // 处理移除类型
  function handleTypeRemove(typeValue: string) {
    const newTypes = selectedType.filter(type => type !== typeValue);
    selectedType = newTypes;
  }

  // 获取类型的首字母缩写
  function getTypeAbbreviation(type: string): string {
    return type.split('_').map(word => word.charAt(0)).join('');
  }

  // 处理时间范围变化
  function handleTimeRangeChange(params: {
    selectedTimeRange: string;
    customStartTime: string;
    customEndTime: string;
    selectedTimeZone: string;
  }) {
    // 将时间范围转换为日期格式
    const startDate = params.customStartTime ? params.customStartTime.split('T')[0] : '';
    const endDate = params.customEndTime ? params.customEndTime.split('T')[0] : '';
    dataQueryStore.setDateRange(startDate, endDate);
  }

  // 响应式变量用于 Select 组件
  let selectedStatus = $state($dataQueryStore.queryParams.status);
  let selectedType = $state($dataQueryStore.queryParams.type as MarketEventType[]);

  // 计算可显示的 Badge 数量（基于实际容器宽度）
  let containerRef = $state<HTMLDivElement | undefined>(undefined);
  let visibleBadgeCount = $state(0);
  let hiddenCount = $derived(Math.max(0, selectedType.length - visibleBadgeCount));

  // 计算可见的 Badge 数量
  function calculateVisibleBadges() {
    if (!containerRef || selectedType.length === 0) {
      visibleBadgeCount = selectedType.length;
      return;
    }

    const containerWidth = containerRef.offsetWidth;
    const containerPadding = 24; // px-3 = 12px * 2
    const selectTriggerMinWidth = 120; // 选择器最小宽度
    const badgeGap = 4; // gap-1 = 4px
    const hiddenBadgeWidth = 40; // "+n" badge 大概宽度

    let availableWidth = containerWidth - containerPadding - selectTriggerMinWidth;
    let currentWidth = 0;
    let count = 0;

    // 遍历每个 badge，计算能放下多少个
    for (let i = 0; i < selectedType.length; i++) {
      const abbreviation = getTypeAbbreviation(selectedType[i]);
      // 估算 badge 宽度：文字宽度 + padding + X按钮
      const estimatedBadgeWidth = abbreviation.length * 8 + 32 + 16; // 粗略估算

      if (currentWidth + estimatedBadgeWidth + badgeGap <= availableWidth) {
        currentWidth += estimatedBadgeWidth + badgeGap;
        count++;
      } else {
        // 如果还有剩余项，需要为 "+n" badge 预留空间
        if (i < selectedType.length - 1) {
          if (currentWidth + hiddenBadgeWidth <= availableWidth) {
            // 可以放下 "+n" badge，但需要减少一个普通 badge
            count = Math.max(0, count - 1);
          }
        }
        break;
      }
    }

    visibleBadgeCount = count;
  }

  // 监听容器大小变化和选中项变化
  $effect(() => {
    if (containerRef) {
      calculateVisibleBadges();
    }
  });

  // 监听窗口大小变化
  $effect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => calculateVisibleBadges();
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  });

  // 监听状态变化并更新 store
  $effect(() => {
    if (selectedStatus !== $dataQueryStore.queryParams.status) {
      dataQueryStore.setStatus(selectedStatus);
    }
  });

  $effect(() => {
    if (selectedType !== $dataQueryStore.queryParams.type) {
      dataQueryStore.setType(selectedType);
    }
  });

  // 同步 store 变化到本地状态
  $effect(() => {
    selectedStatus = $dataQueryStore.queryParams.status;
    selectedType = $dataQueryStore.queryParams.type;
  });
</script>

<!-- 可折叠的高级查询筛选区域 -->
<Card class="mb-6">
  <CardHeader class="pb-4">
    <div class="flex items-center justify-between">
      <CardTitle class="text-xl font-semibold">高级查询筛选</CardTitle>
      <Button variant="ghost" size="sm" onclick={toggleExpanded}>
        {expanded ? '一键收起' : '一键展开'}
      </Button>
    </div>
  </CardHeader>
  {#if expanded}
    <CardContent class="pt-0">
      <div class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <!-- 币种多选框 -->
        <div class="flex items-center gap-3">
          <Label for="coins">币种选择</Label>
          <CoinMultiSelect
            selectedCoins={$dataQueryStore.queryParams.selectedCoins}
            placeholder="请选择币种（可多选）"
            maxSelections={5}
            onSelectionChange={handleCoinSelectionChange}
            onCoinAdd={handleCoinAdd}
            onCoinRemove={handleCoinRemove}
          />
        </div>
        <!-- 日期范围选择器 -->
        <div class="flex items-center gap-3">
          <Label for="dateRange">日期范围</Label>
          <TimeRangeSelector
            selectedTimeRange={$dataQueryStore.queryParams.startDate && $dataQueryStore.queryParams.endDate ? "custom" : "today"}
            customStartTime={$dataQueryStore.queryParams.startDate ? `${$dataQueryStore.queryParams.startDate}T00:00` : ''}
            customEndTime={$dataQueryStore.queryParams.endDate ? `${$dataQueryStore.queryParams.endDate}T23:59` : ''}
            onTimeRangeChange={handleTimeRangeChange}
          />
        </div>
        <!-- 状态筛选下拉框 -->
        <div class="flex items-center gap-3">
          <Label for="status">状态</Label>
          <Select type="single" bind:value={selectedStatus}>
            <SelectTrigger id="status">
              {selectedStatus}
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="全部">全部</SelectItem>
              <SelectItem value="有效">有效</SelectItem>
              <SelectItem value="无效">无效</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <!-- 类型筛选下拉框 -->
        <div class="flex items-center gap-3">
          <Label for="type" class="flex-shrink-0">类型</Label>
          <div class="flex-1" bind:this={containerRef}>
            <TypeMultiSelect
              selectedTypes={selectedType}
              placeholder="请选择类型"
              visibleBadgeCount={visibleBadgeCount}
              hiddenCount={hiddenCount}
              onSelectionChange={(types) => {
                selectedType = types as MarketEventType[];
                dataQueryStore.setType(types as MarketEventType[]);
              }}
              onTypeRemove={handleTypeRemove}
            />
          </div>
        </div>
      </div>
      <div class="flex justify-end gap-3">
        <Button variant="outline" onclick={handleReset} size="sm">重置</Button>
        <Button onclick={submitQuery} size="sm">查询</Button>
      </div>
    </CardContent>
  {/if}
</Card>
