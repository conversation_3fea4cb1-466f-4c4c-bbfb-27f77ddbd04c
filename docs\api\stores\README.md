[**Svelte Demo API Documentation**](../README.md)

---

[Svelte Demo API Documentation](../README.md) / stores

# stores

## Interfaces

- [Tab](interfaces/Tab.md)

## Variables

- [activeTabId](variables/activeTabId.md)
- [dashboardStore](variables/dashboardStore.md)
- [dataQueryStore](variables/dataQueryStore.md)
- [isSidebarOpen](variables/isSidebarOpen.md)
- [legacyDashboardStore](variables/legacyDashboardStore.md)
- [legacyLiquidationStore](variables/legacyLiquidationStore.md)
- [liquidationStore](variables/liquidationStore.md)
- [tabs](variables/tabs.md)

## Functions

- [closeTab](functions/closeTab.md)
- [openTab](functions/openTab.md)
- [setActiveTab](functions/setActiveTab.md)
- [setSidebarOpen](functions/setSidebarOpen.md)
- [toggleSidebar](functions/toggleSidebar.md)
