// src/tests/setup.ts
import '@testing-library/jest-dom';

import { vi } from 'vitest';

// Mock SvelteKit modules
vi.mock('$app/environment', () => ({
  browser: false,
  dev: true,
  building: false,
  version: 'test',
}));

vi.mock('$app/navigation', () => ({
  goto: vi.fn(),
  invalidate: vi.fn(),
  invalidateAll: vi.fn(),
  preloadData: vi.fn(),
  preloadCode: vi.fn(),
  beforeNavigate: vi.fn(),
  afterNavigate: vi.fn(),
  pushState: vi.fn(),
  replaceState: vi.fn(),
}));

vi.mock('$app/stores', async () => {
  const { readable } = await import('svelte/store');

  const page = readable({
    url: new URL('http://localhost:3000'),
    params: {},
    route: { id: null },
    status: 200,
    error: null,
    data: {},
    form: undefined,
  });

  return {
    page,
    navigating: readable(null),
    updated: readable(false),
  };
});

// Mock MSW for tests
vi.mock('$lib/mocks/browser', () => ({
  worker: {
    start: vi.fn(),
    stop: vi.fn(),
    resetHandlers: vi.fn(),
  },
}));

// Global test utilities
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: [],
  takeRecords: vi.fn(() => []),
})) as unknown as typeof IntersectionObserver;
