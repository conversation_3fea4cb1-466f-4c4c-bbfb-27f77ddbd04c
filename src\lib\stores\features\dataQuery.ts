// src/lib/stores/features/dataQuery.ts
import { derived, writable } from 'svelte/store';

import { dataQueryService } from '$lib/services/data/dataQuery';
import type { MarketEvent, MarketEventType, QueryResults } from '$lib/types';
import type { PaginationState } from '$lib/types';

// 向后兼容的查询参数类型
interface QueryParams {
  keyword: string;
  selectedCoins: string[];
  startDate: string;
  endDate: string;
  status: string;
  type: MarketEventType[];
}

// 初始查询参数
const initialQueryParams: QueryParams = {
  keyword: '',
  selectedCoins: [],
  startDate: '',
  endDate: '',
  status: '全部', // 保留用于向后兼容
  type: [], // MarketEventType数组，支持多选
};

// 初始分页设置（用于无限滚动）
const initialPagination: PaginationState = {
  currentPage: 1,
  itemsPerPage: 20, // 增加每页数量，适合无限滚动
  totalItems: 0,
  totalPages: 0,
  hasPrevious: false,
  hasNext: false,
};

/**
 * 数据查询状态管理
 */
function createDataQueryStore() {
  const { subscribe, update, set } = writable({
    queryParams: initialQueryParams,
    results: {
      items: [] as MarketEvent[], // 使用新的MarketEvent类型
      loading: false,
      pagination: initialPagination,
    } as QueryResults,
    isFilterExpanded: true,
    error: null as string | null,
    // 无限滚动相关状态（保留但可能不再使用，因为API返回所有数据）
    hasMore: false, // 默认为false，因为API返回所有数据
    loadingMore: false, // 是否正在加载更多数据
  });

  // 派生 stores
  const queryParams = derived({ subscribe }, ($state) => $state.queryParams);
  const results = derived({ subscribe }, ($state) => $state.results);
  const pagination = derived({ subscribe }, ($state) => $state.results.pagination);
  const loading = derived({ subscribe }, ($state) => $state.results.loading);
  const error = derived({ subscribe }, ($state) => $state.error);
  const isFilterExpanded = derived({ subscribe }, ($state) => $state.isFilterExpanded);
  const hasMore = derived({ subscribe }, ($state) => $state.hasMore);
  const loadingMore = derived({ subscribe }, ($state) => $state.loadingMore);

  return {
    subscribe,
    queryParams,
    results,
    pagination,
    loading,
    error,
    isFilterExpanded,
    hasMore,
    loadingMore,

    // 执行查询（获取所有符合条件的数据）
    async executeQuery() {
      let currentState: any;
      update((state) => {
        currentState = state;
        return {
          ...state,
          results: {
            ...state.results,
            loading: true,
          },
          error: null,
          hasMore: false, // API返回所有数据，无需分页
          loadingMore: false,
        };
      });

      try {
        // 传递分页参数以保持兼容性，但API会返回所有数据
        const queryData = await dataQueryService.queryData({
          ...currentState.queryParams,
          page: 1,
          pageSize: 1000, // 设置较大值确保获取所有数据
        });

        update((state) => {
          return {
            ...state,
            results: {
              items: queryData.items,
              loading: false,
              pagination: {
                ...state.results.pagination,
                currentPage: 1,
                totalItems: queryData.total,
                totalPages: 1, // 只有一页，包含所有数据
              },
            },
            hasMore: false, // 所有数据已加载
          };
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '查询失败';
        update((state) => ({
          ...state,
          results: { ...state.results, loading: false },
          error: errorMessage,
          hasMore: false,
        }));
      }
    },

    // 加载更多数据（无限滚动）
    async loadMore() {
      let currentState: any;
      update((state) => {
        currentState = state;
        return {
          ...state,
          loadingMore: true,
          error: null,
        };
      });

      try {
        const nextPage = currentState.results.pagination.currentPage + 1;
        const queryData = await dataQueryService.queryData({
          ...currentState.queryParams,
          page: nextPage,
          pageSize: currentState.results.pagination.itemsPerPage,
        });

        update((state) => {
          const totalPages = Math.ceil(queryData.total / state.results.pagination.itemsPerPage);
          const hasMore =
            queryData.items.length === state.results.pagination.itemsPerPage &&
            nextPage < totalPages;

          return {
            ...state,
            results: {
              items: [...state.results.items, ...queryData.items], // 追加新数据
              loading: false,
              pagination: {
                ...state.results.pagination,
                currentPage: nextPage,
                totalItems: queryData.total,
                totalPages,
              },
            },
            hasMore,
            loadingMore: false,
          };
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载更多数据失败';
        update((state) => ({
          ...state,
          loadingMore: false,
          error: errorMessage,
        }));
      }
    },

    // 更新查询参数
    updateQueryParams: (params: Partial<QueryParams>) => {
      update((state) => ({
        ...state,
        queryParams: { ...state.queryParams, ...params },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
        hasMore: true,
        loadingMore: false,
      }));
    },

    // 设置关键字
    setKeyword: (keyword: string) => {
      update((state) => ({
        ...state,
        queryParams: { ...state.queryParams, keyword },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 添加币种到选中列表
    addSelectedCoin: (coinSymbol: string) => {
      update((state) => {
        const selectedCoins = [...state.queryParams.selectedCoins];
        if (!selectedCoins.includes(coinSymbol)) {
          selectedCoins.push(coinSymbol);
        }

        return {
          ...state,
          queryParams: {
            ...state.queryParams,
            selectedCoins,
            keyword: selectedCoins.join(', '), // 更新显示文本
          },
          results: {
            ...state.results,
            pagination: { ...state.results.pagination, currentPage: 1 },
          },
        };
      });
    },

    // 从选中列表移除币种
    removeSelectedCoin: (coinSymbol: string) => {
      update((state) => {
        const selectedCoins = state.queryParams.selectedCoins.filter((coin: string) => coin !== coinSymbol);

        return {
          ...state,
          queryParams: {
            ...state.queryParams,
            selectedCoins,
            keyword: selectedCoins.join(', '), // 更新显示文本
          },
          results: {
            ...state.results,
            pagination: { ...state.results.pagination, currentPage: 1 },
          },
        };
      });
    },

    // 清空选中的币种
    clearSelectedCoins: () => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          selectedCoins: [],
          keyword: '',
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置选中的币种列表
    setSelectedCoins: (coins: string[]) => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          selectedCoins: coins,
          keyword: coins.join(', '),
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置日期范围
    setDateRange: (startDate: string, endDate: string) => {
      update((state) => ({
        ...state,
        queryParams: { ...state.queryParams, startDate, endDate },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置状态
    setStatus: (status: string) => {
      update((state) => ({
        ...state,
        queryParams: { ...state.queryParams, status },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置类型
    setType: (type: MarketEventType[]) => {
      update((state) => ({
        ...state,
        queryParams: { ...state.queryParams, type },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置每页项目数
    setItemsPerPage: (itemsPerPage: number) => {
      update((state) => ({
        ...state,
        results: {
          ...state.results,
          pagination: {
            ...state.results.pagination,
            itemsPerPage,
            currentPage: 1,
          },
        },
      }));
    },

    // 切换筛选区域展开/收起状态
    toggleFilterExpanded: () => {
      update((state) => ({
        ...state,
        isFilterExpanded: !state.isFilterExpanded,
      }));
    },

    // 导出数据
    async exportData() {
      let currentState: any;
      update((state) => {
        currentState = state;
        return state;
      });

      try {
        const blob = await dataQueryService.exportData(currentState.queryParams);

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `data-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '导出失败';
        update((state) => ({ ...state, error: errorMessage }));
      }
    },

    // 重置
    reset: () => {
      set({
        queryParams: initialQueryParams,
        results: {
          items: [],
          loading: false,
          pagination: initialPagination,
        },
        isFilterExpanded: true,
        error: null,
        hasMore: true,
        loadingMore: false,
      });
    },
  };
}

// 创建并导出 store 实例
export const dataQueryStore = createDataQueryStore();
