/**
 * 错误报告和分析服务
 *
 * 提供错误收集、分析和报告功能
 *
 * @category Utils
 */

import { browser } from '$app/environment';
import type { ErrorReport, ErrorStats } from '$lib/components/features/error-boundary/types';

import { createModuleLogger, type ErrorLogContext,logError } from './logger';

const reportingLogger = createModuleLogger('error-reporting');

/**
 * 错误报告配置
 */
export interface ErrorReportingConfig {
  /** 是否启用错误报告 */
  enabled: boolean;
  /** 报告端点URL */
  endpoint?: string;
  /** 采样率 (0-1) */
  sampleRate: number;
  /** 批量发送大小 */
  batchSize: number;
  /** 发送间隔（毫秒） */
  flushInterval: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 是否包含用户信息 */
  includeUserInfo: boolean;
  /** 是否包含设备信息 */
  includeDeviceInfo: boolean;
}

/**
 * 错误报告服务类
 */
export class ErrorReportingService {
  private config: ErrorReportingConfig;
  private reportQueue: ErrorReport[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private isInitialized = false;

  constructor(config: Partial<ErrorReportingConfig> = {}) {
    this.config = {
      enabled: false,
      sampleRate: 1.0,
      batchSize: 10,
      flushInterval: 30000, // 30秒
      maxRetries: 3,
      includeUserInfo: false,
      includeDeviceInfo: true,
      ...config,
    };
  }

  /**
   * 初始化错误报告服务
   */
  initialize(): void {
    if (this.isInitialized || !browser || !this.config.enabled) {
      return;
    }

    this.startFlushTimer();
    this.isInitialized = true;

    reportingLogger.info('ErrorReportingService initialized', {
      config: this.config,
    });
  }

  /**
   * 销毁错误报告服务
   */
  destroy(): void {
    if (!this.isInitialized) {
      return;
    }

    this.stopFlushTimer();
    this.flushReports(); // 发送剩余的报告
    this.reportQueue = [];
    this.isInitialized = false;

    reportingLogger.info('ErrorReportingService destroyed');
  }

  /**
   * 报告错误
   */
  reportError(error: Error, context: ErrorLogContext = {}, errorId?: string): void {
    if (!this.config.enabled || !this.shouldSample()) {
      return;
    }

    try {
      const report = this.createErrorReport(error, context, errorId);
      this.queueReport(report);
    } catch (reportError) {
      reportingLogger.error('Failed to create error report', {
        originalError: error.message,
        reportError: reportError instanceof Error ? reportError.message : String(reportError),
      });
    }
  }

  /**
   * 创建错误报告
   */
  private createErrorReport(error: Error, context: ErrorLogContext, errorId?: string): ErrorReport {
    const report: ErrorReport = {
      errorId: errorId || `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      message: error.message,
      stack: error.stack,
      type: context.errorType || 'unknown',
      timestamp: new Date().toISOString(),
      userAgent: browser ? navigator.userAgent : 'server',
      url: browser ? window.location.href : 'server',
      context: {
        ...context,
        // 移除敏感信息
        userId: this.config.includeUserInfo ? context.userId : undefined,
        sessionId: this.config.includeUserInfo ? context.sessionId : undefined,
      },
    };

    // 添加设备信息
    if (this.config.includeDeviceInfo && browser) {
      report.context = {
        ...report.context,
        deviceInfo: this.getDeviceInfo(),
      };
    }

    return report;
  }

  /**
   * 获取设备信息
   */
  private getDeviceInfo(): Record<string, any> {
    if (!browser) {
      return {};
    }

    return {
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
  }

  /**
   * 判断是否应该采样
   */
  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  /**
   * 将报告加入队列
   */
  private queueReport(report: ErrorReport): void {
    this.reportQueue.push(report);

    reportingLogger.debug('Error report queued', {
      errorId: report.errorId,
      queueSize: this.reportQueue.length,
    });

    // 如果队列已满，立即发送
    if (this.reportQueue.length >= this.config.batchSize) {
      this.flushReports();
    }
  }

  /**
   * 发送报告
   */
  private async flushReports(): Promise<void> {
    if (this.reportQueue.length === 0 || !this.config.endpoint) {
      return;
    }

    const reportsToSend = [...this.reportQueue];
    this.reportQueue = [];

    try {
      await this.sendReports(reportsToSend);
      reportingLogger.info('Error reports sent successfully', {
        count: reportsToSend.length,
      });
    } catch (error) {
      reportingLogger.error('Failed to send error reports', {
        count: reportsToSend.length,
        error: error instanceof Error ? error.message : String(error),
      });

      // 重新加入队列进行重试（限制重试次数）
      const retriableReports = reportsToSend.filter((report) => {
        const retryCount = (report.context?.retryCount || 0) + 1;
        return retryCount <= this.config.maxRetries;
      });

      retriableReports.forEach((report) => {
        report.context = {
          ...report.context,
          retryCount: (report.context?.retryCount || 0) + 1,
        };
      });

      this.reportQueue.unshift(...retriableReports);
    }
  }

  /**
   * 发送报告到服务器
   */
  private async sendReports(reports: ErrorReport[]): Promise<void> {
    if (!this.config.endpoint) {
      throw new Error('No reporting endpoint configured');
    }

    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        reports,
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: browser ? navigator.userAgent : 'server',
          url: browser ? window.location.href : 'server',
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  /**
   * 启动定时发送
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushReports();
    }, this.config.flushInterval);
  }

  /**
   * 停止定时发送
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): { size: number; config: ErrorReportingConfig } {
    return {
      size: this.reportQueue.length,
      config: { ...this.config },
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ErrorReportingConfig>): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...newConfig };

    // 如果启用状态发生变化，重新初始化
    if (wasEnabled !== this.config.enabled) {
      if (this.config.enabled) {
        this.initialize();
      } else {
        this.destroy();
      }
    }

    reportingLogger.info('ErrorReportingService config updated', {
      config: this.config,
    });
  }
}

// 创建全局实例
export const errorReporting = new ErrorReportingService({
  enabled: import.meta.env.PROD, // 只在生产环境启用
  endpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT,
  sampleRate: parseFloat(import.meta.env.VITE_ERROR_SAMPLE_RATE || '1.0'),
});

// 在浏览器环境中自动初始化
if (browser) {
  errorReporting.initialize();
}
