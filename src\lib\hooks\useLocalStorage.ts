/**
 * 本地存储 Hook
 *
 * 提供响应式的本地存储功能
 *
 * @category Hooks
 */

import { type Writable,writable } from 'svelte/store';

import { browser } from '$app/environment';
import { createModuleLogger } from '$lib/utils/logger';

const storageLogger = createModuleLogger('use-local-storage');

/**
 * 本地存储配置
 */
export interface UseLocalStorageOptions<T> {
  /** 默认值 */
  defaultValue: T;
  /** 序列化函数 */
  serializer?: {
    read: (value: string) => T;
    write: (value: T) => string;
  };
  /** 是否在窗口间同步 */
  syncAcrossWindows?: boolean;
  /** 错误处理函数 */
  onError?: (error: Error) => void;
}

/**
 * 本地存储 Hook 返回值
 */
export interface UseLocalStorageReturn<T> {
  /** 存储的值 */
  value: Writable<T>;
  /** 设置值 */
  setValue: (newValue: T | ((prev: T) => T)) => void;
  /** 移除值 */
  removeValue: () => void;
  /** 重置为默认值 */
  resetValue: () => void;
  /** 获取原始存储值 */
  getRawValue: () => string | null;
}

/**
 * 默认序列化器
 */
const defaultSerializer = {
  read: <T>(value: string): T => {
    try {
      return JSON.parse(value);
    } catch {
      return value as unknown as T;
    }
  },
  write: <T>(value: T): string => {
    try {
      return JSON.stringify(value);
    } catch {
      return String(value);
    }
  },
};

/**
 * 本地存储 Hook
 */
export function useLocalStorage<T>(
  key: string,
  options: UseLocalStorageOptions<T>
): UseLocalStorageReturn<T> {
  const {
    defaultValue,
    serializer = defaultSerializer,
    syncAcrossWindows = true,
    onError,
  } = options;

  // 读取初始值
  function readStorageValue(): T {
    if (!browser) {
      return defaultValue;
    }

    try {
      const item = localStorage.getItem(key);
      if (item === null) {
        return defaultValue;
      }
      return serializer.read(item);
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to read from localStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }

      return defaultValue;
    }
  }

  // 创建响应式存储
  const value = writable<T>(readStorageValue());

  // 写入存储值
  function writeStorageValue(newValue: T): void {
    if (!browser) {
      return;
    }

    try {
      const serializedValue = serializer.write(newValue);
      localStorage.setItem(key, serializedValue);

      storageLogger.debug('Value written to localStorage', {
        key,
        value: serializedValue,
      });
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to write to localStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }
    }
  }

  // 设置值
  function setValue(newValue: T | ((prev: T) => T)): void {
    value.update((currentValue) => {
      const updatedValue =
        typeof newValue === 'function' ? (newValue as (prev: T) => T)(currentValue) : newValue;

      writeStorageValue(updatedValue);
      return updatedValue;
    });
  }

  // 移除值
  function removeValue(): void {
    if (!browser) {
      return;
    }

    try {
      localStorage.removeItem(key);
      value.set(defaultValue);

      storageLogger.debug('Value removed from localStorage', { key });
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to remove from localStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }
    }
  }

  // 重置为默认值
  function resetValue(): void {
    setValue(defaultValue);
  }

  // 获取原始存储值
  function getRawValue(): string | null {
    if (!browser) {
      return null;
    }

    try {
      return localStorage.getItem(key);
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to get raw value from localStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }

      return null;
    }
  }

  // 跨窗口同步
  if (browser && syncAcrossWindows) {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === key && event.newValue !== null) {
        try {
          const newValue = serializer.read(event.newValue);
          value.set(newValue);

          storageLogger.debug('Value synced from another window', {
            key,
            newValue: event.newValue,
          });
        } catch (error) {
          const storageError = error instanceof Error ? error : new Error(String(error));
          storageLogger.error('Failed to sync value from another window', {
            key,
            error: storageError.message,
          });

          if (onError) {
            onError(storageError);
          }
        }
      } else if (event.key === key && event.newValue === null) {
        // 值被删除
        value.set(defaultValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // 清理函数
    const cleanup = () => {
      window.removeEventListener('storage', handleStorageChange);
    };

    // 在 Svelte 中，我们不能直接使用 onDestroy，所以返回清理函数
    // 调用者需要在组件销毁时手动调用
    (setValue as any).cleanup = cleanup;
  }

  return {
    value,
    setValue,
    removeValue,
    resetValue,
    getRawValue,
  };
}

/**
 * 简化的本地存储 Hook
 */
export function useSimpleLocalStorage<T>(key: string, defaultValue: T): UseLocalStorageReturn<T> {
  return useLocalStorage(key, { defaultValue });
}

/**
 * 会话存储 Hook
 */
export function useSessionStorage<T>(
  key: string,
  options: UseLocalStorageOptions<T>
): UseLocalStorageReturn<T> {
  const { defaultValue, serializer = defaultSerializer, onError } = options;

  // 读取初始值
  function readStorageValue(): T {
    if (!browser) {
      return defaultValue;
    }

    try {
      const item = sessionStorage.getItem(key);
      if (item === null) {
        return defaultValue;
      }
      return serializer.read(item);
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to read from sessionStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }

      return defaultValue;
    }
  }

  // 创建响应式存储
  const value = writable<T>(readStorageValue());

  // 写入存储值
  function writeStorageValue(newValue: T): void {
    if (!browser) {
      return;
    }

    try {
      const serializedValue = serializer.write(newValue);
      sessionStorage.setItem(key, serializedValue);

      storageLogger.debug('Value written to sessionStorage', {
        key,
        value: serializedValue,
      });
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to write to sessionStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }
    }
  }

  // 设置值
  function setValue(newValue: T | ((prev: T) => T)): void {
    value.update((currentValue) => {
      const updatedValue =
        typeof newValue === 'function' ? (newValue as (prev: T) => T)(currentValue) : newValue;

      writeStorageValue(updatedValue);
      return updatedValue;
    });
  }

  // 移除值
  function removeValue(): void {
    if (!browser) {
      return;
    }

    try {
      sessionStorage.removeItem(key);
      value.set(defaultValue);

      storageLogger.debug('Value removed from sessionStorage', { key });
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to remove from sessionStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }
    }
  }

  // 重置为默认值
  function resetValue(): void {
    setValue(defaultValue);
  }

  // 获取原始存储值
  function getRawValue(): string | null {
    if (!browser) {
      return null;
    }

    try {
      return sessionStorage.getItem(key);
    } catch (error) {
      const storageError = error instanceof Error ? error : new Error(String(error));
      storageLogger.error('Failed to get raw value from sessionStorage', {
        key,
        error: storageError.message,
      });

      if (onError) {
        onError(storageError);
      }

      return null;
    }
  }

  return {
    value,
    setValue,
    removeValue,
    resetValue,
    getRawValue,
  };
}
