[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [stores](../README.md) / liquidationStore

# Variable: liquidationStore

> `const` **liquidationStore**: `object`

Defined in: src/lib/stores/features/liquidation.ts:322

## Type declaration

### charts

> **charts**: `Readable`\<[`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]\>

### error

> **error**: `Readable`\<`null` \| `string`\>

### loading

> **loading**: `Readable`\<`boolean`\>

### reorderCharts()

> **reorderCharts**: (`newOrder`) => `void`

#### Parameters

##### newOrder

`string`[]

#### Returns

`void`

### reset()

> **reset**: () => `void`

#### Returns

`void`

### setSnapshotTimeRange()

> **setSnapshotTimeRange**: (`timeRange`) => `void`

#### Parameters

##### timeRange

[`SnapshotTimeRange`](../../types/type-aliases/SnapshotTimeRange.md)

#### Returns

`void`

### setTrendDuration()

> **setTrendDuration**: (`duration`) => `void`

#### Parameters

##### duration

[`TrendDuration`](../../types/type-aliases/TrendDuration.md)

#### Returns

`void`

### setTrendTimeFrame()

> **setTrendTimeFrame**: (`timeFrame`) => `void`

#### Parameters

##### timeFrame

[`TrendTimeFrame`](../../types/type-aliases/TrendTimeFrame.md)

#### Returns

`void`

### setTrendTimeRange()

> **setTrendTimeRange**: (`timeRange`) => `void`

#### Parameters

##### timeRange

[`TrendTimeRange`](../../types/type-aliases/TrendTimeRange.md)

#### Returns

`void`

### setTrendViewMode()

> **setTrendViewMode**: (`viewMode`) => `void`

#### Parameters

##### viewMode

`"overall"` | `"comparison"`

#### Returns

`void`

### snapshotCharts

> **snapshotCharts**: `Readable`\<[`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]\>

### snapshotData

> **snapshotData**: `Readable`\<[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]\>

### startAutoRefresh()

> **startAutoRefresh**: (`interval`) => `void`

#### Parameters

##### interval

`number` = `60000`

#### Returns

`void`

### stats

> **stats**: `Readable`\<[`LiquidationStats`](../../types/interfaces/LiquidationStats.md)\>

### stopAutoRefresh()

> **stopAutoRefresh**: () => `void`

#### Returns

`void`

### subscribe()

> **subscribe**: (`this`, `run`, `invalidate?`) => `Unsubscriber`

Subscribe on value changes.

#### Parameters

##### this

`void`

##### run

`Subscriber`\<\{ `chartConfigs`: [`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]; `error`: `null` \| `string`; `isLoading`: `boolean`; `snapshotData`: [`LiquidationData`](../../types/interfaces/LiquidationData.md)[]; `stats`: [`LiquidationStats`](../../types/interfaces/LiquidationStats.md); `timeFilters`: [`TimeFilters`](../../types/interfaces/TimeFilters.md); `trendData`: [`LiquidationData`](../../types/interfaces/LiquidationData.md)[]; \}\>

subscription callback

##### invalidate?

() => `void`

cleanup callback

#### Returns

`Unsubscriber`

### timeFilters

> **timeFilters**: `Readable`\<[`TimeFilters`](../../types/interfaces/TimeFilters.md)\>

### toggleChartVisibility()

> **toggleChartVisibility**: (`chartId`) => `void`

#### Parameters

##### chartId

`string`

#### Returns

`void`

### trendCharts

> **trendCharts**: `Readable`\<[`LiquidationChartConfig`](../../types/interfaces/LiquidationChartConfig.md)[]\>

### trendData

> **trendData**: `Readable`\<[`LiquidationData`](../../types/interfaces/LiquidationData.md)[]\>

### updateTimeFilters()

> **updateTimeFilters**: (`filters`) => `void`

#### Parameters

##### filters

`Partial`\<[`TimeFilters`](../../types/interfaces/TimeFilters.md)\>

#### Returns

`void`

### loadDetailedTrendData()

> **loadDetailedTrendData**(`timeFrame?`, `duration?`): `Promise`\<`void`\>

#### Parameters

##### timeFrame?

[`TrendTimeFrame`](../../types/type-aliases/TrendTimeFrame.md)

##### duration?

[`TrendDuration`](../../types/type-aliases/TrendDuration.md)

#### Returns

`Promise`\<`void`\>

### loadSnapshotData()

> **loadSnapshotData**(`timeRange?`): `Promise`\<`void`\>

#### Parameters

##### timeRange?

[`SnapshotTimeRange`](../../types/type-aliases/SnapshotTimeRange.md)

#### Returns

`Promise`\<`void`\>

### loadTrendData()

> **loadTrendData**(`timeRange?`): `Promise`\<`void`\>

#### Parameters

##### timeRange?

[`TrendTimeRange`](../../types/type-aliases/TrendTimeRange.md)

#### Returns

`Promise`\<`void`\>

### refreshAllData()

> **refreshAllData**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>
