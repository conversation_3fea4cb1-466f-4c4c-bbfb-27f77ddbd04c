[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [constants](../README.md) / CHART_COLORS

# Variable: CHART_COLORS

> `const` **CHART_COLORS**: `object`

Defined in: src/lib/constants/charts.ts:2

## Type declaration

### ALTCOIN

> `readonly` **ALTCOIN**: `"#722ed1"` = `'#722ed1'`

### ALTCOIN_LONG

> `readonly` **ALTCOIN_LONG**: `"#52c41a"` = `'#52c41a'`

### ALTCOIN_SHORT

> `readonly` **ALTCOIN_SHORT**: `"#f5222d"` = `'#f5222d'`

### ERROR

> `readonly` **ERROR**: `"#f5222d"` = `'#f5222d'`

### LONG

> `readonly` **LONG**: `"#52c41a"` = `'#52c41a'`

### MAJOR

> `readonly` **MAJOR**: `"#1890ff"` = `'#1890ff'`

### MAJOR_LONG

> `readonly` **MAJOR_LONG**: `"#1890ff"` = `'#1890ff'`

### MAJOR_SHORT

> `readonly` **MAJOR_SHORT**: `"#722ed1"` = `'#722ed1'`

### PALETTE

> `readonly` **PALETTE**: readonly \[`"#1890ff"`, `"#52c41a"`, `"#faad14"`, `"#f5222d"`, `"#722ed1"`, `"#13c2c2"`, `"#eb2f96"`, `"#fa8c16"`, `"#a0d911"`, `"#2f54eb"`\]

### PRIMARY

> `readonly` **PRIMARY**: `"#1890ff"` = `'#1890ff'`

### SHORT

> `readonly` **SHORT**: `"#f5222d"` = `'#f5222d'`

### SUCCESS

> `readonly` **SUCCESS**: `"#52c41a"` = `'#52c41a'`

### WARNING

> `readonly` **WARNING**: `"#faad14"` = `'#faad14'`
