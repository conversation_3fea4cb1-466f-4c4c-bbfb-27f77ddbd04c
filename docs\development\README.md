# 开发指南

本文档提供了项目开发的详细指南，包括环境搭建、开发流程和最佳实践。

## 🚀 环境搭建

### 系统要求

- **Node.js**: 18.0 或更高版本
- **npm**: 9.0 或更高版本 (或 yarn 1.22+)
- **Git**: 2.30 或更高版本

### 开发工具推荐

- **VS Code**: 推荐的代码编辑器
- **Svelte for VS Code**: Svelte 语法支持
- **TypeScript and JavaScript**: TypeScript 支持
- **Tailwind CSS IntelliSense**: CSS 类名提示
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

### 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd svelte-demo

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 在浏览器中打开 http://localhost:5174
```

## 🔧 开发流程

### 1. 创建新功能分支

```bash
# 从 main 分支创建新分支
git checkout main
git pull origin main
git checkout -b feature/new-feature-name
```

### 2. 开发新功能

按照以下步骤开发新功能：

#### 添加新页面

```bash
# 1. 创建路由文件
touch src/routes/new-page/+page.svelte

# 2. 创建页面组件
mkdir -p src/lib/components/features/newPage
touch src/lib/components/features/newPage/NewPage.svelte
touch src/lib/components/features/newPage/index.ts

# 3. 更新导航配置
# 编辑 src/lib/components/layout/Sidebar/navigationConfig.ts
```

#### 添加新组件

```bash
# 1. 创建组件目录
mkdir -p src/lib/components/ui/newComponent

# 2. 创建组件文件
touch src/lib/components/ui/newComponent/NewComponent.svelte
touch src/lib/components/ui/newComponent/NewComponent.test.ts
touch src/lib/components/ui/newComponent/index.ts

# 3. 更新导出文件
# 编辑 src/lib/components/ui/index.ts
```

#### 添加新服务

```bash
# 1. 创建服务文件
touch src/lib/services/data/newService.ts

# 2. 创建类型定义
touch src/lib/types/newService.ts

# 3. 更新导出文件
# 编辑 src/lib/services/data/index.ts
# 编辑 src/lib/types/index.ts
```

### 3. 代码质量检查

```bash
# 类型检查
npm run check

# 代码格式化
npm run format

# 代码质量检查
npm run lint

# 运行测试
npm run test
```

### 4. 提交代码

```bash
# 添加文件
git add .

# 提交代码 (使用约定式提交)
git commit -m "feat: add new feature description"

# 推送分支
git push origin feature/new-feature-name
```

## 📝 代码规范

### 文件命名

- **组件文件**: PascalCase.svelte (如 `UserProfile.svelte`)
- **工具文件**: camelCase.ts (如 `formatDate.ts`)
- **类型文件**: camelCase.ts (如 `userTypes.ts`)
- **测试文件**: _.test.ts 或 _.spec.ts

### 目录结构

```
src/lib/components/ui/button/
├── Button.svelte          # 主组件文件
├── Button.test.ts         # 测试文件
├── button.types.ts        # 类型定义 (可选)
└── index.ts              # 导出文件
```

### 组件编写规范

#### 基本结构

```svelte
<script lang="ts">
  // 1. 导入语句
  import { createEventDispatcher } from 'svelte';
  import type { ComponentProps } from './button.types';

  // 2. 类型定义
  interface $$Props extends ComponentProps {}

  // 3. Props 定义
  export let variant: 'primary' | 'secondary' = 'primary';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let disabled = false;

  // 4. 事件派发器
  const dispatch = createEventDispatcher<{
    click: MouseEvent;
  }>();

  // 5. 响应式语句
  $: classes = `btn btn-${variant} btn-${size}`;

  // 6. 函数定义
  function handleClick(event: MouseEvent) {
    if (!disabled) {
      dispatch('click', event);
    }
  }
</script>

<!-- 7. 模板 -->
<button class={classes} {disabled} on:click={handleClick} {...$$restProps}>
  <slot />
</button>

<!-- 8. 样式 (如果需要) -->
<style>
  .btn {
    /* 组件样式 */
  }
</style>
```

### TypeScript 规范

#### 类型定义

```typescript
// 使用 interface 定义对象类型
interface User {
  id: string;
  name: string;
  email: string;
}

// 使用 type 定义联合类型
type Status = 'loading' | 'success' | 'error';

// 使用泛型
interface ApiResponse<T> {
  data: T;
  status: 'success' | 'error';
  message?: string;
}
```

#### 函数定义

```typescript
// 明确的参数和返回类型
async function fetchUserData(userId: string): Promise<User> {
  const response = await fetch(`/api/users/${userId}`);
  return response.json();
}

// 使用类型守卫
function isUser(obj: unknown): obj is User {
  return typeof obj === 'object' && obj !== null && 'id' in obj && 'name' in obj;
}
```

## 🧪 测试策略

### 单元测试

```typescript
import { render, screen, fireEvent } from '@testing-library/svelte';
import Button from './Button.svelte';

describe('Button Component', () => {
  test('renders with correct text', () => {
    render(Button, { props: { variant: 'primary' } });
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('handles click events', async () => {
    const { component } = render(Button);
    const mockFn = vi.fn();

    component.$on('click', mockFn);
    await fireEvent.click(screen.getByRole('button'));

    expect(mockFn).toHaveBeenCalled();
  });
});
```

### 集成测试

```typescript
import { render, screen, waitFor } from '@testing-library/svelte';
import Dashboard from './Dashboard.svelte';

describe('Dashboard Integration', () => {
  test('loads and displays data', async () => {
    render(Dashboard);

    await waitFor(() => {
      expect(screen.getByText('仪表板')).toBeInTheDocument();
    });

    expect(screen.getByText('总用户数')).toBeInTheDocument();
  });
});
```

## 🔍 调试技巧

### 开发工具

```typescript
// 1. 使用统一日志系统 (推荐) 🆕
import { logger, createModuleLogger } from '$lib/utils/logger';

logger.debug('Debug info:', { variable, state });
const moduleLogger = createModuleLogger('dashboard');
moduleLogger.info('模块状态', { data });

// 2. 使用 Svelte DevTools
// 安装浏览器扩展后可以查看组件状态

// 3. 使用断点调试
debugger; // 在需要的地方添加断点
```

### 性能分析

```typescript
// 1. 使用统一日志系统的性能监控 (推荐) 🆕
import { performanceLogger } from '$lib/utils/logger';

const startTime = performance.now();
// ... 执行操作
const endTime = performance.now();
performanceLogger.info('操作完成', {
  operation: 'dataLoad',
  duration: endTime - startTime
});

// 2. 使用 Svelte 的性能分析
// 在开发模式下启用性能分析
```

## 📦 构建和部署

### 本地构建

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 环境变量

```bash
# .env.local
VITE_API_BASE_URL=https://api.example.com
VITE_APP_VERSION=1.0.0

# 日志系统配置 🆕
VITE_LOG_LEVEL=debug
VITE_LOG_PRETTY_PRINT=true
VITE_PERFORMANCE_MONITORING=true
```

### 部署检查清单

- [ ] 所有测试通过
- [ ] 类型检查通过
- [ ] 代码格式化完成
- [ ] 构建成功
- [ ] 环境变量配置正确
- [ ] 性能测试通过

## 🤝 团队协作

### Git 工作流

1. **功能开发**: 从 `main` 创建 `feature/` 分支
2. **Bug 修复**: 从 `main` 创建 `fix/` 分支
3. **代码审查**: 创建 Pull Request
4. **合并**: 通过审查后合并到 `main`

### 提交信息规范

```bash
# 格式: type(scope): description
feat(dashboard): add new chart component
fix(api): resolve data loading issue
docs(readme): update installation guide
test(button): add unit tests
refactor(stores): improve state management
```

### 代码审查要点

- [ ] 代码符合项目规范
- [ ] 功能实现正确
- [ ] 测试覆盖充分
- [ ] 性能影响可接受
- [ ] 文档更新完整
