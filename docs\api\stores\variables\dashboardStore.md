[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [stores](../README.md) / dashboardStore

# Variable: dashboardStore

> `const` **dashboardStore**: `object`

Defined in: src/lib/stores/features/dashboard.ts:252

## Type declaration

### chartData

> **chartData**: `Readable`\<[`ChartData`](../../types/interfaces/ChartData.md)\>

### charts

> **charts**: `Readable`\<[`ChartConfig`](../../types/interfaces/ChartConfig.md)[]\>

### clearError()

> **clearError**: () => `void`

#### Returns

`void`

### error

> **error**: `Readable`\<`null` \| `string`\>

### loading

> **loading**: `Readable`\<`boolean`\>

### reorderCharts()

> **reorderCharts**: (`newOrder`) => `void`

#### Parameters

##### newOrder

`string`[]

#### Returns

`void`

### reset()

> **reset**: () => `void`

#### Returns

`void`

### setLoading()

> **setLoading**: (`isLoading`) => `void`

#### Parameters

##### isLoading

`boolean`

#### Returns

`void`

### setRefreshInterval()

> **setRefreshInterval**: (`interval`) => `void`

#### Parameters

##### interval

`null` | `number`

#### Returns

`void`

### setTimeRange()

> **setTimeRange**: (`__namedParameters`) => `void`

#### Parameters

##### \_\_namedParameters

###### customEndTime?

`string` = `''`

###### customStartTime?

`string` = `''`

###### selectedTimeRange

`string`

###### selectedTimeZone?

`string` = `'UTC'`

#### Returns

`void`

### stats

> **stats**: `Readable`\<[`DashboardStats`](../../types/interfaces/DashboardStats.md)\>

### subscribe()

> **subscribe**: (`this`, `run`, `invalidate?`) => `Unsubscriber`

Subscribe on value changes.

#### Parameters

##### this

`void`

##### run

`Subscriber`\<\{ `chartConfigs`: [`ChartConfig`](../../types/interfaces/ChartConfig.md)[]; `chartData`: [`ChartData`](../../types/interfaces/ChartData.md); `customEndTime`: `string`; `customStartTime`: `string`; `error`: `null` \| `string`; `isLoading`: `boolean`; `refreshInterval`: `null` \| `number`; `selectedTimeRange`: [`TimeRange`](../../types/type-aliases/TimeRange.md); `selectedTimeZone`: `string`; `stats`: [`DashboardStats`](../../types/interfaces/DashboardStats.md); \}\>

subscription callback

##### invalidate?

() => `void`

cleanup callback

#### Returns

`Unsubscriber`

### toggleChartVisibility()

> **toggleChartVisibility**: (`chartId`) => `void`

#### Parameters

##### chartId

`string`

#### Returns

`void`

### loadDashboardData()

> **loadDashboardData**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>

### refreshChartData()

> **refreshChartData**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>

### refreshStats()

> **refreshStats**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>
