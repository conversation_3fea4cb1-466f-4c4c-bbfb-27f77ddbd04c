/**
 * 节流 Hook
 *
 * 提供节流功能，限制函数在指定时间内的执行频率
 *
 * @category Hooks
 */

import { onDestroy } from 'svelte';
import { derived, type Readable,type Writable, writable } from 'svelte/store';

import { createModuleLogger } from '$lib/utils/logger';

const throttleLogger = createModuleLogger('use-throttle');

/**
 * 节流配置
 */
export interface UseThrottleOptions {
  /** 节流间隔（毫秒） */
  interval: number;
  /** 是否在开始时立即执行 */
  leading?: boolean;
  /** 是否在结束时执行 */
  trailing?: boolean;
}

/**
 * 节流值 Hook 返回值
 */
export interface UseThrottledValueReturn<T> {
  /** 节流后的值 */
  throttledValue: Readable<T>;
  /** 是否正在节流中 */
  isThrottling: Readable<boolean>;
  /** 立即更新值 */
  flush: () => void;
  /** 取消节流 */
  cancel: () => void;
}

/**
 * 节流函数 Hook 返回值
 */
export interface UseThrottledFunctionReturn<T extends (...args: any[]) => any> {
  /** 节流后的函数 */
  throttledFunction: T;
  /** 是否正在节流中 */
  isThrottling: Readable<boolean>;
  /** 立即执行 */
  flush: () => void;
  /** 取消执行 */
  cancel: () => void;
}

/**
 * 节流值 Hook
 */
export function useThrottledValue<T>(
  value: Writable<T>,
  options: UseThrottleOptions
): UseThrottledValueReturn<T> {
  const { interval, leading = true, trailing = true } = options;

  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  let lastValue: T;
  let hasTrailingCall = false;

  const isThrottling = writable(false);
  const throttledValue = writable<T>();

  // 初始化节流值
  value.subscribe((currentValue) => {
    throttledValue.set(currentValue);
  });

  const throttledStore = derived<[Writable<T>], T>([value], ([currentValue], set) => {
    const now = Date.now();
    lastValue = currentValue;

    // 如果是第一次调用且启用了 leading
    if (leading && lastExecTime === 0) {
      set(currentValue);
      lastExecTime = now;
      isThrottling.set(true);

      throttleLogger.debug('Leading execution', { interval, value: currentValue });

      // 设置节流结束定时器
      timeoutId = setTimeout(() => {
        isThrottling.set(false);

        // 如果有待执行的调用且启用了 trailing
        if (hasTrailingCall && trailing) {
          set(lastValue);
          lastExecTime = Date.now();
          hasTrailingCall = false;

          throttleLogger.debug('Trailing execution', { interval, value: lastValue });
        }

        timeoutId = null;
      }, interval);

      return;
    }

    // 计算距离上次执行的时间
    const timeSinceLastExec = now - lastExecTime;

    if (timeSinceLastExec >= interval) {
      // 可以立即执行
      set(currentValue);
      lastExecTime = now;
      isThrottling.set(true);
      hasTrailingCall = false;

      throttleLogger.debug('Throttled execution', {
        interval,
        timeSinceLastExec,
        value: currentValue,
      });

      // 设置节流结束定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(() => {
        isThrottling.set(false);

        // 如果有待执行的调用且启用了 trailing
        if (hasTrailingCall && trailing) {
          set(lastValue);
          lastExecTime = Date.now();
          hasTrailingCall = false;

          throttleLogger.debug('Trailing execution after throttle', { interval, value: lastValue });
        }

        timeoutId = null;
      }, interval);
    } else {
      // 标记有待执行的调用
      hasTrailingCall = true;

      throttleLogger.debug('Call throttled', { interval, timeSinceLastExec, value: currentValue });
    }
  });

  // 立即更新值
  function flush(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    value.subscribe((currentValue) => {
      throttledValue.set(currentValue);
      lastExecTime = Date.now();
      isThrottling.set(false);
      hasTrailingCall = false;

      throttleLogger.debug('Flushed', { value: currentValue });
    })();
  }

  // 取消节流
  function cancel(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    isThrottling.set(false);
    hasTrailingCall = false;

    throttleLogger.debug('Cancelled');
  }

  // 清理函数
  onDestroy(() => {
    cancel();
  });

  return {
    throttledValue: throttledStore,
    isThrottling,
    flush,
    cancel,
  };
}

/**
 * 节流函数 Hook
 */
export function useThrottledFunction<T extends (...args: any[]) => any>(
  fn: T,
  options: UseThrottleOptions
): UseThrottledFunctionReturn<T> {
  const { interval, leading = true, trailing = true } = options;

  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  let lastArgs: Parameters<T> | null = null;
  let hasTrailingCall = false;

  const isThrottling = writable(false);

  function throttledFunction(...args: Parameters<T>): void {
    const now = Date.now();
    lastArgs = args;

    // 如果是第一次调用且启用了 leading
    if (leading && lastExecTime === 0) {
      fn(...args);
      lastExecTime = now;
      isThrottling.set(true);

      throttleLogger.debug('Leading function execution', { interval, args });

      // 设置节流结束定时器
      timeoutId = setTimeout(() => {
        isThrottling.set(false);

        // 如果有待执行的调用且启用了 trailing
        if (hasTrailingCall && trailing && lastArgs) {
          fn(...lastArgs);
          lastExecTime = Date.now();
          hasTrailingCall = false;

          throttleLogger.debug('Trailing function execution', { interval, args: lastArgs });
        }

        timeoutId = null;
      }, interval);

      return;
    }

    // 计算距离上次执行的时间
    const timeSinceLastExec = now - lastExecTime;

    if (timeSinceLastExec >= interval) {
      // 可以立即执行
      fn(...args);
      lastExecTime = now;
      isThrottling.set(true);
      hasTrailingCall = false;

      throttleLogger.debug('Throttled function execution', { interval, timeSinceLastExec, args });

      // 设置节流结束定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(() => {
        isThrottling.set(false);

        // 如果有待执行的调用且启用了 trailing
        if (hasTrailingCall && trailing && lastArgs) {
          fn(...lastArgs);
          lastExecTime = Date.now();
          hasTrailingCall = false;

          throttleLogger.debug('Trailing function execution after throttle', {
            interval,
            args: lastArgs,
          });
        }

        timeoutId = null;
      }, interval);
    } else {
      // 标记有待执行的调用
      hasTrailingCall = true;

      throttleLogger.debug('Function call throttled', { interval, timeSinceLastExec, args });
    }
  }

  // 立即执行
  function flush(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (lastArgs) {
      fn(...lastArgs);
      lastExecTime = Date.now();
      isThrottling.set(false);
      hasTrailingCall = false;

      throttleLogger.debug('Function flushed', { args: lastArgs });
    }
  }

  // 取消执行
  function cancel(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    isThrottling.set(false);
    hasTrailingCall = false;
    lastArgs = null;

    throttleLogger.debug('Function cancelled');
  }

  // 清理函数
  onDestroy(() => {
    cancel();
  });

  return {
    throttledFunction: throttledFunction as T,
    isThrottling,
    flush,
    cancel,
  };
}

/**
 * 简化的节流值 Hook
 */
export function useSimpleThrottle<T>(value: Writable<T>, interval: number): Readable<T> {
  const { throttledValue } = useThrottledValue(value, { interval });
  return throttledValue;
}

/**
 * 简化的节流函数 Hook
 */
export function useSimpleThrottledFunction<T extends (...args: any[]) => any>(
  fn: T,
  interval: number
): T {
  const { throttledFunction } = useThrottledFunction(fn, { interval });
  return throttledFunction;
}
