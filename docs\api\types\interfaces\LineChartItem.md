[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / LineChartItem

# Interface: LineChartItem

Defined in: src/lib/types/dashboard.ts:38

折线图数据项

## Example

```typescript
const lineData: LineChartItem = {
  name: '周一',
  value: 85,
};
```

## Properties

### name

> **name**: `string`

Defined in: src/lib/types/dashboard.ts:40

数据点名称，通常用作 X 轴标签

---

### value

> **value**: `number`

Defined in: src/lib/types/dashboard.ts:42

数据值，用作 Y 轴坐标
