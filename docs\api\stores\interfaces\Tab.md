[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [stores](../README.md) / Tab

# Interface: Tab

Defined in: src/lib/stores/ui/tabs.ts:6

## Properties

### closeable

> **closeable**: `boolean`

Defined in: src/lib/stores/ui/tabs.ts:11

---

### href

> **href**: `string`

Defined in: src/lib/stores/ui/tabs.ts:9

---

### icon?

> `optional` **icon**: `Component`\<\{ \}, \{ \}, `string`\>

Defined in: src/lib/stores/ui/tabs.ts:10

---

### id

> **id**: `string`

Defined in: src/lib/stores/ui/tabs.ts:7

---

### label

> **label**: `string`

Defined in: src/lib/stores/ui/tabs.ts:8
