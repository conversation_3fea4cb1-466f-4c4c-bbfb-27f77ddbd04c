import { http, HttpResponse } from 'msw';

import {
  barChartData,
  generateMockData,
  lineChartData,
  pieChartData,
  scatterChartData,
} from '$lib/data/mockData';
import type { MessageType, RealTimeMessage } from '$lib/stores/features/realTimeMessages';
import type {
  LiquidationData,
  LiquidationRankItem,
  LiquidationRankResponse,
  LiquidationStats,
  RankQueryParams,
  RankSortField,
  TrendDuration,
  TrendTimeFrame,
} from '$lib/types';

// 生成ULID格式的ID辅助函数
function generateULID(): string {
  const timestamp = Date.now().toString(36).toUpperCase();
  const randomPart = Math.random().toString(36).substring(2, 15).toUpperCase();
  return `01${timestamp}${randomPart}`.substring(0, 26);
}

// 爆仓数据mock生成函数
function generateMockLiquidationData(count: number = 100): LiquidationData[] {
  const symbols = ['BTC', 'ETH', 'SOL', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC'];
  const thresholds = [10000, 50000, 100000, 500000, 1000000];
  const result: LiquidationData[] = [];

  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  for (let i = 0; i < count; i++) {
    const randomOffset = Math.random() * (now.getTime() - oneDayAgo.getTime());
    const randomDate = new Date(oneDayAgo.getTime() + randomOffset);
    const amount = Math.round(10000 + Math.random() * 990000);
    const side = Math.random() > 0.5 ? 1 : 0;
    const symbolIndex = Math.floor(Math.random() * symbols.length);
    const symbol = symbols[symbolIndex];
    const thresholdIndex = Math.floor(Math.random() * thresholds.length);
    const threshold = thresholds[thresholdIndex];
    const coinType = ['BTC', 'ETH', 'SOL', 'BNB'].includes(symbol) ? 'Major' : 'Altcoin';

    result.push({
      id: `liq-${i}`,
      datetime: randomDate.toISOString(),
      amountUsd: amount,
      side,
      symbol,
      threshold: String(threshold),
      coinType,
    });
  }

  return result.sort((a, b) => new Date(b.datetime).getTime() - new Date(a.datetime).getTime());
}

function generateMockTrendData(duration: TrendDuration): LiquidationData[] {
  const symbols = ['BTC', 'ETH', 'SOL', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC'];
  const thresholds = [10000, 50000, 100000, 500000, 1000000];
  const result: LiquidationData[] = [];

  // 根据duration计算时间范围
  const now = new Date();
  let startTime: Date;
  let totalMinutes: number;

  switch (duration) {
    case '1d':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      totalMinutes = 24 * 60; // 1440分钟
      break;
    case '7d':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      totalMinutes = 7 * 24 * 60; // 10080分钟
      break;
    case '30d':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      totalMinutes = 30 * 24 * 60; // 43200分钟
      break;
    case '90d':
      startTime = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      totalMinutes = 90 * 24 * 60; // 129600分钟
      break;
    default:
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      totalMinutes = 7 * 24 * 60;
  }

  // 模拟真实 API 行为：固定以15分钟粒度返回数据
  // 图表组件会根据选择的 timeFrame 进行聚合
  const minuteInterval = 15; // API 固定以15分钟粒度返回数据
  const dataPointsCount = Math.floor(totalMinutes / minuteInterval);

  for (let i = 0; i < dataPointsCount; i++) {
    // 确保时间戳是15分钟对齐的，模拟真实API行为
    const pointTime = new Date(startTime.getTime() + i * minuteInterval * 60 * 1000);

    // 在每个15分钟时间点生成随机数量的爆仓记录
    const liquidationsPerPoint = Math.floor(Math.random() * 15) + 3; // 每个时间点3-18个爆仓

    for (let j = 0; j < liquidationsPerPoint; j++) {
      // 在15分钟时间窗口内随机分布，但保持时间戳的真实性
      const randomOffset = Math.random() * minuteInterval * 60 * 1000; // 在15分钟内随机分布
      const actualTime = new Date(pointTime.getTime() + randomOffset);

      const amount = Math.round(10000 + Math.random() * 990000);
      const side = Math.random() > 0.5 ? 1 : 0;
      const symbolIndex = Math.floor(Math.random() * symbols.length);
      const symbol = symbols[symbolIndex];
      const thresholdIndex = Math.floor(Math.random() * thresholds.length);
      const threshold = thresholds[thresholdIndex];
      const coinType = ['BTC', 'ETH', 'SOL', 'BNB'].includes(symbol) ? 'Major' : 'Altcoin';

      result.push({
        id: `trend-${i}-${j}`,
        datetime: actualTime.toISOString(),
        amountUsd: amount,
        side,
        symbol,
        threshold: String(threshold),
        coinType,
      });
    }
  }

  return result.sort((a, b) => new Date(a.datetime).getTime() - new Date(b.datetime).getTime());
}

function calculateLiquidationStats(data: LiquidationData[]): LiquidationStats {
  const totalAmount = data.reduce((sum, item) => sum + item.amountUsd, 0);
  const totalCount = data.length;
  const longAmount = data
    .filter((item) => item.side === 1)
    .reduce((sum, item) => sum + item.amountUsd, 0);
  const shortAmount = data
    .filter((item) => item.side === 0)
    .reduce((sum, item) => sum + item.amountUsd, 0);
  const longShortRatio = shortAmount > 0 ? longAmount / shortAmount : 0;

  return {
    totalAmount,
    totalCount,
    longAmount,
    shortAmount,
    longShortRatio,
  };
}

// 生成清算排行榜数据
function generateLiquidationRankData(params: RankQueryParams): LiquidationRankResponse {
  const symbols = [
    // 主流币
    { symbol: 'BTC', coinType: 'Major' as const },
    { symbol: 'ETH', coinType: 'Major' as const },
    { symbol: 'SOL', coinType: 'Major' as const },
    { symbol: 'BNB', coinType: 'Major' as const },
    { symbol: 'XRP', coinType: 'Major' as const },
    { symbol: 'ADA', coinType: 'Major' as const },
    // 山寨币
    { symbol: 'DOT', coinType: 'Altcoin' as const },
    { symbol: 'LINK', coinType: 'Altcoin' as const },
    { symbol: 'UNI', coinType: 'Altcoin' as const },
    { symbol: 'AVAX', coinType: 'Altcoin' as const },
    { symbol: 'MATIC', coinType: 'Altcoin' as const },
    { symbol: 'ATOM', coinType: 'Altcoin' as const },
    { symbol: 'FTM', coinType: 'Altcoin' as const },
    { symbol: 'NEAR', coinType: 'Altcoin' as const },
    { symbol: 'ALGO', coinType: 'Altcoin' as const },
    { symbol: 'VET', coinType: 'Altcoin' as const },
    { symbol: 'ICP', coinType: 'Altcoin' as const },
    { symbol: 'FLOW', coinType: 'Altcoin' as const },
    { symbol: 'SAND', coinType: 'Altcoin' as const },
    { symbol: 'MANA', coinType: 'Altcoin' as const },
  ];

  // 生成每个币种的排行数据
  const allItems: LiquidationRankItem[] = symbols.map((coin, index) => {
    // 基础清算金额，主流币通常更高
    const baseAmount =
      coin.coinType === 'Major'
        ? 50000000 + Math.random() * 200000000 // 5000万-2.5亿
        : 5000000 + Math.random() * 45000000; // 500万-5000万

    const totalAmount = Math.round(baseAmount);
    const totalCount = Math.round(100 + Math.random() * 900); // 100-1000笔

    // 多空分布，随机但合理
    const longRatio = 0.3 + Math.random() * 0.4; // 30%-70%
    const longAmount = Math.round(totalAmount * longRatio);
    const shortAmount = totalAmount - longAmount;
    const longShortRatio = shortAmount > 0 ? longAmount / shortAmount : 0;

    // 多空笔数分布
    const longCount = Math.round(totalCount * longRatio);
    const shortCount = totalCount - longCount;

    // 趋势数据
    const amountChange = (Math.random() - 0.5) * 40; // -20% 到 +20%
    const countChange = (Math.random() - 0.5) * 30; // -15% 到 +15%
    const direction = amountChange > 5 ? 'up' : amountChange < -5 ? 'down' : 'stable';

    return {
      rank: index + 1, // 临时排名，后面会重新排序
      symbol: coin.symbol,
      name: coin.symbol, // 使用符号作为名称
      amount: totalAmount,
      count: totalCount,
      changePercent: amountChange,
      icon: `https://assets.coingecko.com/coins/images/1/large/${coin.symbol.toLowerCase()}.png`,
      coinType: coin.coinType,
      totalAmount,
      totalCount,
      longAmount,
      shortAmount,
      longCount,
      shortCount,
      longShortRatio,
      avgAmount: Math.round(totalAmount / totalCount),
      maxAmount: Math.round(totalAmount * 1.5),
      minAmount: Math.round(totalAmount * 0.1),
    };
  });

  // 应用筛选条件
  let filteredItems = allItems;

  // 币种类型筛选
  if (params.coinTypeFilter && params.coinTypeFilter !== 'all') {
    filteredItems = filteredItems.filter((item) => item.coinType === params.coinTypeFilter);
  }

  // 根据排行榜类型确定默认排序字段
  let defaultSortField: RankSortField;
  if (params.rankType === 'amount') {
    defaultSortField = 'totalAmount';
  } else {
    defaultSortField = 'totalCount';
  }

  // 使用指定的排序字段，如果没有指定则使用默认字段
  const sortField = params.sortField || defaultSortField;

  // 排序
  filteredItems.sort((a, b) => {
    const aValue = a[sortField as keyof LiquidationRankItem] as number;
    const bValue = b[sortField as keyof LiquidationRankItem] as number;
    const multiplier = params.sortDirection === 'desc' ? -1 : 1;

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return (aValue - bValue) * multiplier;
    }
    return 0;
  });

  // 移除市场份额计算，因为不在类型定义中

  // 重新分配排名
  filteredItems.forEach((item, index) => {
    item.rank = index + 1;
  });

  // 固定返回前30条数据
  const top30Items = filteredItems.slice(0, 30);

  // 计算汇总统计
  const majorCoinsAmount = allItems
    .filter((item) => item.coinType === 'Major')
    .reduce((sum, item) => sum + item.totalAmount, 0);

  const altcoinsAmount = allItems
    .filter((item) => item.coinType === 'Altcoin')
    .reduce((sum, item) => sum + item.totalAmount, 0);

  const totalLongAmount = allItems.reduce((sum, item) => sum + item.longAmount, 0);
  const totalShortAmount = allItems.reduce((sum, item) => sum + item.shortAmount, 0);

  return {
    items: top30Items,
    updateTime: new Date().toISOString(),
    timeRange: params.timeRange,
  };
}

// 实时消息模拟数据生成
let messageIdCounter = 1;
const mockMessages: RealTimeMessage[] = [];

function generateMockRealTimeMessage(): RealTimeMessage {
  const types: MessageType[] = ['info', 'success', 'warning', 'error', 'notification'];
  const type = types[Math.floor(Math.random() * types.length)];

  const messages = {
    info: {
      titles: ['系统信息', '数据更新', '连接状态', '配置变更'],
      contents: [
        '系统运行正常，所有服务状态良好',
        '数据已成功同步，最新数据已可用',
        'HTTP 连接已建立',
        '系统配置已更新，新设置已生效',
      ],
    },
    success: {
      titles: ['操作成功', '数据同步', '任务完成', '连接成功'],
      contents: [
        '数据查询操作已成功完成',
        '实时数据同步成功',
        '后台任务执行完成',
        '与数据源连接成功建立',
      ],
    },
    warning: {
      titles: ['性能警告', '数据延迟', '连接不稳定', '资源使用'],
      contents: [
        '系统响应时间较慢，请检查网络连接',
        '数据更新存在延迟，正在重新同步',
        'HTTP 连接不稳定，正在重连',
        'CPU 使用率较高，建议优化查询条件',
      ],
    },
    error: {
      titles: ['连接错误', '数据错误', '系统错误', '认证失败'],
      contents: [
        '无法连接到数据源，请检查网络设置',
        '数据格式错误，无法解析响应',
        '系统内部错误，请联系管理员',
        '用户认证失败，请重新登录',
      ],
    },
    notification: {
      titles: ['市场提醒', '价格警报', '清算提醒', '系统通知'],
      contents: [
        'BTC 价格突破关键阻力位 $50,000',
        '检测到大额清算事件，总金额超过 $10M',
        '市场波动加剧，建议关注风险控制',
        '系统将在 30 分钟后进行维护更新',
      ],
    },
  };

  const typeMessages = messages[type];
  const titleIndex = Math.floor(Math.random() * typeMessages.titles.length);
  const contentIndex = Math.floor(Math.random() * typeMessages.contents.length);

  return {
    id: `msg_${messageIdCounter++}`,
    type,
    title: typeMessages.titles[titleIndex],
    content: typeMessages.contents[contentIndex],
    timestamp: new Date().toISOString(),
    isRead: false,
    source: 'system',
    priority: type === 'error' ? 'high' : type === 'warning' ? 'medium' : 'low',
  };
}

// 初始化一些消息
for (let i = 0; i < 10; i++) {
  mockMessages.push(generateMockRealTimeMessage());
}

// 定期生成新消息（模拟实时数据）
setInterval(() => {
  if (mockMessages.length < 200) {
    // 增加最大消息数量限制
    mockMessages.unshift(generateMockRealTimeMessage());
  }
}, 3000); // 每3秒生成一条新消息，比轮询频率稍慢

// 定义MSW处理程序
export const handlers = [
  // 处理柱状图数据请求
  http.get('/api/charts/bar', () => {
    return HttpResponse.json(barChartData);
  }),

  // 处理折线图数据请求
  http.get('/api/charts/line', () => {
    return HttpResponse.json(lineChartData);
  }),

  // 处理饼图数据请求
  http.get('/api/charts/pie', () => {
    return HttpResponse.json(pieChartData);
  }),

  // 处理散点图数据请求
  http.get('/api/charts/scatter', () => {
    return HttpResponse.json(scatterChartData);
  }),

  // 处理所有图表数据请求 (POST 方法)
  http.post('/api/charts/all', async ({ request }) => {
    await request.json(); // 消费请求体但不使用 (startTime, endTime, selectedTimeZone)
    const newData = generateMockData();
    return HttpResponse.json(newData);
  }),

  // 处理所有图表数据请求 (GET 方法，向后兼容)
  http.get('/api/charts/all', () => {
    try {
      const newData = generateMockData();
      return HttpResponse.json(newData);
    } catch (error) {
      console.error('MSW: 处理 /api/charts/all 时出错:', error);
      // 返回基本的空数据结构
      return HttpResponse.json({
        barChart: { categories: [], series: [] },
        lineChart: { categories: [], series: [] },
        pieChart: { data: [] },
        scatterChart: { data: [] },
      });
    }
  }),

  // 处理统计卡片数据请求 (POST 方法)
  http.post('/api/dashboard/stats', async ({ request }) => {
    await request.json(); // 消费请求体但不使用 (startTime, endTime, selectedTimeZone)
    const statsData = {
      totalUsers: Math.floor(Math.random() * 50000) + 100000,
      monthlyRevenue: Math.floor(Math.random() * 200000) + 700000,
      conversionRate: parseFloat((Math.random() * 2 + 2.5).toFixed(2)),
      activeUsers: Math.floor(Math.random() * 30000) + 60000,
    };
    return HttpResponse.json(statsData);
  }),

  // 处理统计卡片数据请求 (GET 方法，向后兼容)
  http.get('/api/dashboard/stats', () => {
    try {
      const statsData = {
        totalUsers: Math.floor(Math.random() * 50000) + 100000,
        monthlyRevenue: Math.floor(Math.random() * 200000) + 700000,
        conversionRate: parseFloat((Math.random() * 2 + 2.5).toFixed(2)),
        activeUsers: Math.floor(Math.random() * 30000) + 60000,
      };
      return HttpResponse.json(statsData);
    } catch (error) {
      console.error('MSW: 处理 /api/dashboard/stats 时出错:', error);
      return HttpResponse.json(
        {
          totalUsers: 150000,
          monthlyRevenue: 900000,
          conversionRate: 3.5,
          activeUsers: 75000,
        },
        { status: 200 }
      );
    }
  }),

  // 处理仪表板概览数据请求
  http.get('/api/dashboard/overview', () => {
    const overviewData = {
      summary: {
        totalRevenue: Math.floor(Math.random() * 500000) + 1000000,
        totalOrders: Math.floor(Math.random() * 2000) + 7000,
        totalCustomers: Math.floor(Math.random() * 3000) + 10000,
        averageOrderValue: parseFloat((Math.random() * 50 + 120).toFixed(2)),
      },
      trends: {
        revenueGrowth: parseFloat((Math.random() * 10 + 8).toFixed(1)),
        orderGrowth: parseFloat((Math.random() * 8 + 5).toFixed(1)),
        customerGrowth: parseFloat((Math.random() * 12 + 10).toFixed(1)),
      },
    };
    return HttpResponse.json(overviewData);
  }),

  // 爆仓数据相关handlers
  // 处理快照数据请求
  http.post('/api/liquidation/snapshot', async ({ request }) => {
    await request.json(); // 消费请求体但不使用

    const data = generateMockLiquidationData(100);
    const stats = calculateLiquidationStats(data);

    const responseData = {
      data,
      stats,
    };

    return HttpResponse.json(responseData);
  }),

  // 处理趋势数据请求
  http.post('/api/liquidation/trend', async ({ request }) => {
    await request.json(); // 消费请求体但不使用

    // 根据timeRange生成相应的趋势数据
    const data = generateMockTrendData('7d'); // 默认7天数据

    return HttpResponse.json(data);
  }),

  // 处理详细趋势数据请求
  http.post('/api/liquidation/trend-detailed', async ({ request }) => {
    const body = (await request.json()) as { timeFrame: TrendTimeFrame; duration: TrendDuration };

    // API 以15分钟粒度返回数据，timeFrame 由前端图表组件处理
    const data = generateMockTrendData(body.duration);

    return HttpResponse.json(data);
  }),

  // 处理清算排行榜数据请求
  http.post('/api/liquidation/rank', async ({ request }) => {
    const params = (await request.json()) as RankQueryParams;

    // 设置默认参数
    const queryParams: RankQueryParams = {
      rankType: params.rankType || 'amount',
      sortField: params.sortField || (params.rankType === 'count' ? 'totalCount' : 'totalAmount'),
      sortDirection: params.sortDirection || 'desc',
      coinTypeFilter: params.coinTypeFilter || 'all',
      timeRange: params.timeRange || '24h',
    };

    const rankData = generateLiquidationRankData(queryParams);

    return HttpResponse.json(rankData);
  }),

  // 数据查询相关handlers
  // 处理数据查询请求
  http.post('/api/data/query', async ({ request }) => {
    const params = (await request.json()) as any;

    // 根据事件类型生成相应的参数
    const generateParametersForEventType = (eventType: string) => {
      const baseParams = [
        { parameterId: 'threshold', value: Math.floor(Math.random() * 5) },
        { parameterId: 'datetime', value: new Date().toISOString() },
      ];

      switch (eventType) {
        case 'TWAP_DETECTION':
          return [
            ...baseParams,
            { parameterId: 'twapId', value: generateULID() },
            { parameterId: 'amountUsd', value: Math.round(1000 + Math.random() * 50000) },
            { parameterId: 'side', value: Math.floor(Math.random() * 3) + 1 },
            { parameterId: 'market', value: Math.floor(Math.random() * 3) + 1 },
            { parameterId: 'duration', value: Math.floor(Math.random() * 20) + 1 },
            { parameterId: 'status', value: ['ongoing', 'started', 'expired'][Math.floor(Math.random() * 3)] },
          ];

        case 'LIQUIDATION_ORDER':
          return [
            ...baseParams,
            { parameterId: 'side', value: Math.random() > 0.5 ? 1 : 0 }, // 1=多头, 0=空头
            { parameterId: 'exchange', value: [4, 32, 512, 4096][Math.floor(Math.random() * 4)] },
            { parameterId: 'exchangeLabel', value: ['BITGET', 'COINBASE', 'KRAKEN', 'OKX'][Math.floor(Math.random() * 4)] },
            { parameterId: 'base', value: ['BTC', 'ETH', 'SOL', 'FOXY'][Math.floor(Math.random() * 4)] },
            { parameterId: 'pair', value: 'BTC-USDT-SWAP' },
            { parameterId: 'priceUsd', value: Math.round(Math.random() * 100000) / 100000 },
            { parameterId: 'amount', value: Math.round(Math.random() * 1000000) },
            { parameterId: 'amountUsd', value: Math.round(1000 + Math.random() * 50000) },
            { parameterId: 'coinType', value: ['BTC', 'ETH', 'SOL', 'BNB'].includes(baseParams[0]?.value) ? 'Major' : 'Altcoin' },
          ];

        case 'VOLUME_INCREASE':
          return [
            ...baseParams,
            { parameterId: 'side', value: Math.random() > 0.5 ? 1 : 0 }, // 1=买入, 0=卖出
            { parameterId: 'priceUsd', value: Math.round(Math.random() * 100000) / 100000 },
            { parameterId: 'previousPriceUsd', value: Math.round(Math.random() * 100000) / 100000 },
            { parameterId: 'exchange', value: [4, 32, 512, 4096][Math.floor(Math.random() * 4)] },
            { parameterId: 'exchangeLabel', value: ['BITGET', 'COINBASE', 'KRAKEN', 'OKX'][Math.floor(Math.random() * 4)] },
            { parameterId: 'base', value: ['BTC', 'ETH', 'SOL', 'BIGTIME'][Math.floor(Math.random() * 4)] },
            { parameterId: 'pair', value: 'BIGTIME/USDC' },
            { parameterId: 'aggregated', value: Math.random() > 0.5 },
            { parameterId: 'priceDirection', value: Math.floor(Math.random() * 2) + 1 },
            { parameterId: 'recentVolumeSumUsd', value: Math.round(10000 + Math.random() * 100000) },
            { parameterId: 'percentAbnormal', value: Math.round(Math.random() * 1000) },
            { parameterId: 'amountUsd', value: Math.round(5000 + Math.random() * 95000) }, // 交易量增长金额
            { parameterId: 'increasePercent', value: Math.round((Math.random() * 200 + 50) * 100) / 100 }, // 50-250% 增长
          ];

        default:
          return baseParams;
      }
    };

    // 生成符合规范的市场事件数据
    const generateMockMarketEvents = () => {
      const events = [];

      // 根据入参确定时间范围
      let startTime: Date;
      let endTime: Date;

      if (params.startDate && params.endDate) {
        startTime = new Date(params.startDate);
        endTime = new Date(params.endDate);
      } else if (params.startDate) {
        startTime = new Date(params.startDate);
        endTime = new Date(startTime.getTime() + 24 * 60 * 60 * 1000); // 默认1天范围
      } else if (params.endDate) {
        endTime = new Date(params.endDate);
        startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000); // 默认1天范围
      } else {
        // 如果没有指定日期，默认生成最近2天的数据
        const now = new Date();
        endTime = now;
        startTime = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
      }

      // 市场事件类型
      const marketEventTypes = [
        'TWAP_DETECTION',
        'LIQUIDATION_ORDER',
        'EXCHANGE_TRANSFER',
        'ORDERBOOK_IMBALANCE',
        'FUNDING_RATE_SWITCH',
        'EXTREME_FUNDING_RATE',
        'VOLUME_INCREASE',
        'OPEN_INTEREST_VARIATION'
      ];

      // 货币ID池
      const currencyIds = [
        '01J8QJG2T7RR1E2V0NFXFGF2RR',
        '01HAF64YPQCN0VFY2D0SRQQ0MD',
        '01HAF6501AXTMRSK1F836B8Q3F',
        '01HWKTT6BZ7YKZZ545HTPD22N7',
        '01HEPMMG27T0J8Y1RKW74XHXTM',
        '01JP2G8RNR4JBJP81JS5JNJQ89',
        '01HAF64ZT94SKECAMZ9C8TD0J1',
        '01HCVQSJ0DG9QV5YNV9NP69KQ6',
        '01HAF64ZYT42GP9AK8MY45CK7T',
        '01JC303X64R02BZP8P6CZD1BCE'
      ];



      // 根据时间范围生成时间段数据
      const timeSlots = [];
      const timeRangeMs = endTime.getTime() - startTime.getTime();
      const slotCount = Math.min(20, Math.max(5, Math.floor(timeRangeMs / (2 * 60 * 60 * 1000)))); // 每2小时一个时间段

      for (let i = 0; i < slotCount; i++) {
        const slotStart = new Date(startTime.getTime() + (i * timeRangeMs) / slotCount);
        timeSlots.push(slotStart);
      }

      timeSlots.forEach((slotStart) => {
        const recordCount = Math.floor(Math.random() * 5) + 2;

        for (let j = 0; j < recordCount; j++) {
          const randomMinutes = Math.floor(Math.random() * 15);
          const randomSeconds = Math.floor(Math.random() * 60);
          const eventTime = new Date(
            slotStart.getTime() + randomMinutes * 60 * 1000 + randomSeconds * 1000
          );

          const eventType = marketEventTypes[Math.floor(Math.random() * marketEventTypes.length)];
          const currencyId = currencyIds[Math.floor(Math.random() * currencyIds.length)];

          // 根据事件类型生成相应的参数
          const parameters = generateParametersForEventType(eventType);

          events.push({
            id: generateULID(),
            marketEventType: eventType,
            date: Math.floor(eventTime.getTime() / 1000), // Unix时间戳（秒）
            parameters,
            currency: currencyId,
          });
        }
      });

      // 添加更多随机分布的数据
      const additionalEventCount = Math.floor(timeRangeMs / (60 * 60 * 1000)); // 每小时1个额外事件
      for (let i = 0; i < Math.min(30, additionalEventCount); i++) {
        const randomTime = new Date(startTime.getTime() + Math.random() * timeRangeMs);
        const eventType = marketEventTypes[Math.floor(Math.random() * marketEventTypes.length)];
        const currencyId = currencyIds[Math.floor(Math.random() * currencyIds.length)];
        const parameters = generateParametersForEventType(eventType);

        events.push({
          id: generateULID(),
          marketEventType: eventType,
          date: Math.floor(randomTime.getTime() / 1000),
          parameters,
          currency: currencyId,
        });
      }

      return events.sort((a, b) => b.date - a.date);
    };



    const mockEvents = generateMockMarketEvents();

    // 应用筛选条件
    let filteredData = [...mockEvents];

    // 根据marketEventType筛选
    if (params.type && Array.isArray(params.type) && params.type.length > 0) {
      filteredData = filteredData.filter((event: any) => params.type.includes(event.marketEventType));
    } else if (params.type && typeof params.type === 'string' && params.type !== '全部') {
      filteredData = filteredData.filter((event: any) => event.marketEventType === params.type);
    }

    // 返回符合规范的响应格式
    return HttpResponse.json({
      code: 1000,
      message: 'success',
      data: filteredData,
    });
  }),

  // 处理数据导出请求
  http.post('/api/data/export', async ({ request }) => {
    await request.json(); // 消费请求体但不使用

    // 生成 CSV 内容
    const headers = ['ID', '名称', '类型', '状态', '创建时间'];
    const csvRows = [
      headers.join(','),
      '1,"数据项A1","类型A","有效","2024-01-01 10:00:00"',
      '2,"数据项B2","类型B","无效","2024-01-02 11:00:00"',
      '3,"数据项C3","类型C","有效","2024-01-03 12:00:00"',
    ];

    const csvContent = csvRows.join('\n');

    return new HttpResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv;charset=utf-8;',
        'Content-Disposition': 'attachment; filename="export.csv"',
      },
    });
  }),

  // 实时消息相关handlers
  // 获取实时消息列表
  http.get('/api/real-time-messages', ({ request }) => {
    const url = new URL(request.url);
    const lastUpdateTime = url.searchParams.get('lastUpdateTime');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const type = url.searchParams.get('type');
    const unreadOnly = url.searchParams.get('unreadOnly') === 'true';

    let filteredMessages = [...mockMessages];

    // 过滤条件
    if (lastUpdateTime) {
      const lastTime = new Date(lastUpdateTime);
      filteredMessages = filteredMessages.filter((msg) => new Date(msg.timestamp) > lastTime);
    }

    if (type && type !== 'all') {
      filteredMessages = filteredMessages.filter((msg) => msg.type === type);
    }

    if (unreadOnly) {
      filteredMessages = filteredMessages.filter((msg) => !msg.isRead);
    }

    // 限制返回数量
    const limitedMessages = filteredMessages.slice(0, limit);

    return HttpResponse.json({
      messages: limitedMessages,
      total: filteredMessages.length,
      page: 1,
      limit,
      hasMore: filteredMessages.length > limit,
      serverTime: new Date().toISOString(),
    });
  }),

  // 标记消息为已读
  http.put('/api/real-time-messages/:messageId/read', ({ params }) => {
    const messageId = params.messageId as string;
    const message = mockMessages.find((msg) => msg.id === messageId);

    if (message) {
      message.isRead = true;
      return HttpResponse.json({ success: true });
    }

    return HttpResponse.json({ success: false }, { status: 404 });
  }),

  // 批量标记消息为已读
  http.put('/api/real-time-messages/batch-read', async ({ request }) => {
    const { messageIds } = (await request.json()) as { messageIds: string[] };
    let count = 0;

    messageIds.forEach((id) => {
      const message = mockMessages.find((msg) => msg.id === id);
      if (message && !message.isRead) {
        message.isRead = true;
        count++;
      }
    });

    return HttpResponse.json({ success: true, count });
  }),

  // 标记所有消息为已读
  http.put('/api/real-time-messages/read-all', () => {
    let count = 0;
    mockMessages.forEach((msg) => {
      if (!msg.isRead) {
        msg.isRead = true;
        count++;
      }
    });

    return HttpResponse.json({ success: true, count });
  }),

  // 删除消息
  http.delete('/api/real-time-messages/:messageId', ({ params }) => {
    const messageId = params.messageId as string;
    const index = mockMessages.findIndex((msg) => msg.id === messageId);

    if (index !== -1) {
      mockMessages.splice(index, 1);
      return HttpResponse.json({ success: true });
    }

    return HttpResponse.json({ success: false }, { status: 404 });
  }),

  // 批量删除消息
  http.post('/api/real-time-messages/batch-delete', async ({ request }) => {
    const { messageIds } = (await request.json()) as { messageIds: string[] };
    let count = 0;

    messageIds.forEach((id) => {
      const index = mockMessages.findIndex((msg) => msg.id === id);
      if (index !== -1) {
        mockMessages.splice(index, 1);
        count++;
      }
    });

    return HttpResponse.json({ success: true, count });
  }),

  // 清除所有消息
  http.delete('/api/real-time-messages/clear-all', () => {
    const count = mockMessages.length;
    mockMessages.length = 0; // 清空数组
    return HttpResponse.json({ success: true, count });
  }),

  // 获取消息统计信息
  http.get('/api/real-time-messages/stats', () => {
    const total = mockMessages.length;
    const unread = mockMessages.filter((msg) => !msg.isRead).length;
    const byType = mockMessages.reduce(
      (acc, msg) => {
        acc[msg.type] = (acc[msg.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    return HttpResponse.json({
      total,
      unread,
      byType,
      lastUpdateTime: new Date().toISOString(),
    });
  }),

  // 历史数据查询相关handlers
  // 处理历史数据查询请求
  http.post('/api/historical/query', async ({ request }) => {
    const params = (await request.json()) as any;

    // 生成历史数据
    const generateHistoricalData = () => {
      const symbols = params.selectedCoins || ['BTC', 'ETH', 'SOL', 'BNB', 'ADA'];
      // 适配单个数据类型或数组类型
      const dataTypes = Array.isArray(params.dataTypes)
        ? params.dataTypes
        : params.dataTypes
          ? [params.dataTypes]
          : ['LIQUIDATION_ORDER', 'VOLUME_INCREASE', 'TWAP_DETECTION'];
      const items: any[] = [];

      // 计算日期范围
      const startDate = new Date(params.startDate || '2024-01-01');
      const endDate = new Date(params.endDate || new Date().toISOString().split('T')[0]);
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      // 为每一天生成数据
      for (let day = 0; day <= daysDiff; day++) {
        const currentDate = new Date(startDate.getTime() + day * 24 * 60 * 60 * 1000);
        const dateStr = currentDate.toISOString().split('T')[0];

        // 为每个币种生成数据
        symbols.forEach((symbol: string) => {
          dataTypes.forEach((dataType: string) => {
            // 每个币种每种数据类型每天生成1-3条记录
            const recordCount = Math.floor(Math.random() * 3) + 1;

            for (let i = 0; i < recordCount; i++) {
              const amount = Math.round(10000 + Math.random() * 990000);
              const quantity = Math.round(Math.random() * 1000);
              const price = Math.round((Math.random() * 100000) * 100) / 100;
              const changePercent = (Math.random() - 0.5) * 20; // -10% to +10%
              const volume = Math.round(amount * (0.8 + Math.random() * 0.4)); // 80%-120% of amount
              const marketCap = Math.round(amount * (10 + Math.random() * 20)); // 10x-30x of amount

              // 为方向性数据类型生成方向信息
              const isDirectionalType = dataType === 'LIQUIDATION_ORDER' || dataType === 'VOLUME_INCREASE';
              const side = isDirectionalType ? (Math.random() > 0.5 ? 1 : 0) : undefined; // 1=多头/买入, 0=空头/卖出

              items.push({
                id: `hist-${day}-${symbol}-${dataType}-${i}`,
                date: dateStr,
                symbol,
                name: `${symbol} Token`,
                type: dataType,
                amount,
                quantity,
                price,
                changePercent,
                volume,
                marketCap,
                timestamp: Math.floor(currentDate.getTime() / 1000),
                metadata: {
                  exchange: ['Binance', 'Coinbase', 'Kraken', 'OKX'][Math.floor(Math.random() * 4)],
                  pair: `${symbol}/USDT`,
                  // 添加方向性信息到 metadata
                  ...(isDirectionalType && { side }),
                  // 为清算数据添加额外的清算特定字段
                  ...(dataType === 'LIQUIDATION_ORDER' && {
                    liquidationType: side === 1 ? 'long' : 'short',
                    threshold: [10000, 50000, 100000, 500000, 1000000][Math.floor(Math.random() * 5)],
                    coinType: ['BTC', 'ETH', 'SOL', 'BNB'].includes(symbol) ? 'Major' : 'Altcoin',
                  }),
                  // 为交易量增长数据添加额外字段
                  ...(dataType === 'VOLUME_INCREASE' && {
                    volumeType: side === 1 ? 'buy' : 'sell',
                    increasePercent: Math.round((Math.random() * 200 + 50) * 100) / 100, // 50-250% 增长
                    timeWindow: ['1h', '4h', '12h', '24h'][Math.floor(Math.random() * 4)],
                  }),
                }
              });
            }
          });
        });
      }

      return items.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    };

    const items = generateHistoricalData();
    const total = items.length;

    return HttpResponse.json({
      status: 'success',
      data: {
        items: items,
        total,
        queryParams: params,
      }
    });
  }),

  // 健康检查端点
  // 处理健康检查 GET 请求
  http.get('/api/health', () => {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: Math.floor(Math.random() * 86400), // 随机运行时间（秒）
      services: {
        database: 'connected',
        cache: 'connected',
        api: 'running',
      },
      checks: {
        memory: {
          status: 'ok',
          usage: Math.floor(Math.random() * 30 + 20) + '%', // 20-50% 使用率
        },
        disk: {
          status: 'ok',
          usage: Math.floor(Math.random() * 20 + 10) + '%', // 10-30% 使用率
        },
        response_time: {
          status: 'ok',
          value: Math.floor(Math.random() * 50 + 10) + 'ms', // 10-60ms 响应时间
        },
      },
    };

    return HttpResponse.json(healthData);
  }),

  // 处理健康检查 HEAD 请求（ErrorRecoveryHelper.svelte 使用）
  http.head('/api/health', () => {
    // HEAD 请求只返回状态码和响应头，不返回响应体
    return new HttpResponse(null, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-Health-Status': 'healthy',
        'X-Timestamp': new Date().toISOString(),
        'X-Service-Version': '1.0.0',
      },
    });
  }),

  // Coinact API 模拟 - 获取货币信息
  http.get('https://api.coinact.gg/catalog/currencies/:currencyId', ({ params }) => {
    const { currencyId } = params;

    // 模拟货币信息数据
    const mockCurrencies: Record<string, any> = {
      '01JP2G8RNR4JBJP81JS5JNJQ89': {
        id: '01JP2G8RNR4JBJP81JS5JNJQ89',
        name: 'Strix',
        symbol: 'STRX',
        image: 'https://coin-images.coingecko.com/coins/images/14899/large/RwdVsGcw_400x400.jpg?**********'
      },
      '01HEPMMG27T0J8Y1RKW74XHXTM': {
        id: '01HEPMMG27T0J8Y1RKW74XHXTM',
        name: 'Bitcoin',
        symbol: 'BTC',
        image: 'https://coin-images.coingecko.com/coins/images/1/large/bitcoin.png?**********'
      },
      '01HAF64YMSYF7BPH8JJRWVATT5': {
        id: '01HAF64YMSYF7BPH8JJRWVATT5',
        name: 'Ethereum',
        symbol: 'ETH',
        image: 'https://coin-images.coingecko.com/coins/images/279/large/ethereum.png?1696501628'
      },
      '01HAF64YNKTZJ3EEF6EHFAHVFF': {
        id: '01HAF64YNKTZJ3EEF6EHFAHVFF',
        name: 'XRP',
        symbol: 'XRP',
        image: 'https://coin-images.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png?1696501442'
      },
      '01HAF64YN0PNRDNMNB2K0AH6DY': {
        id: '01HAF64YN0PNRDNMNB2K0AH6DY',
        name: 'Tether',
        symbol: 'USDT',
        image: 'https://coin-images.coingecko.com/coins/images/325/large/Tether.png?1696501661'
      }
    };

    const currency = mockCurrencies[currencyId as string];

    if (!currency) {
      return new HttpResponse(null, { status: 404 });
    }

    return HttpResponse.json(currency);
  }),

  // Coinact API 模拟 - 币种搜索
  http.get('https://api.coinact.gg/ranking/coins', ({ request }) => {
    const url = new URL(request.url);
    const name = url.searchParams.get('name');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    // 模拟搜索结果
    const allCoins = [
      {
        id: '01HEPMMG27T0J8Y1RKW74XHXTM',
        name: 'Bitcoin',
        symbol: 'btc',
        image: 'https://coin-images.coingecko.com/coins/images/1/large/bitcoin.png?**********',
        rank: '1',
        marketCap: 2347652819439,
        price: 117711.8495731225,
        priceVariation: { h1: -0.094, h24: -0.861 }
      },
      {
        id: '01HAF64YMSYF7BPH8JJRWVATT5',
        name: 'Ethereum',
        symbol: 'eth',
        image: 'https://coin-images.coingecko.com/coins/images/279/large/ethereum.png?1696501628',
        rank: '2',
        marketCap: 458611418819,
        price: 3766.9604656845,
        priceVariation: { h1: -0.549, h24: -1.95 }
      },
      {
        id: '01JP2G8RNR4JBJP81JS5JNJQ89',
        name: 'Strix',
        symbol: 'strx',
        image: 'https://coin-images.coingecko.com/coins/images/14899/large/RwdVsGcw_400x400.jpg?**********',
        rank: '150',
        marketCap: 50000000,
        price: 0.08130785714285714,
        priceVariation: { h1: 2.5, h24: 5.2 }
      }
    ];

    let filteredCoins = allCoins;
    if (name) {
      filteredCoins = allCoins.filter(coin =>
        coin.name.toLowerCase().includes(name.toLowerCase()) ||
        coin.symbol.toLowerCase().includes(name.toLowerCase())
      );
    }

    const limitedCoins = filteredCoins.slice(0, limit);

    return HttpResponse.json({
      page: 0,
      total: filteredCoins.length,
      count: limitedCoins.length,
      items: limitedCoins
    });
  }),
];
