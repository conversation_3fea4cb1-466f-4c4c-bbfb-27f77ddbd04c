<!--
  TimeRangeSelector 组件

  功能：
  - 提供快速时间范围选择（今天、昨天、本周等）
  - 支持自定义时间范围输入（日期时间选择器）
  - 集成时区选择和管理功能
  - 数据验证和错误处理
  - 防抖优化和性能提升

  使用场景：
  - 数据查询界面的时间范围筛选
  - 仪表板的时间范围控制
  - 报表生成的时间参数设置

  @component TimeRangeSelector
  <AUTHOR> Agent
  @version 2.0.0 (重构优化版本)
-->

<script lang="ts">
  import { onMount } from 'svelte';

  // shadcn-svelte 组件导入
  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent } from '$lib/components/ui/card';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Popover, PopoverContent, PopoverTrigger } from '$lib/components/ui/popover';
  import { findTimezoneById, getDefaultTimezone } from '$lib/constants/timezones';
  import type { TimeRange } from '$lib/types';

  import TimezoneSelector from './TimezoneSelector.svelte';

  interface Props {
    /** 选中的时间范围 */
    selectedTimeRange: TimeRange | string;
    /** 自定义开始时间 */
    customStartTime?: string;
    /** 自定义结束时间 */
    customEndTime?: string;
    /** 选中的时区 */
    selectedTimeZone?: string;
    /** 时间范围变更回调 */
    onTimeRangeChange: (params: {
      selectedTimeRange: TimeRange | string;
      customStartTime: string;
      customEndTime: string;
      selectedTimeZone: string;
    }) => void;
  }

  const {
    selectedTimeRange = 'today',
    customStartTime = '',
    customEndTime = '',
    selectedTimeZone = 'UTC',
    onTimeRangeChange,
  }: Props = $props();

  let isExpanded = $state(false);
  let showTimeZoneSettings = $state(false);
  let internalStartTime = $state(customStartTime);
  let internalEndTime = $state(customEndTime);
  let internalTimeRange = $state(selectedTimeRange);
  let internalTimeZone = $state(selectedTimeZone);

  // 用户体验状态
  let isApplying = $state(false);
  let hasValidationError = $state(false);
  let validationMessage = $state('');

  // 时间范围标签映射
  const TIME_RANGE_LABELS: Record<string, string> = {
    today: '今天',
    yesterday: '昨天',
    week: '本周',
    month: '本月',
    last24h: '最近24小时',
    lastWeek: '最近一周',
    lastMonth: '最近一月',
  };

  // 快速选择选项
  const QUICK_RANGES: TimeRange[] = [
    'today',
    'yesterday',
    'week',
    'month',
    'last24h',
    'lastWeek',
    'lastMonth'
  ];

  // Helper function to format Date object to YYYY-MM-DDTHH:mm for datetime-local input
  function formatDateTimeLocal(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  // Function to calculate start and end times for quick ranges
  function calculateTimeRange(range: TimeRange | string): { start: string; end: string } {
    try {
      const now = new Date();

      // 验证当前时间是否有效
      if (isNaN(now.getTime())) {
        console.warn('Invalid current date, using fallback');
        return { start: internalStartTime, end: internalEndTime };
      }

      const start = new Date(now);
      const end = new Date(now);

      switch (range) {
        case 'today':
          start.setHours(0, 0, 0, 0);
          end.setHours(23, 59, 59, 999);
          break;
        case 'yesterday':
          start.setDate(now.getDate() - 1);
          start.setHours(0, 0, 0, 0);
          end.setDate(now.getDate() - 1);
          end.setHours(23, 59, 59, 999);
          break;
        case 'week': {
          const dayOfWeek = now.getDay(); // Sunday - Saturday : 0 - 6
          start.setDate(now.getDate() - dayOfWeek); // Go to Sunday
          start.setHours(0, 0, 0, 0);
          end.setDate(start.getDate() + 6); // Go to Saturday
          end.setHours(23, 59, 59, 999);
          break;
        }
        case 'month':
          start.setDate(1);
          start.setHours(0, 0, 0, 0);
          end.setMonth(now.getMonth() + 1, 0); // Last day of current month
          end.setHours(23, 59, 59, 999);
          break;
        case 'last24h':
          start.setTime(now.getTime() - 24 * 60 * 60 * 1000); // 更精确的24小时前
          break;
        case 'lastWeek':
          start.setTime(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 更精确的7天前
          break;
        case 'lastMonth':
          start.setMonth(now.getMonth() - 1);
          break;
        default:
          // For 'custom' or unknown, return current values
          return { start: internalStartTime, end: internalEndTime };
      }

      // 验证计算结果
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        console.warn(`Invalid date calculation for range: ${range}`);
        return { start: internalStartTime, end: internalEndTime };
      }

      // 确保开始时间不晚于结束时间
      if (start.getTime() > end.getTime()) {
        console.warn(`Start time is after end time for range: ${range}`);
        [start.setTime(end.getTime()), end.setTime(start.getTime())];
      }

      return {
        start: formatDateTimeLocal(start),
        end: formatDateTimeLocal(end),
      };
    } catch (error) {
      console.error('Error calculating time range:', error);
      return { start: internalStartTime, end: internalEndTime };
    }
  }

  // 当前时区信息
  const currentTimezone = $derived(
    findTimezoneById(internalTimeZone) || getDefaultTimezone()
  );

  // 缓存格式化的日期字符串（只显示到分钟级别）
  const formatDateSafe = (dateStr: string): string => {
    if (!dateStr) return '未选择';
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '无效日期';

      // 使用 toLocaleString 的 options 参数控制显示格式，只显示到分钟级别
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false // 使用24小时制
      });
    } catch {
      return '无效日期';
    }
  };

  // 计算显示的时间范围文本
  const displayTimeRange = $derived.by(() => {
    let rangeText: string;
    if (internalTimeRange === 'custom') {
      const start = formatDateSafe(internalStartTime);
      const end = formatDateSafe(internalEndTime);
      rangeText = `从 ${start} 到 ${end}`;
    } else {
      rangeText = TIME_RANGE_LABELS[internalTimeRange as string] || '选择时间范围';
    }

    return `${rangeText} (${currentTimezone.offset})`;
  });

  // 防抖的状态同步
  let syncTimeoutId: ReturnType<typeof setTimeout> | undefined;

  // 同步内部状态到外部 props (带防抖)
  $effect(() => {
    // 清除之前的定时器
    if (syncTimeoutId) {
      clearTimeout(syncTimeoutId);
    }

    // 设置新的防抖定时器
    syncTimeoutId = setTimeout(() => {
      onTimeRangeChange({
        selectedTimeRange: internalTimeRange,
        customStartTime: internalStartTime,
        customEndTime: internalEndTime,
        selectedTimeZone: internalTimeZone,
      });
    }, 100); // 100ms 防抖

    // 清理函数
    return () => {
      if (syncTimeoutId) {
        clearTimeout(syncTimeoutId);
      }
    };
  });

  // 验证时间范围
  function validateTimeRange(start: string, end: string): { isValid: boolean; message: string } {
    if (!start || !end) {
      return { isValid: false, message: '请选择开始和结束时间' };
    }

    try {
      const startDate = new Date(start);
      const endDate = new Date(end);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return { isValid: false, message: '时间格式无效' };
      }

      if (startDate.getTime() >= endDate.getTime()) {
        return { isValid: false, message: '开始时间必须早于结束时间' };
      }

      // 检查时间范围是否过长（例如超过1年）
      const maxRange = 365 * 24 * 60 * 60 * 1000; // 1年
      if (endDate.getTime() - startDate.getTime() > maxRange) {
        return { isValid: false, message: '时间范围不能超过1年' };
      }

      return { isValid: true, message: '' };
    } catch {
      return { isValid: false, message: '时间验证失败' };
    }
  }

  function selectQuickRange(range: TimeRange) {
    try {
      const updatedRange = calculateTimeRange(range);
      internalTimeRange = range;
      internalStartTime = updatedRange.start;
      internalEndTime = updatedRange.end;

      // 清除验证错误
      hasValidationError = false;
      validationMessage = '';
    } catch (error) {
      console.error('Error selecting quick range:', error);
      hasValidationError = true;
      validationMessage = '选择时间范围时出错';
    }
  }

  async function applyCustomRange() {
    isApplying = true;

    try {
      const validation = validateTimeRange(internalStartTime, internalEndTime);

      if (!validation.isValid) {
        hasValidationError = true;
        validationMessage = validation.message;
        return;
      }

      internalTimeRange = 'custom';
      hasValidationError = false;
      validationMessage = '';
      isExpanded = false; // 关闭面板
    } catch (error) {
      console.error('Error applying custom range:', error);
      hasValidationError = true;
      validationMessage = '应用时间范围时出错';
    } finally {
      isApplying = false;
    }
  }

  function handleTimeZoneChange(newTimeZone: string) {
    internalTimeZone = newTimeZone;
  }



  function toggleTimeZoneSettings() {
    showTimeZoneSettings = !showTimeZoneSettings;
  }

  onMount(() => {
    // Popover组件会自动处理点击外部关闭，这里不需要额外的事件监听器
  });
</script>

<div class="relative inline-block">
  <Popover bind:open={isExpanded}>
    <PopoverTrigger>
      <Button variant="outline" size="sm" class="justify-start text-left font-normal">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="mr-2 h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
        {displayTimeRange}
      </Button>
    </PopoverTrigger>

    <PopoverContent
      class="w-[320px] max-w-[calc(100vw-2rem)] p-0 sm:w-[480px]"
      align="end"
      side="bottom"
      sideOffset={4}
      alignOffset={-4}
      avoidCollisions={true}
      collisionPadding={16}
    >
      <Card class="border-0 shadow-none">
        <CardContent class="p-4">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <!-- 左侧面板: 绝对时间范围 -->
            <div class="space-y-3">
              <h3 class="text-foreground text-sm font-medium">绝对时间范围</h3>

              <div class="space-y-2">
                <Label for="start-time" class="text-xs">从:</Label>
                <Input
                  id="start-time"
                  type="datetime-local"
                  bind:value={internalStartTime}
                  class="text-xs"
                />
              </div>

              <div class="space-y-2">
                <Label for="end-time" class="text-xs">到:</Label>
                <Input
                  id="end-time"
                  type="datetime-local"
                  bind:value={internalEndTime}
                  class="text-xs"
                />
              </div>

              <!-- 验证错误显示 -->
              {#if hasValidationError}
                <div class="text-destructive text-xs p-2 bg-destructive/10 rounded-md">
                  {validationMessage}
                </div>
              {/if}

              <Button
                onclick={applyCustomRange}
                size="sm"
                class="w-full"
                disabled={isApplying}
              >
                {#if isApplying}
                  <svg class="mr-2 h-4 w-4 animate-spin" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  应用中...
                {:else}
                  应用时间范围
                {/if}
              </Button>
            </div>

            <!-- 右侧面板: 快速选择 -->
            <div class="space-y-3">
              <h3 class="text-foreground text-sm font-medium">快速选择</h3>
              <div class="space-y-1">
                {#each QUICK_RANGES as range (range)}
                  <Button
                    onclick={() => selectQuickRange(range)}
                    variant={internalTimeRange === range ? 'default' : 'ghost'}
                    size="sm"
                    class="w-full justify-start text-xs"
                  >
                    {TIME_RANGE_LABELS[range]}
                  </Button>
                {/each}
              </div>
            </div>
          </div>

          <!-- 底部时区设置 -->
          <div class="border-border mt-3 border-t pt-3">
            <TimezoneSelector
              selectedTimezone={internalTimeZone}
              onTimezoneChange={handleTimeZoneChange}
              showSettings={showTimeZoneSettings}
              onToggleSettings={toggleTimeZoneSettings}
            />
          </div>
        </CardContent>
      </Card>
    </PopoverContent>
  </Popover>
</div>
