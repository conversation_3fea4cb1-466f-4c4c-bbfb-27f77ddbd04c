<script lang="ts">
  import { Card, CardContent } from '$lib/components/ui/card';
  import CurrencyIcon from '$lib/components/features/CurrencyIcon.svelte';
  import { TrendingUp, TrendingDown } from 'lucide-svelte';

  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    formatUsdAmount,
    formatPercentage,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取交易量增长相关参数
  const base = getParameterValue(event, 'base') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const priceDirection = getParameterValue(event, 'priceDirection') as number;
  const recentVolumeSumUsd = getParameterValue(event, 'recentVolumeSumUsd') as number;
  const percentAbnormal = getParameterValue(event, 'percentAbnormal') as number;
  const priceUsd = getParameterValue(event, 'priceUsd') as number;
  // 价格变化方向和样式
  const isPriceUp = priceDirection === 1;
  const priceChangeColor = isPriceUp ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3">
    <!-- 主要信息行：货币图标 + 基本信息 -->
    <div class="flex items-center justify-between mb-2">
      <!-- 左侧：货币图标 + 币种信息 -->
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <!-- 货币图标 -->
        <CurrencyIcon
          currencyId={event.currency}
          symbol={base}
          size="size-6"
          class="flex-shrink-0"
        />

        <!-- 币种和交易对 -->
        <div class="min-w-0 flex-1">
          <div class="text-foreground font-semibold text-sm truncate">
            {base || '未知'}
          </div>
          {#if pair}
            <div class="text-muted-foreground text-xs truncate">
              {pair}
            </div>
          {/if}
        </div>
      </div>

      <!-- 右侧：价格变化指示 -->
      <div class="flex items-center space-x-1 flex-shrink-0">
        {#if isPriceUp}
          <TrendingUp class="size-4 {priceChangeColor}" />
        {:else}
          <TrendingDown class="size-4 {priceChangeColor}" />
        {/if}
        <span class="text-xs font-medium {priceChangeColor}">
          {isPriceUp ? '↗' : '↘'}
        </span>
      </div>
    </div>

    <!-- 关键数据文案 -->
    <div class="mt-2">
      <p class="text-sm text-foreground leading-relaxed">
        <span class="font-semibold">{base}</span> volume increased by
        <span class="font-semibold {priceChangeColor}">
          {formatPercentage(percentAbnormal)}%
        </span>
        (<span class="font-semibold">{formatUsdAmount(recentVolumeSumUsd)}</span>)
        while the price hits
        <span class="font-semibold {priceChangeColor}">
          {formatUsdAmount(priceUsd)}
        </span>
      </p>
    </div>
  </CardContent>
</Card>
