[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / LiquidationStats

# Interface: LiquidationStats

Defined in: src/lib/types/liquidation.ts:15

## Properties

### longAmount

> **longAmount**: `number`

Defined in: src/lib/types/liquidation.ts:18

---

### longShortRatio

> **longShortRatio**: `number`

Defined in: src/lib/types/liquidation.ts:20

---

### shortAmount

> **shortAmount**: `number`

Defined in: src/lib/types/liquidation.ts:19

---

### totalAmount

> **totalAmount**: `number`

Defined in: src/lib/types/liquidation.ts:16

---

### totalCount

> **totalCount**: `number`

Defined in: src/lib/types/liquidation.ts:17
