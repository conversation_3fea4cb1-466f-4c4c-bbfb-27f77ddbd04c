[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [services](../README.md) / createApiService

# Function: createApiService()

> **createApiService**(`baseURL?`): [`BaseApiService`](../classes/BaseApiService.md)

Defined in: src/lib/services/api/base.ts:142

创建默认的 API 服务实例

## Parameters

### baseURL?

`string`

## Returns

[`BaseApiService`](../classes/BaseApiService.md)
