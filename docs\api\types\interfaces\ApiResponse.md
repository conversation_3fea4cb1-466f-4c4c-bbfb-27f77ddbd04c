[**Svelte Demo API Documentation**](../../README.md)

---

[Svelte Demo API Documentation](../../README.md) / [types](../README.md) / ApiResponse

# Interface: ApiResponse\<T\>

Defined in: src/lib/types/api.ts:6

统一的 API 响应格式

## Type Parameters

### T

`T`

## Properties

### data

> **data**: `T`

Defined in: src/lib/types/api.ts:7

---

### message?

> `optional` **message**: `string`

Defined in: src/lib/types/api.ts:9

---

### status

> **status**: `"error"` \| `"success"`

Defined in: src/lib/types/api.ts:8
