<script lang="ts">
  import { ChevronDown, ChevronUp, X, Check } from 'lucide-svelte';
  import { onMount } from 'svelte';

  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import type { MarketEventType } from '$lib/types';

  interface Props {
    selectedTypes?: MarketEventType[];
    placeholder?: string;
    disabled?: boolean;
    class?: string;
    visibleBadgeCount?: number;
    hiddenCount?: number;
    onSelectionChange?: (selectedTypes: MarketEventType[]) => void;
    onTypeAdd?: (type: MarketEventType) => void;
    onTypeRemove?: (type: MarketEventType) => void;
  }

  let {
    selectedTypes = $bindable([]),
    placeholder = '请选择类型',
    disabled = false,
    class: className = '',
    visibleBadgeCount = selectedTypes.length,
    hiddenCount = 0,
    onSelectionChange,
    onTypeAdd,
    onTypeRemove,
  }: Props = $props();

  let isOpen = $state(false);
  let containerRef = $state<HTMLDivElement | undefined>(undefined);

  // 类型选项列表（使用中文标签）
  const typeOptions: { value: MarketEventType; label: string }[] = [
    { value: 'TWAP_DETECTION', label: 'TWAP检测' },
    { value: 'LIQUIDATION_ORDER', label: '清算订单' },
    { value: 'EXCHANGE_TRANSFER', label: '交易所转账' },
    { value: 'ORDERBOOK_IMBALANCE', label: '订单簿失衡' },
    { value: 'FUNDING_RATE_SWITCH', label: '资金费率切换' },
    { value: 'EXTREME_FUNDING_RATE', label: '极端资金费率' },
    { value: 'VOLUME_INCREASE', label: '交易量增长' },
    { value: 'OPEN_INTEREST_VARIATION', label: '持仓量变化' },
  ];

  // 获取类型的首字母缩写
  function getTypeAbbreviation(type: MarketEventType): string {
    return type.split('_').map((word: string) => word.charAt(0)).join('');
  }

  // 处理选项选择
  function handleSelect(typeValue: MarketEventType) {
    if (selectedTypes.includes(typeValue)) {
      // 如果已选中，则取消选择
      handleRemove(typeValue);
    } else {
      // 如果未选中，则添加选择
      const newSelectedTypes = [...selectedTypes, typeValue];
      selectedTypes = newSelectedTypes;

      if (onTypeAdd) {
        onTypeAdd(typeValue);
      }
      if (onSelectionChange) {
        onSelectionChange(newSelectedTypes);
      }
    }
  }

  // 移除选中的类型
  function handleRemove(typeValue: MarketEventType) {
    const newSelectedTypes = selectedTypes.filter((type) => type !== typeValue);
    selectedTypes = newSelectedTypes;

    if (onTypeRemove) {
      onTypeRemove(typeValue);
    }
    if (onSelectionChange) {
      onSelectionChange(newSelectedTypes);
    }
  }

  // 切换下拉列表
  function toggleDropdown() {
    if (disabled) return;
    isOpen = !isOpen;
  }

  // 清空所有选择
  function clearAll() {
    selectedTypes = [];
    isOpen = false;

    if (onSelectionChange) {
      onSelectionChange([]);
    }
  }

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      isOpen = false;
    }
  }

  // 处理外部点击
  function handleClickOutside(event: MouseEvent) {
    if (!containerRef || !isOpen) return;

    const target = event.target as Node;
    if (!containerRef.contains(target)) {
      isOpen = false;
    }
  }

  // 组件挂载时添加事件监听器
  onMount(() => {
    const handleClick = (event: MouseEvent) => handleClickOutside(event);

    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('click', handleClick);
    };
  });
</script>

<div bind:this={containerRef} class="relative {className}">
  <!-- 选择框容器 -->
  <div
    class="border-input bg-background ring-offset-background focus-within:ring-ring min-h-[2.25rem] w-full rounded-md border px-3 py-2 text-sm focus-within:ring-2 focus-within:ring-offset-2 {disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}"
    onclick={toggleDropdown}
    onkeydown={handleKeydown}
    role="button"
    tabindex="0"
  >
    <div class="flex items-center justify-between">
      <!-- 选中的类型标签区域 -->
      <div class="flex items-center gap-1 flex-1 min-w-0">
        {#if selectedTypes.length === 0}
          <span class="text-muted-foreground">{placeholder}</span>
        {:else}
          <!-- 显示可见的类型标签 -->
          {#each selectedTypes.slice(0, visibleBadgeCount) as type}
            <Badge variant="secondary" class="flex h-6 items-center gap-1 px-2 py-0 text-xs flex-shrink-0" title={type}>
              <span>{getTypeAbbreviation(type)}</span>
              <Button
                variant="ghost"
                size="sm"
                class="ml-1 h-3 w-3 p-0 hover:bg-transparent"
                onclick={(e) => {
                  e.stopPropagation();
                  handleRemove(type);
                }}
              >
                <X class="h-2.5 w-2.5" />
              </Button>
            </Badge>
          {/each}

          <!-- 显示隐藏数量 -->
          {#if hiddenCount > 0}
            <Badge variant="outline" class="h-6 px-2 py-0 text-xs flex-shrink-0">
              +{hiddenCount}
            </Badge>
          {/if}
        {/if}
      </div>

      <!-- 右侧按钮区域 -->
      <div class="flex items-center gap-1">
        {#if selectedTypes.length > 0}
          <Button
            variant="ghost"
            size="sm"
            class="text-muted-foreground hover:text-foreground h-5 px-1 text-xs"
            onclick={(e) => {
              e.stopPropagation();
              clearAll();
            }}
          >
            清空
          </Button>
        {/if}
        <Button variant="ghost" size="sm" class="h-5 w-5 p-0">
          {#if isOpen}
            <ChevronUp class="h-3 w-3" />
          {:else}
            <ChevronDown class="h-3 w-3" />
          {/if}
        </Button>
      </div>
    </div>
  </div>

  <!-- 下拉选项列表 -->
  {#if isOpen}
    <div
      class="bg-popover text-popover-foreground absolute z-50 mt-1 w-full rounded-md border p-0 shadow-md outline-none"
    >
      <div class="max-h-60 overflow-y-auto">
        <div class="p-1">
          {#each typeOptions as option}
            <button
              class="hover:bg-accent hover:text-accent-foreground flex w-full items-center justify-between rounded-sm px-3 py-2 text-left text-sm {selectedTypes.includes(option.value) ? 'bg-accent text-accent-foreground' : ''}"
              onclick={() => handleSelect(option.value)}
            >
              <div class="flex items-center gap-2">
                <span class="font-medium">{option.label}</span>
              </div>
              {#if selectedTypes.includes(option.value)}
                <Check class="h-4 w-4 text-primary" />
              {/if}
            </button>
          {/each}
        </div>
      </div>
    </div>
  {/if}
</div>
